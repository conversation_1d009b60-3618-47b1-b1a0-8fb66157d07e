import React from 'react';
import { Statistic } from 'antd';

const data = [
  { title: '总销售额', value: 345678.98, previousValue: 345655.55, change: '-xx%' },
  { title: '广告销售', value: 345678.98, previousValue: 345655.55, change: '-xx%' },
  { title: 'ACOS', value: 345678.98, previousValue: 345655.55, change: '-xx%' },
  { title: '广告花费', value: 345678.98, previousValue: 345655.55, change: '-xx%' },
  { title: '自然销售点击', value: 345678.98, previousValue: 345655.55, change: '-xx%' },
  { title: 'TACOS', value: 345678.98, previousValue: 345655.55, change: '-xx%' },
];

const DashboardCards = () => {
  return (
    <div style={{ display: 'flex', gap: '16px' }}>
      {data.map((item, index) => (
        <ACard key={index} style={{ width: 200 }}>
          <Statistic
            title={item.title}
            value={item.value}
            precision={2}
            valueStyle={{ color: '#3f8600' }}
          />
          <Statistic
            value={item.previousValue}
            precision={2}
            valueStyle={{ color: '#cf1322' }}
            suffix={item.change}
          />
        </ACard>
      ))}
    </div>
  );
};

export default DashboardCards;