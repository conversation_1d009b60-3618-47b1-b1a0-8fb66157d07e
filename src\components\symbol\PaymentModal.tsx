import React from 'react';
import { Modal, Button } from 'antd';

interface PaymentModalProps {
  visible: boolean;
  onClose: () => void;
  jumpUrl?: string;
  redirectToUrl: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({ visible, onClose, jumpUrl, redirectToUrl }) => {
  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      centered
      maskClosable={false}
    >
      {/* 大字号 */}
     <div className='text-lg'>
     <h3 className='text-2xl'>正在支付中...</h3>
      <p className='py-6 text-gray-600'>支付完成前，请不要关闭此窗口，若付款过程中遇到问题，请随时联系客服。</p>
      {jumpUrl && (
       <div className='flex justify-end'>
         <Button type="primary" size='large' onClick={redirectToUrl}>
          我已支付
        </Button>
       </div>
      )}
     </div>
    </Modal>
  );
};

export default PaymentModal;