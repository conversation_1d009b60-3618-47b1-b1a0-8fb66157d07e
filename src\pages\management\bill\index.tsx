import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { invoiceList, invoiceDownload } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
import AddBill from './modules/AddBill';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {
    const { t } = useTranslation();

    const userInfo = useAppSelector(selectUserInfo);

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const [loading, setLoading] = useState(true);

    const nav = useNavigate();
    // 批量操作弹窗
    const [open, setOpen] = useState(false);

    const [modalVisible, setModalVisible] = useState(false);

    // 申请开票弹窗
    const addBillRef = useRef<{ showModal: () => void, hideModal: () => void }>(null);
    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: invoiceList,
            apiParams: {
                page: 1,
                pagesize: 20,
                where: {
                    "BalanceType": "INCO",
                }
            },
            immediate: false,
            columns: () => [
                {
                    key: 'ID',
                    dataIndex: 'ID',
                    title: '序号',
                    align: 'center',
                    checked: true,
                    // hidden: true,
                },
                {
                    key: 'InvoiceType',
                    dataIndex: 'InvoiceType',
                    title: '开票类型',
                    align: 'center',
                    width: 150,
                    checked: true,
                    // 数电票（普通发票） 数电票（增值税专用发票）
                    render: (type: number) => (
                        <span>{type === 0 ? '普通发票' : '增值税专用发票'}</span>
                    ),
                },

                {
                    key: 'InvoiceInfo',
                    dataIndex: 'InvoiceInfo',
                    title: '企业名称',
                    align: 'center',
                    //   width: 120,
                    checked: true,
                    render: (text: string) => {
                        const InvoiceInfo = JSON.parse(text);
                        return InvoiceInfo?.CompanyName || '-';
                    }
                },
                {
                    key: 'SumMoney',
                    dataIndex: 'SumMoney',
                    title: '开票金额',
                    align: 'center',
                    //   width: 120,
                    checked: true,
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                // {
                //     key: 'LeftBalance',
                //     dataIndex: 'LeftBalance',
                //     title: '剩余金额',
                //     align: 'center',
                //     //   width: 120,
                //     checked: true,
                //     render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                // },
                {
                    key: 'AddDatetime',
                    dataIndex: 'AddDatetime',
                    title: '申请时间',
                    align: 'center',
                    //   width: 150,
                    checked: true,
                    ellipsis: {
                        showTitle: false,
                    },
                    sorter: (a: any, b: any) => new Date(a.AddDatetime).getTime() - new Date(b.AddDatetime).getTime(),
                },
                {
                    key: 'InvoiceState',
                    dataIndex: 'InvoiceState',
                    title: '状态',
                    align: 'center',
                    //   width: 100,
                    checked: true,
                    sorter: (a: any, b: any) => a.InvoiceState - b.InvoiceState,
                    //   `InvoiceState` tinyint(1) DEFAULT '0' COMMENT '0 申请中 1 开票中 2 已经完毕 -1 拒绝失败',
                    render: (state: number, record: any) => (
                        <ATag color={state === -1 ? 'red' : state === 0 ? 'blue' : state === 1 ? 'orange' : 'green'}>
                            {/* {state === -1 ? '开票失败' : state === 0 ? '申请中' : state === 1 ? '开票中' : '开票成功'} */}
                            {state === -1 ? '开票失败' : state === 0 ? '开票中' : '开票成功'}
                        </ATag>
                    ),
                },
                // {
                //     key: 'OrderIds',
                //     dataIndex: 'OrderIds',
                //     title: '关联订单号',
                //     align: 'center',
                //     width: 150,
                //     checked: true,
                //     render: (text: string) => {
                //         const OrderIds = JSON.parse(text);
                //         // 用popover展示
                //         return <div>
                //             查看
                //         </div>
                //     }
                // },
                {
                    key: 'operate',
                    dataIndex: 'operate',
                    title: '操作',
                    align: 'center',
                    width: 180,
                    checked: true,
                    render: (text: string, record: any) => {
                        const OrderIds = JSON.parse(record.OrderIds);
                        return (
                            <div>
                                {/* 展示详情 然后根据InvoiceState为2的时候展示下载按钮 */}
                                <APopover
                                content={
                                    <ul>
                                        {OrderIds?.order_nums?.map((orderId: string, index: number) => (
                                            <li key={index}>{orderId}</li>
                                        ))}
                                    </ul>
                                }
                                title="关联订单号"
                                trigger="click"
                            >
                                {/* <span className="cursor-pointer text-primary underline">查看</span> */}
                                <AButton type="link">关联订单</AButton>
                            </APopover>

                            {record.InvoiceState === 2 && <AButton type="link" onClick={() => handleDownload(record)}>下载</AButton>}
                        </div>
                        )
                    }
                }
            ]
        },
{ showQuickJumper: true }
    );

const {
    checkedRowKeys,
    rowSelection,
    onBatchDeleted,
    onDeleted,
    handleEdit,
    handleAdd,
    drawerVisible,
    closeDrawer,
    operateType,
    editingData
} = useTableOperate(data, run);


async function handleBatchDelete() {
    // request
    console.log(checkedRowKeys);
    onBatchDeleted();
}

const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
    if (info.source === 'trigger' || nextOpen) {
        setOpen(nextOpen);
    }
};

function handleDelete(id: number) {
    // request
    console.log(id);

    onDeleted();
}

function edit(id: number) {
    handleEdit(id);
}
const getTableData = async () => {
    console.log(tableProps, "tableProps====")
    // console.log(params, "params====")
    // return
    // run(params);
    const formValues = form.getFieldsValue();
    console.log(formValues, "formValues====")
    const searchParams = {
        where: {
            ...(formValues?.InvoiceState ? { InvoiceState: `${formValues.InvoiceState}` } : {}),
        },
    };
    handleRun({
        page: 1,
        pagesize: 20,
        ...searchParams,
        CountryCode: "CN"
    });
}

// 查看详情
const handleDetail = (record: any) => {
    console.log(record, "record====")
}

// 下载发票
const handleDownload = (record: any) => {
    // console.log(record, "record====")
    window.open(`/api/user/invoice_download/${record.ID}`, '_blank');
}



// 头部操作插槽
const prefix = () => {
    return (
        <div className='flex items-center'>

            {/* 申请开票 */}
            <AButton
                // icon={}
                size="small"
                ghost
                type="primary"
                className=''
                // disabled={checkedRowKeys.length === 0}
                onClick={() => addBillRef.current?.showModal()}
            >
                <div className='flex items-center'>
                    <IconIcRoundPlus className="text-icon" /> 申请开票
                </div>
            </AButton>



        </div>
    )
}
return (
    <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
        <AddBill ref={addBillRef} onInvoiceApplied={()=>{
            reset()
        }}  />
        <ACard>

            <UserSearch
                search={getTableData}
                reset={reset}
                form={form}
                loading={tableProps.loading}
            />
        </ACard>

        <ACard
            ref={tableWrapperRef}
            bordered={false}
            extra={
                <TableHeaderOperation
                    onDelete={handleBatchDelete}
                    refresh={reset}
                    add={handleAdd}
                    loading={tableProps.loading}
                    setColumnChecks={setColumnChecks}
                    disabledDelete={checkedRowKeys.length === 0}
                    columns={columnChecks}
                    prefix={prefix()}
                />
            }
            title={
                <div>
                    发票管理
                </div>
            }
            className="flex-col-stretch sm:flex-1-hidden card-wrapper"
        >
            <ATable
                scroll={scrollConfig}
                // rowSelection={rowSelection}
                size="small"
                {...tableProps}
                dataSource={tableProps.dataSource}
            // loading={loading}
            // tableLayout="auto"
            //   dataSource={formattedData}
            />
        </ACard>
    </div>
);
}
