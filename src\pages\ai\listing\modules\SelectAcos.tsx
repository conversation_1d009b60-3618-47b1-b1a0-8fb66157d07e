import { Alert, Card, Modal, Popover } from 'antd';
import { InfoCircleOutlined, QuestionCircleOutlined, RobotOutlined, SettingOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { AuthAsinSetShopPreAcos } from '@/service/api';
import { asinOptions, getStrategyLabel, limitDecimalsP } from './until';
const SelectAcos = ({
  reset,
  shopPreAcos,
  form,
  loading
}: {
  reset: () => void;
  shopPreAcos: number | string;
  form: any;
  loading: boolean;
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedAcos, setSelectedAcos] = useState(shopPreAcos);
  const [applyToAll, setApplyToAll] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [customAcos, setCustomAcos] = useState<string>('');

  const allOptions = [
    ...asinOptions,
    {
      value: 'custom',
      label: `自定义ACOS ≤ ${getStrategyLabel(Number(shopPreAcos), asinOptions, selectedAcos) || '？%'} `
    }
  ];

  // const popoverContent = (
  //     <ul>
  //         <li>1. 设置该站点的整体预期ACOS值，应用于所有AI托管Listing。</li>
  //         <li>2. 如需个别调整，选中Listing后在批量操作中进行修改  </li>
  //         <li>3. 该站点整体预期ACOS值会作为重要参考指标，请谨慎选择</li>
  //         <li>4. 广告颜色分类系统，基于该站点整体预期ACOS值，分类显示Listing表现情况。</li>
  //         <li className='text-yellow-500'>如未选择，则默认以ACOS小于等于24进行托管</li>
  //     </ul>
  // );

  const handleConfirm = () => {
    setIsModalVisible(true);
  };

  const handleOk = async () => {
    setModalLoading(true);
    const res = await AuthAsinSetShopPreAcos({
      PreAcos: selectedAcos === 'custom' ? Number(customAcos) : selectedAcos,
      CountryCode: form.getFieldValue('country'),
      Asin: applyToAll ? 'ALL' : ''
    });
    if (res && res.data) {
      window.$message?.success('设置成功');
      handleCancel();
      //  setCustomAcos("")
      // if(selectedAcos === 'custom'){
      //     setCustomAcos("")
      // }

      reset();
    }
    setModalLoading(false);
    console.log(res, 'res====');

    console.log('确认选择');
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setAcos(shopPreAcos);
    setApplyToAll(false);
  };

  const setAcos = (value: string | number) => {
    setSelectedAcos(value);
    // shopPreAcos 值不等于 asinOptions中的任何一个则是自定义
    const isCustom = asinOptions.find((option: any) => option.value === value);
    if (typeof value === 'number' && !isCustom) {
      setSelectedAcos('custom'); // 设置为自定义选项
      setCustomAcos(String(value)); // 设置自定义ACOS值
    }
  };

  useEffect(() => {
    setAcos(shopPreAcos);
    // console.log(shopPreAcos, "shopPreAcos====");
  }, [shopPreAcos]);

  // useEffect(() => {
  //     if(!isModalVisible){
  //         setCustomAcos("")
  //     }
  // }, [isModalVisible])

  const popoverContent = (
    <div className="max-w-[400px]">
      {/* <div className="font-medium mb-2">ACOS策略说明：</div> */}
      <ul className="text-gray-600 space-y-2">
        <li className="flex gap-2">
          <span className="text-primary">1.</span>
          设置该站点的整体预期ACOS值，应用于所有AI托管Listing
        </li>
        <li className="flex gap-2">
          <span className="text-primary">2.</span>
          如需个别调整，选中Listing后在批量操作中进行修改
        </li>
        <li className="flex gap-2">
          <span className="text-primary">3.</span>
          该站点整体预期ACOS值会作为重要参考指标，请谨慎选择
        </li>
        <li className="flex gap-2">
          <span className="text-primary">4.</span>
          广告颜色分类系统，基于该站点整体预期ACOS值，分类显示Listing表现情况
        </li>
        <li className="flex gap-2">
          <span className="text-primary">5.</span>
          自定义目标Acos值必须大于等于16%，小于等于50%
        </li>
      </ul>
      <div className="mt-3 flex items-center gap-2 text-yellow-500">
        <InfoCircleOutlined />
        <span>如未选择，则默认以ACOS小于等于24%进行托管</span>
      </div>
    </div>
  );

  return (
    <div className="mr-2 flex items-center">
      <div className="flex items-center border border-white rounded-lg bg-white shadow-sm transition-colors hover:border-blue-400">
        <div className="flex items-center gap-1 border border-white px-3">
          {/* <RobotOutlined className="text-primary" /> */}
          <Icon
            icon="mingcute:ai-fill"
            className="text-lg text-primary"
          />
          <span className="text-gray-700 font-medium">整体预期ACOS</span>
          <Popover
            content={popoverContent}
            title={
              <div className="flex items-center gap-2">
                <SettingOutlined className="text-primary" />
                <span>ACOS策略配置说明</span>
              </div>
            }
            placement="bottom"
          >
            <QuestionCircleOutlined className="cursor-pointer text-gray-400 hover:text-primary" />
          </Popover>
        </div>
        <ASelect
          className="select-acos"
          disabled={loading}
          value={selectedAcos}
          onChange={value => {
            setSelectedAcos(value);
            handleConfirm();
          }}
          suffixIcon={<></>}
          style={{ width: 190 }}
          options={allOptions}
        />
        {selectedAcos === 'custom' && (
          <AButton
            type="link"
            disabled={loading}
            onClick={() => {
              handleConfirm();
            }}
            className="ml-1 !px-2"
          >
            <SettingOutlined className="text-blue-500" />
          </AButton>
        )}
      </div>

      <Modal
        title={
          <div className="flex items-center gap-2">
            <RobotOutlined className="text-lg text-primary" />
            <span>AI智能托管 - 整体ACOS目标设置</span>
          </div>
        }
        open={isModalVisible}
        onOk={handleOk}
        maskClosable={false}
        onCancel={handleCancel}
        okText="确认"
        confirmLoading={modalLoading}
        cancelText="取消"
        width={680}
        className="acos-setting-modal"
      >
        <div className="space-y-4">
          <Card className="border border-blue-100 bg-gray-50">
            {/* <div className="flex items-center gap-3 mb-4">
                            <SettingOutlined className="text-lg text-primary" />
                            <span className="font-medium text-base">ACOS目标配置</span>
                        </div> */}

            <div className="mb-4 flex items-center gap-2">
              <span className="whitespace-nowrap">整体预期ACOS值：</span>
              {selectedAcos === 'custom' ? (
                <div className="flex items-center gap-2">
                  <AInputNumber
                    formatter={limitDecimalsP}
                    parser={limitDecimalsP}
                    autoComplete="off"
                    step={1}
                    min={16}
                    max={50}
                    style={{ width: 180 }}
                    addonBefore="ACOS ≤"
                    addonAfter="%"
                    value={customAcos}
                    onChange={e => setCustomAcos(e)}
                  />
                  <span className="text-primary font-medium">
                    {getStrategyLabel(Number(customAcos), asinOptions, selectedAcos, false)}
                  </span>
                </div>
              ) : (
                <span className="text-primary font-medium">
                  {asinOptions.find((option: any) => option.value === selectedAcos)?.label}
                </span>
              )}
            </div>

            <div className="border border-gray-100 rounded-lg bg-white p-4">
              <div className="mb-2 flex items-center justify-between">
                <span className="font-medium">应用于所有Listing</span>
                <ASwitch
                  checked={applyToAll}
                  onChange={setApplyToAll}
                  className="bg-gray-200"
                />
              </div>
              <p className="text-sm text-gray-500">
                {applyToAll
                  ? '关闭后仅应用于未设置单独ACOS值的Listing'
                  : '开启后，预期Acos值将应用于站点所有AI托管Listing，单独设置Acos值的Listing将同步更改'}
              </p>
            </div>
          </Card>

          <Alert
            message={
              <div className="text-gray-600">
                <div className="mb-2 font-medium">重要提示：</div>
                <ul className="list-disc pl-4 space-y-1">
                  <li>Listing的Acos目标值将作为AI托管的参考指标，请勿频繁修改动</li>
                </ul>
              </div>
            }
            type="info"
            // showIcon
          />

          {selectedAcos === 'custom' && (
            <div className="flex items-center gap-2 rounded-lg bg-blue-50 px-4 py-3">
              <InfoCircleOutlined className="text-primary" />
              <span className="text-blue-600">自定义预期ACOS值范围：16% ~ 50%</span>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default SelectAcos;
