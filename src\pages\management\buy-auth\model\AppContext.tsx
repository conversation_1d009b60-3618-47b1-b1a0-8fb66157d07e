import React, { createContext, useState, ReactNode } from 'react';
import { useSearchParams } from 'react-router-dom';
interface AppContextProps {
  selectedPackage: object | null;
  setSelectedPackage: (id: object | null) => void;
  selectedCoupon: object | null;
  setSelectedCoupon: (id: object | null) => void;
  showPayModal: boolean;
  setShowPayModal: (value: boolean) => void;
  parseFormattedData: () => Record<string, string>;
}

export const AppContext = createContext<AppContextProps | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedPackage, setSelectedPackage] = useState<object | null>(null);
  const [selectedCoupon, setSelectedCoupon] = useState<object | null>(null);
  const [showPayModal, setShowPayModal] = useState(false);
  const [searchParams] = useSearchParams();
  const dataParam = searchParams.get("data");

  const parseFormattedData = () => {
    if(dataParam){
      const entries = dataParam.split(', ').map(entry => {
        const [id, countries] = entry.split(': ');
        return [id.replace(/"/g, ''), countries.replace(/"/g, '')];
      });
      return Object.fromEntries(entries);
    }else{
      return {};
    }
  };
  return (
    <AppContext.Provider value={{ selectedPackage, setSelectedPackage, selectedCoupon, setSelectedCoupon, showPayModal, setShowPayModal, parseFormattedData }}>
      {children}
    </AppContext.Provider>
  );
};