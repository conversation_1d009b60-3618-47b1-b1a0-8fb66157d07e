import { Button, Dropdown, Tag, Typography, Select } from 'antd';
import type { MenuProps } from 'antd';
import { useSubmit } from 'react-router-dom';
import { useRoute } from '@sa/simple-router';
import {
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { localStg } from '@/utils/storage';
import { selectToken, selectUserInfo } from '@/store/slice/auth';
import { fetchLogout, switchUser } from '@/service/api/auth';
import { getUerName, login, resetStore } from '@/store/slice/auth';
import { store } from '@/store';
import { useLogin } from '@/hooks/common/login';
const UserAvatar = memo(() => {
  const { Text } = Typography;
  const token = useAppSelector(selectToken);
  const { t } = useTranslation();
  const userInfo = useAppSelector(selectUserInfo);
  const submit = useSubmit();
  const route = useRoute();
  const router = useRouterPush();
  const [isShowName, setIsShowName] = useState(false)
  const [localUserInfo, setLocalUserInfo] = useState(userInfo);
  const dispatch = useAppDispatch();
  const { toGroupLogin } = useLogin();
  function logout() {
    window?.$modal?.confirm({
      title: t('common.tip'),
      content: t('common.logoutConfirm'),
      okText: t('common.confirm'),
      cancelText: t('common.cancel'),
      onOk: () => {
        let needRedirect = false;
        if (!route.meta?.constant) needRedirect = true;
        // console.log(route.fullPath);
        fetchLogout().then(res => {
          dispatch(resetStore())
          submit({ redirectFullPath: route.fullPath, needRedirect }, { method: 'post', action: '/logout' });

          console.log(res);
        });
        // return;
        // 
      }
    });
  }

  function onClick({ key }: { key: string }) {
    if (key === '1') {
      logout();
    } else {
      router.routerPushByKey('management_user');
    }
  }
  function loginOrRegister() {
    router.routerPushByKey('login');
  }

  const items: MenuProps['items'] = [
    {
      label: (
        <div className="flex-center gap-8px">
          <SvgIcon
            icon="ph:user-circle"
            className="text-icon"
          />
          {t('common.userCenter')}
        </div>
      ),
      key: '0'
    },
    {
      type: 'divider'
    },
    {
      label: (
        <div className="flex-center gap-8px">
          <SvgIcon
            icon="ph:sign-out"
            className="text-icon"
          />
          {t('common.logout')}
        </div>
      ),
      key: '1'
    }
  ];

  // checkShop
  async function checkShop(id: string) {
    console.log(id);
    const res = await switchUser({ SwitchUserID: Number(id) })
    if (res && res.data) {
      window.$message?.success("切换成功")

      toGroupLogin(false)
    }
  }
  const maskSensitiveInfo = (info: string) => {
    if (!info) return "";
    // 如果只有2位，则全部隐藏
    if (info.length <= 2) {
      return "*".repeat(info.length);
    }
    return info.replace(/.(?=.{2})/g, "*");
  };


  // 转换 group_shop 对象为数组
  const shopOptions = useMemo(() => {
    if (!localUserInfo?.group_shop) return [];

    return Object.entries(localUserInfo.group_shop).map(([key, shop]) => ({
      label: (
        <div className='flex items-center'>
          <span className="font-500">{shop.ShopName}</span>
          {shop.IsDistributor && (
            <span className="ml-1 inline-flex items-center justify-center px-1.5 h-5 text-xs font-medium bg-primary text-white rounded">
              VC
            </span>
          )}
        </div>
      ),
      value: String(shop.ID)
    }));
  }, [localUserInfo?.group_shop, isShowName]);



  // 处理店铺切换
  const handleShopChange = (shopId: number) => {
    console.log(shopId, 'shopId');

    checkShop(shopId.toString());
  };






  useEffect(() => {
    // 每次组件渲染时，从本地存储中获取最新的 userInfo
    const storedUserInfo = localStg.get('userInfo');
    // 判断
    if (storedUserInfo && JSON.stringify(storedUserInfo) !== JSON.stringify(localUserInfo)) {
      setLocalUserInfo(storedUserInfo);
      // window.location.reload();
    }
  }, [userInfo, localStg.get('userInfo')]);

  return localUserInfo ? (
    <div className="flex items-center">
      {/* 切换店铺 */}
      {Object.keys(localUserInfo?.group_shop || {}).length > 0 && (
        <Select
          className="mx-2"
          style={{ width: 220 }}
          placeholder={t('page.login.common.switchshop')}
          key={localUserInfo.active_shop_id}
          options={shopOptions}
          onChange={handleShopChange}
          defaultValue={localUserInfo.active_shop_id}
          optionLabelProp="label"
        // label="店铺"
        />
      )}


      {/* 用户信息 */}
      <Dropdown
        placement="bottomRight"
        trigger={['click']}
        menu={{ items, onClick }}
      >
        <div>
          <ButtonIcon className="px-12px">
            {/* <SvgIcon
              icon="ph:user-circle"
              className="text-icon-large"
            /> */}


            <div className="flex flex-col items-baseline">

              <span className="text-16px font-medium mb-1">
                {isShowName ? localUserInfo?.NickName : maskSensitiveInfo(localUserInfo?.NickName)}
                {localUserInfo?.CompanyName && <Tag className="ml-1" style={{
                  color: '#DAA520',
                  borderColor: '#DAA520',
                  background: 'transparent'
                }}>
                  {isShowName ? localUserInfo?.CompanyName : maskSensitiveInfo(localUserInfo?.CompanyName)}
                </Tag>
                }
              </span>
              <span className="text-gray text-14px">
                {isShowName ? localUserInfo.Email : maskSensitiveInfo(localUserInfo.Email)}
              </span>
            </div>
          </ButtonIcon>
        </div>
      </Dropdown>
      {/* 显示/隐藏用户名 */}
      <p className="cursor-pointer" onClick={() => {
        setIsShowName(!isShowName)
      }}>
        {
          isShowName ?
            <EyeOutlined />

            :
            <EyeInvisibleOutlined />
        }
      </p>
      {/* 切换店铺 */}
      {/* {localUserInfo?.group_shop?.length > 0 && (
        <Dropdown
          trigger={['click']}
          placement="bottomRight"
          menu={{
            items: localUserInfo?.group_shop?.map((item: any) => ({
              label: (
                <div>
                  <p className="font-bold">{isShowName ? item.UserName : maskSensitiveInfo(item.UserName)}</p>
                  <p>{isShowName ? item.Email : maskSensitiveInfo(item.Email)}</p>
                </div>
              ),
              key: item.ID,
              onClick: () => checkShop(item.ID),
            })) || []
          }}
        >
          <Button type="link" className="ml-2">
            <Text type="success" className="cursor-pointer">切换店铺</Text>
         </Button>
        </Dropdown>
      )} */}
    </div>
  ) : (
    <Button onClick={loginOrRegister}>{t('page.login.common.loginOrRegister')}</Button>
  );
});

export default UserAvatar;
