import { Suspense, lazy } from 'react';
import { <PERSON><PERSON><PERSON>, But<PERSON> } from 'antd';
import { RadarChartOutlined } from '@ant-design/icons';
import { DownOutlined, QuestionCircleOutlined, WarningOutlined } from '@ant-design/icons';
const TableHeaderOperation = lazy(() => import('@/components/advanced/TableHeaderOperation'));
const UserSearch = lazy(() => import('./modules/UserSearch'));
const LocalSearch = lazy(() => import('./modules/LocalSearch'));
import { getListingSumData, AuthAsinAdd, AuthAsinList } from '@/service/api';
const ListingDashboard = lazy(() => import('./modules/ListingDashboard'))
const NewAntdModalComponent = lazy(() => import('./modules/NewModalComponent'))
import BigNumber from "bignumber.js";
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {

    const { tableWrapperRef, scrollConfig } = useTableScroll();
    // 引入国际化
    const { t } = useTranslation();

    const [authLoading, setAuthLoading] = useState(true)

    const [isShowContrast, setisShowContrast] = useState(true)

    const [searchParams, setSearchParams] = useState({})


    const [tableData, setTableData] = useState<any[]>([]);

    // 原始数据
    const [originalData, setOriginalData] = useState<any[]>([]);

    // 库存最小限制
    const [asinLimitStore, setAsinLimitStore] = useState(0);
    const nav = useNavigate();
    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: getListingSumData,
            apiParams: {
            },
            immediate: false,
            columns: () => [
                {
                    key: 'Asin',
                    dataIndex: 'Asin',
                    title: () => {
                        return (
                            <div className='flex items-center justify-center text-[12px]'>
                                {t('page.listingall.column.parentAsin')}
                                <span className='text-gray-700 text-[12px]'>（{t('page.listingall.column.parentAsinExtra')}）</span>
                            </div>
                        )
                    },
                    // '父ASIN（有库存或有销售额）',
                    align: 'center',
                    width: 350,
                    checked: true,
                    render: (_, record) => {
                        // console.log(record,Number(record.HaveNum) == 0 || Number(record.HaveNum) < 50, "record")
                        return (
                            <div className='flex items-stretch h-full gap-2 cursor-pointer'>
                                   <LazyImage
                                        src={record.first_image}
                                        alt={record.item_name || "--"}
                                        className='max-h-[100px] object-contain min-w-[80px]'
                                        width={100}
                                    />
                                <div onClick={(event) => {
                                    // 检查是否有文本被选中
                                    const selection = window.getSelection();
                                    if (selection && selection.toString().length > 0) {
                                        return; // 如果有文本被选中，则不执行跳转
                                    }
                                    document.querySelectorAll('.ant-tooltip').forEach(element => {
                                        element.style.display = 'none';
                                    });
                                    nav(`/ai/children-listingall?market=${form.getFieldValue('country')}&asin=${record.parent_asin}&isAuth=${record.IsAuth || 0}&haveNum=${record.HaveNum || 0}`, '_blank');
                                }} className='flex flex-col justify-between w-full'>
                                    <Tooltip title={t('page.listingall.tooltip.clickAsinDetails')} placement='topLeft'>
                                        <div className='line-clamp-2 text-left'>
                                            {record.item_name || "--"}
                                        </div>
                                    </Tooltip>
                                    <div className='text-start flex flex-nowrap'>
                                        <span className='text-gray-400 mr-1'>{t('page.listingall.column.price')}:</span>
                                        {record.min_price === record.max_price ?

                                            <CurrencySymbol countryCode={form.getFieldValue('country')} value={record.min_price} />
                                            :
                                            <>
                                                <CurrencySymbol countryCode={form.getFieldValue('country')} value={record.min_price} />~<CurrencySymbol countryCode={form.getFieldValue('country')} value={record.max_price} />
                                            </>
                                        }
                                        <span className='mx-1'> | </span>
                                        <div className='flex items-center'>
                                            <span className='text-gray-400 mr-1'>{t('page.listingall.column.inventory')}:</span>
                                            {record.HaveNum}

                                            {/* 当库存为0 或者没有时 或者小于50 显示一个红色的⚠️图标 */}
                                            {(Number(record?.HaveNum) === 0 || Number(record?.HaveNum) < asinLimitStore) && (
                                                <Tooltip title={t('page.listingall.tooltip.inventoryLessThan', { limit: asinLimitStore })}>
                                                    <WarningOutlined className='text-red-500 ml-1' />
                                                </Tooltip>
                                            )}
                                        </div>

                                    </div>
                                    <div className='text-start'>
                                        父ASIN:
                                        <a
                                            className='text-primary'
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            onClick={(event) => {
                                                event.stopPropagation();
                                                if (record.parent_asin && record.parent_asin !== '-') {
                                                    window.open(`https://www.${getSalesChannel(form.getFieldValue('country'))}/dp/${record.parent_asin}`, '_blank');
                                                }
                                            }}
                                        >
                                            {record.parent_asin || "-"}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        );
                    }
                },
                {
                    key: '总销售额',
                    title: t('page.listingall.column.totalSales'),
                    align: 'center',
                    dataIndex: '总销售额',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.总销售额 - b.总销售额;
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={form.getFieldValue('country')} value={text} oldValue={record.old_总销售额} isShowContrast={isShowContrast} />;
                    }
                },
                {
                    key: '广告总销售额',
                    dataIndex: '广告总销售额',
                    title: t('page.listingall.column.adTotalSales'),
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.广告总销售额 - b.广告总销售额;
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={form.getFieldValue('country')} value={text} oldValue={record.old_广告总销售额} isShowContrast={isShowContrast} />;
                    }
                },
                {
                    key: 'ACOS',
                    dataIndex: 'ACOS',
                    title: "ACOS",
                    align: 'center',
                    width: 80,
                    sorter: (a: any, b: any) => {
                        console.log(a.ACOS, b.ACOS, "a.ACOS, b.ACOS")
                        return a.ACOS.replace("%", "") - b.ACOS.replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.广告总花费 || 0);
                        const sales = new BigNumber(record.广告总销售额 || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        // 计算old
                        const oldAcos = new BigNumber(record.old_广告总花费 || 0).dividedBy(new BigNumber(record.old_广告总销售额 || 0)).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol value={acos} oldValue={oldAcos} isShowContrast={isShowContrast} reverseColors={true} />;
                    }
                },
                {
                    key: '自然销售比例',
                    dataIndex: '自然销售比例',
                    title: t('page.listingall.column.naturalSalesRatio'),
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.自然销售比例.replace("%", "") - b.自然销售比例.replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol value={text || '0%'} oldValue={record.old_自然销售比例 || '0%'} isShowContrast={isShowContrast} />;
                    }
                },
                {
                    key: 'TACOS',
                    dataIndex: 'TACOS',
                    title: t('page.listingall.column.tacos'),
                    align: 'center',
                    width: 90,
                    sorter: (a: any, b: any) => {
                        return a.TACOS.replace("%", "") - b.TACOS.replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.广告总花费 || 0);
                        const sales = new BigNumber(record.总销售额 || 0);
                        const tacos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        const oldTacos = new BigNumber(record.old_广告总花费 || 0).dividedBy(new BigNumber(record.old_总销售额 || 0)).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol value={tacos} oldValue={oldTacos} isShowContrast={isShowContrast} reverseColors={true} />;
                    }
                },
                {
                    key: "SP广告销售额占比",
                    dataIndex: "SP广告销售额占比",
                    title: t('page.listingall.column.spAdSalesRatio'),
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a["SP广告销售额占比"].replace("%", "") - b["SP广告销售额占比"].replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol value={text || '0%'} oldValue={record.old_SP广告销售额占比 || '0%'} isShowContrast={isShowContrast} />;
                    }
                },
                {
                    key: '广告总花费',
                    dataIndex: '广告总花费',
                    title: t('page.listingall.column.adTotalCost'),
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.广告总花费 - b.广告总花费;
                    },
                    checked: false,
                    render: (_, record) => {
                        // 保留整数 
                        return <CurrencySymbol isInteger={true} value={record.广告总花费} oldValue={record.old_广告总花费} isShowContrast={isShowContrast} reverseColors={true} />;
                    }
                },
                {
                    key: '总订单量',
                    dataIndex: '总订单量',
                    title: t('page.listingall.column.totalOrders'),
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.总订单量 - b.总订单量;
                    },
                    checked: false,
                    render: (_, record) => {
                        // 保留整数 
                        return <CurrencySymbol isInteger={true} value={parseInt(record.总订单量 || 0).toFixed(0)} oldValue={parseInt(record.old_总订单量 || 0).toFixed(0)} isShowContrast={isShowContrast} />;
                    }
                },
                {
                    key: 'IsAuth',
                    dataIndex: 'IsAuth',
                    title: t('page.listingall.column.aiHost'),
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return b.IsAuth - a.IsAuth;
                    },
                    checked: true,
                    render: (_, record) => {
                        return <ATag color={record.IsAuth === true ? 'blue' : 'default'}>{record.IsAuth === true ? t('page.listingall.search.hosted') : t('page.listingall.search.unhosted')}</ATag>;
                    }
                }
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);

    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }


    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }

    // 批量托管
    async function handleBatchAuthAsin() {
        // console.log(checkedRowKeys, "checkedRowKeys")
        // checkedRowKeys存储的为ID 根据ID获取parent_asin
        const asinData = checkedRowKeys.map((item: any) => {
            const dataItem = tableData.find((dataItem: any) => dataItem.ID === item);
            return {
                Asin: dataItem.parent_asin,
                IsAuth: dataItem.IsAuth

            }
        })
        // console.log(asinData, "asinData")
        // return
        // 检查是否有已托管的Listing
        const hasManagedListing = asinData.some(data => data.IsAuth === true);

        if (hasManagedListing) {
            window.$message?.warning(t('page.listingall.message.existingHosted'));
            return; // 终止操作
        }
        setAuthLoading(true)
        const res = await AuthAsinAdd({
            AsinData: asinData,
            CountryCode: form.getFieldValue('country')
        })
        console.log(res, "res")
        if (res && res.data) {
            console.log(res.data, "res.data")
            window.$message?.success(t('page.listingall.message.hostSuccess'));
            handleBatchDelete()
        }else{
           setAuthLoading(false)
        }
    }

    // 本地搜索
    const handleSearch = (value: string) => {
        console.log(value, "value")
    }
    // 头部操作插槽
    const prefix = () => {
        return (
            <div className='flex items-center'>

                {/* 添加授权ASIN */}
                <Button
                    icon={<IconIcRoundPlus className="text-icon" />}
                    size="small"
                    ghost
                    type="primary"
                    className=''
                    disabled={checkedRowKeys.length === 0}
                    onClick={handleBatchAuthAsin}
                >
                    {t('page.listingall.button.batchHost')}
                </Button>

            </div>
        )
    }

    const getTableData = async (params: any = {}) => {
        // console.log(params,rowSelection, "params")

        setAuthLoading(true)
        if (params.localSearch) {
            let newData = originalData;

            if (params.Asin || params.Asin == '') {
                // 模糊搜索
                params.Asin = params.Asin.replace(/ /g, '');
                newData = newData.filter((item: any) => {
                    return item.parent_asin.includes(params.Asin);
                });

            }
            if (params.IsAuth) {
                // 还需要判断params.IsAuth是否为undefined 如果是 则查询所有
                if (params.IsAuth == 'undefined') {
                    newData = newData
                } else {
                    // console.log(newData.length,newData, "筛选前")
                    const IsAuth = params.IsAuth === "1" ? true : false;
                    // 本地筛选
                    newData = newData.filter((item: any) => {
                        return item.IsAuth === IsAuth;
                    });
                    // console.log(newData.length, "筛选后")
                }

            }
            setTableData(newData)
            setAuthLoading(false)
            // 本地筛选
            // const newData = tableData.filter((item: any) => {
            //     return item.parent_asin === params.Asin
            // })
            // setTableData(newData)
        } else {
            if (Object.keys(params).length < 4) {
                setAuthLoading(false)
                return
            }
            console.log(params, "params====")
            // params.ClearCache = 1
            setSearchParams(params)
            run(params);
            // setAuthLoading(false)
        }

    }


    const formattedData = () => {
        // setAuthLoading(true)
        if (tableProps.dataSource.length === 0) {
            if (!tableProps.loading) {
                setAuthLoading(false)
            }
            return []
        }
        const _new = tableProps.dataSource
        let newData: any[] = []
        AuthAsinList({
            CountryCode: form.getFieldValue('country')
            // OnlyAuthed: true
        }).then((res:any) => {
            if (res && res.data && res.data.AsinAuthList) {
                // console.log(res.data, "res.data")
                _new.forEach((item: any) => {
                    const asinData = res.data.AsinAuthList[item.parent_asin] || "";
                    // console.log(asinData, "asinData")
                    // console.log(asinData['IsAuth'], "res.data[item.parent_asin]");
                    if (asinData) {
                        item['IsAuth'] = asinData['IsAuth'];
                    } else {
                        console.warn(`No data found for parent_asin: ${item.parent_asin}`);
                        item['IsAuth'] = false; // 或者根据需要设置一个默认值
                    }
                })
                newData = JSON.parse(JSON.stringify(_new))
                // console.log(_new, "new")
                // 筛除 _new中总销售额 或者HaveNum为0的
                // newData = newData.sort((a: any, b: any) => {
                //     return b.IsAuth - a.IsAuth
                // }).filter((item: any) => {
                //     // console.log(item, "item")
                //     return parseInt(item.总销售额 || 0) > 0 || parseInt(item.HaveNum || 0) > 0
                // })

                // console.log(newData, "new===========")

                setOriginalData(newData);
                setTableData(newData);
                setAuthLoading(false)
                setAsinLimitStore(res.data.AsinLimitStore)
            }

        }).catch(() => {
            setAuthLoading(false)
        })
        // console.log(authList, "authList")
        console.log(newData, "newData")
        return newData
    }



    useEffect(() => {
        setAuthLoading(true)
        // console.log(tableProps.loading, "tableProps.loading")
        if (tableProps.dataSource.length >= 0) {

            // console.log(tableProps.dataSource, "tableProps.dataSource")
            const newData = formattedData();
            setOriginalData(newData);
            // 筛除
            // 使用 setTableData 更新表格数据
            setTableData(newData);
        }
        // else{
        //     if (!tableProps.loading) {
        //         console.log(tableProps.loading, "tableProps.loading")
        //         setAuthLoading(tableProps.loading)
        //     }
        // }


    }, [tableProps.dataSource])
    // 监听tableProps.loading
    useEffect(() => {
        if (tableProps.loading) {
            setAuthLoading(true)
        }
    }, [tableProps.loading])

    // 监听滚动
    const [container, setContainer] = useState<HTMLDivElement | null>(null);
    // offsetTop
    const [offsetTop, setOffsetTop] = useState(1)
    return (


        <div className="min-h-500px  max-h-2000px w-full relative" ref={setContainer}>

            {/* 搜索 */}
            <div className=" sticky top-0 z-10 w-full">
                <ACard className='flex items-center justify-between'>
                    <UserSearch
                        search={getTableData}
                        reset={reset}
                        form={form}
                        loading={authLoading}
                    // suffix={
                    //     <Button
                    //         icon={<RadarChartOutlined className="text-icon" />}
                    //         size="small"
                    //         ghost
                    //         type="primary"
                    //         className='mr-2'
                    //         onClick={handleAdd}
                    //     >
                    //         AI广告诊断
                    //     </Button>
                    // }

                    />
                </ACard>
            </div>
            <p className='text-gray-500 pl-2 py-2 pt-2'>{t('page.listingall.notice.adStatsNoSb')}</p>


            {/* ListingDashboard */}
            <ListingDashboard params={searchParams} authLoading={authLoading} />
            <p className='text-gray-500 pl-2 py-2'>{`${t('page.listingall.notice.recentDataBias')} ${t('page.listingall.notice.minimumUnit')}`}</p>
            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={() => {
                            getTableData({})
                        }}
                        prefix={prefix()}
                        add={handleAdd}
                        loading={authLoading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <>
                        {/* 本地搜索 */}
                        <LocalSearch authLoading={authLoading} search={getTableData} />
                    </>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={{
                        y: 700,
                        x: 702
                    }} 
                    // rowSelection={rowSelection}
                    rowSelection={{
                        ...rowSelection,
                        getCheckboxProps: (record: any) => ({
                            disabled: Number(record.HaveNum || 0) < asinLimitStore, // Disable checkbox if stock is less than 50
                        }),
                    }}
                    size="small"

                    {...tableProps}
                    loading={authLoading}
                    dataSource={tableData}
                    pagination={{
                        ...tableProps.pagination,
                        total: tableData.length,
                        showTotal: (total, range) => t('page.listingall.pagination.total', { total }),
                    }}
                    locale={{
                        emptyText: <AEmpty
                            image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                            description={
                                !tableProps.loading && form.getFieldValue('country') ? (
                                    <div className='flex-col items-center'>
                                        <AButton type='primary' className='my-2' onClick={reset}>
                                            {t('page.listingall.button.refreshPage')}
                                        </AButton>
                                    </div>
                                ) : null
                            }></AEmpty>
                    }}
                // tableLayout="auto"
                // dataSource={formattedData()}
                />
                <Suspense>
                    <NewAntdModalComponent
                        visible={drawerVisible}
                        closeDrawer={closeDrawer}
                        form={form}
                        tableloading={authLoading}
                        onRefresh={() => {
                        }}
                    />
                </Suspense>
            </ACard>
        </div>
    );
}
