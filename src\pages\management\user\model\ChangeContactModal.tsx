import React, { useState, useImperativeHandle, forwardRef, useRef, useEffect } from 'react';
import { Modal, Form, Input, Button } from 'antd';
import CaptchaQRCode from '@/pages/login/captch-qr-code';
import { useCaptcha } from '@/hooks/business/captcha';
import { useFormRules } from '@/hooks/common/form';

interface ChangeContactModalProps {
  onSubmit: (values: { contact: string; captchaValue: string; code: string }) => void;
  type: 'phone' | 'email';
  initialValues: { phone?: string; email?: string };
}

const ChangeContactModal = forwardRef(({ onSubmit, type, initialValues }: ChangeContactModalProps, ref) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const captchaRef = useRef<any>(null);
  const { label, isCounting, loading, getCaptcha } = useCaptcha();
  const { formRules } = useFormRules();
  const [captchaKey, setCaptchaKey] = useState('');

  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => {
      setVisible(false);
    },
  }));

  useEffect(() => {
    if (visible) {
      if(type === 'phone'){
        form.setFieldsValue({phone:initialValues.phone})
      }else{
        form.setFieldsValue({phone:initialValues.email})
      }
      captchaRef.current.refreshCaptcha();
    }
  }, [visible, type, initialValues]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('Validation Failed:', error);
    }
  };

  const getCaptchaCode = () => {
    form.validateFields(['phone', 'captchaValue']).then(() => {
      const params = form.getFieldsValue();
      getCaptcha(form, { ...params, captchaKey }, (data: any) => {
        captchaRef.current.refreshCaptcha();
      });
    });
  };

  const title = type === 'phone' ? '手机号' : '邮箱';
  const placeholder = type === 'phone' ? '请输入手机号' : '请输入邮箱';

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={() => setVisible(false)}
      footer={[
        <Button key="back" onClick={() => setVisible(false)}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          确认
        </Button>,
      ]}
      centered
      maskClosable={false}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="phone"
          label={title}
          rules={type === 'phone' ? formRules.phone : formRules.email}
        >
          <Input placeholder={placeholder} />
        </Form.Item>
        <CaptchaQRCode
          ref={captchaRef}
          label={true}
          onCaptchaChange={(value, key) => {
            setCaptchaKey(key);
          }}
        />
        <Form.Item
          name="code"
          label="验证码"
          rules={formRules.phoneCode}
        >
          <div className="w-full flex-y-center gap-16px">
            <Input placeholder="请输入验证码" />
            <Button
              disabled={isCounting}
              loading={loading}
              onClick={getCaptchaCode}
            >
              {label}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default ChangeContactModal;