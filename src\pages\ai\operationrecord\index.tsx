import { Suspense, lazy, useRef, useState } from 'react';
import { Tag, Form, Button } from 'antd';
import { getCountryAiLog } from '@/service/api';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import { useTable, useTableOperate, useTableScroll } from '@/hooks/common/table';
import { useNavigate } from 'react-router-dom';
import { LeftOutlined, RightOutlined } from '@ant-design/icons'; // 添加这行导入

const UserSearch = lazy(() => import('./modules/UserSearch'));

export function Component() {
  const { t } = useTranslation();
  const { tableWrapperRef, scrollConfig } = useTableScroll();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedDays, setSelectedDays] = useState<any>('');
  const [selectedContinentCode, setSelectedContinentCode] = useState('');
  const nav = useNavigate();
  const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable({
    apiFn: getCountryAiLog,
    apiParams: {
      page: currentPage,
      pagesize: 100
    },
    immediate: false,
    columns: () => [
      { key: 'ID', title: 'ID', dataIndex: 'ID', align: 'center' },
      {
        key: 'market',
        title: t('page.aidrivenads.columns.market'),
        dataIndex: 'market',
        align: 'center',
        checked: true
      },
      { key: 'date', title: t('page.aidrivenads.columns.date'), dataIndex: 'date', align: 'center', checked: true },
      {
        key: 'changeSite',
        title: t('page.aidrivenads.columns.changeSite'),
        dataIndex: 'changeSite',
        align: 'center',
        checked: true,
        render: (text, record) => <Tag color="blue">{record.changeSite}</Tag>
      },
      {
        key: 'campaignName',
        title: t('page.aidrivenads.columns.campaignName'),
        dataIndex: 'campaignName',
        align: 'center',
        checked: true
      },
      {
        key: 'content',
        title: t('page.aidrivenads.columns.content'),
        dataIndex: 'content',
        align: 'center',
        checked: true
      },
      {
        key: 'operate',
        title: t('page.aidrivenads.columns.operate'),
        dataIndex: 'operate',
        align: 'center',
        checked: true,
        render: (text, record) => {
            const operate = parseInt(record.operate);
            if (typeof operate === 'number' && !isNaN(operate)) {
              return record.operate > 0
                ? <Tag shape='circle' color="green">{record.operate}</Tag>
                : <Tag shape='circle' color="yellow">{record.operate}</Tag>;
            } else if (record.operate === '关闭') {
              return <Tag shape='circle' color="red">{record.operate}</Tag>;
            } else if (record.operate.includes('竞价为')) {
              return <Tag shape='circle' color="blue">{record.operate}</Tag>;
            } 
              return <Tag shape='circle' color="grey">{record.operate}</Tag>;
            
          }
        }
      }
    ]
  });

  const {
    checkedRowKeys,
    rowSelection,
    onBatchDeleted,
    handleAdd,
    drawerVisible,
    closeDrawer,
    operateType,
    editingData
  } = useTableOperate(data, run);

  async function handleBatchDelete() {
    console.log(checkedRowKeys);
    onBatchDeleted();
  }

  const getTableData = (params: any) => {
    const { CountryCode, Date } = params;
    // setCurrentPage(1);
    setSelectedCountry(CountryCode);
    setSelectedDays(Date);
    console.log(currentPage, 'currentPage');
    handleRun({
      page: params?.page || currentPage,
      pagesize: 100,
      CountryCode,
      Date,
    });
  };

  const handlePageChange = (direction: 'prev' | 'next') => {
    setCurrentPage(prevPage => {
      const newPage = direction === 'prev' ? prevPage - 1 : prevPage + 1;
      if (newPage < 1) return 1;
      // 直接传入新的页码
      getTableData({
        CountryCode: selectedCountry,
        Date: selectedDays,
        page: newPage // 添加页码参数
      });
      return newPage;
    });
  };

  return (
    <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      <ACard>
        <UserSearch
          search={(params: any) => {
            setCurrentPage(1);
            getTableData(params);
          }}
          reset={() => {}}
          form={form}
          loading={tableProps.loading}
        />
      </ACard>
      <ACard
        ref={tableWrapperRef}
        bordered={false}
        extra={
          <TableHeaderOperation
            onDelete={handleBatchDelete}
            refresh={getTableData}
            add={handleAdd}
            loading={tableProps.loading}
            setColumnChecks={setColumnChecks}
            disabledDelete={checkedRowKeys.length === 0}
            columns={columnChecks}
          />
        }
        title={<div className="flex items-center cursor-default">AI操作记录（由于数据量较大，仅保留1个月数据）</div>}
        className="flex-col-stretch sm:flex-1-hidden card-wrapper"
      >
        <ATable
          scroll={scrollConfig}
          // rowSelection={rowSelection}
          size="small"
          {...tableProps}
          pagination={false} // 禁用默认分页
        />
        {tableProps.dataSource && tableProps.dataSource.length > 0 && (
          <div className="mt-4 flex items-center justify-end">
            <Button
              icon={<LeftOutlined />}
              onClick={() => handlePageChange('prev')}
              disabled={currentPage === 1 || tableProps.loading}
              type="text"
            />
            <span className="mx-2">{currentPage}</span>
            <Button
              icon={<RightOutlined />}
              onClick={() => handlePageChange('next')}
              disabled={tableProps.loading}
              type="text"
              className="ml-2"
            />
          </div>
        )}
      </ACard>
    </div>
  );
}
