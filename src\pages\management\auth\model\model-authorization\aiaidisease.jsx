import { Form, Modal, Button } from 'antd';
import { Icon } from '@iconify/react';
export default function ModelAIAIDisease({ visible, onCancel, onOk, regionData = [] }) {
    const { t } = useTranslation();
    const [options, setOptions] = useState([]);
    const handleCancel = () => {
        setOptions([]);
        onCancel()
    }
    const handleSubmit = (value) => {
        // 查找选中的选项并获取其父级的shop信息
        const selectedOption = options.find(option => option.country === value.CountryCode);
        const AreaCode = selectedOption ? selectedOption.AreaCode : null;

        // 将父级的shop信息与表单值一起传递
        const submitValue = {
            ...value,
            AreaCode
        };
        console.log(submitValue, "submitValue====");
        onOk(submitValue)
    }

    useEffect(() => {
        if (visible) {
            console.log(regionData, "regionData====");
            if (regionData.length > 0) {
                // 遍历regionData中的每一项的children，然后只需要shopauth以及advertise都等于1的数据
                const options = regionData.map(item => {
                    const children = item.children.filter(child => child.shopauth === 1 && child.advertise === 1).map(child => ({
                        ...child,
                        AreaCode: item.shop // 假设父级的shop信息在item.shop中
                    }));
                    console.log(children, "children====");
                    if (children.length > 0) {
                        return children;
                    }
                    return null; // 返回null以便后续过滤
                }).filter(item => item !== null); // 过滤掉null值
                setOptions(options.flat()); // 使用flat()将嵌套数组展平
                console.log(options, "options====");
            }
        }
    }, [regionData, visible]);
    return (
        <Modal
            title={t('sys.menu.aidisease')}
            // visible={settingStore.settings.isAuthorization === '1'}
            open={visible}
            // onOk={handleOk}
            // afterClose={handleAfterClose} //>=1.16.0
            onCancel={handleCancel}
            closeOnEsc={false}
            hasCancel={false}
            footer={<></>}
        // width={1200}
        // maskClosable={false}
        // centered={true}
        >
            <div className="flex flex-row">
                <div className="pr-10">
                    <Form
                        // layout='horizontal' 
                        // getFormApi={getFormApi}
                        labelPosition='top'
                        onSubmit={handleSubmit}
                    >
                        <Form.Select field="CountryCode"
                            style={{ width: 400 }}
                            label="选择AI诊断的国家"
                            trigger='blur'
                            placeholder={t('common.chooseText')}
                            rules={[{ required: true }]}
                            // initValue={defaultRegion}
                            // onChange={handleSelectChange}
                            // showClear
                            filter>
                            {options.map((group, index) => (
                                // <Form.Select.OptGroup className="auth-form-optgroup" label={group.label} key={`${index}-${group.label}`}>
                                //     {group.children.map((option, index2) => (
                                <Form.Select.Option value={group.country} key={`${index}-${group.country}`}>
                                    <Icon
                                        className="mr-2"
                                        icon={`circle-flags:${group.country.toLowerCase()}`}
                                        width={22}
                                        height={22}
                                    />
                                    {group.country}
                                </Form.Select.Option>
                            ))}
                        </Form.Select>


                        <div className="flex justify-end">
                            <Button theme='solid' type='primary' className="mt-4 p-4 bg-#154EC1" htmlType="submit">{t('common.okText')}</Button>
                        </div>
                        {/* </Form.InputGroup> */}
                    </Form>
                </div>
            </div>
        </Modal>
    );
}
