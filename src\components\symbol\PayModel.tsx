import { Modal, Input, Typography, Space, Button, message, Statistic, Tooltip } from 'antd';
import { useState } from 'react';
import Clipboard from 'clipboard';
import { CopyOutlined, SyncOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import type { RadioChangeEvent } from 'antd';
import { selectUserInfo } from '@/store/slice/auth';
import { rechagePackage, createRechargeOrder } from '@/service/api';
import { useLogin } from '@/hooks/common/login';
import CountUp from 'react-countup';

interface PayModelProps {
    open: boolean;
    onCancel: () => void;
    balance?: number;
}



const paymentMethods = [
    {
        value: 'ALIP',
        label: '支付宝支付',
        subLabel: 'Alipay'
    },
    // {
    //     value: 'WECH',
    //     label: '微信支付',
    //     subLabel: 'Wechat Pay'
    // },
    {
        value: 'PUBA',
        label: '对公转账',
        subLabel: 'Corporate Transfer'
    }
];


// 添加对公转账信息
const transferInfo = [
    {
        label: '公司名称',
        value: '北京步刻时光网络科技有限公司'
    },
    {
        label: '银行公户',
        value: '1109 5027 7710 202'
    },
    {
        label: '开户行',
        value: '招商银行股份有限公司北京华贸中心支行'
    }
];

export default function PayModel({ open, onCancel }: PayModelProps) {
    const { Text, Paragraph } = Typography;
    const [quickAmounts, setQuickAmounts] = useState<number[]>([]);
    const [amount, setAmount] = useState<string>('');
    const [selectedQuickAmount, setSelectedQuickAmount] = useState<string | null>(null);
    const [payMethod, setPayMethod] = useState<string>('ALIP');
    const userInfo = useAppSelector(selectUserInfo);
    const [refreshing, setRefreshing] = useState(false);
    const { toGroupLogin } = useLogin();
    const [modalVisible, setModalVisible] = useState(false);
    const [inputError, setInputError] = useState<string>('');

    const validateAmount = (value: string) => {
        const numValue = parseFloat(value);
        if (!value) {
            setInputError('充值金额不能为空');
            return false;
        }
        if (isNaN(numValue) || numValue <= 0) {
            setInputError('充值金额必须大于￥0');
            return false;
        }
        if (numValue > 10000) {
            setInputError('充值金额不能超过￥10000，大额充值请选择对公转账');
            return false;
        }
        setInputError('');
        return true;
    };

    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // if (value !== amount) {
        setAmount(value);
        setSelectedQuickAmount(null);
        // }
        // validateAmount(value);
    };

    const handleQuickAmount = (value: string) => {
        setSelectedQuickAmount(value);
        setAmount('');
        setInputError('');
    };

    const handleInputFocus = () => {
        console.log('focus');
        setSelectedQuickAmount(null);
    };

    const handleInputBlur = () => {
        console.log('blur');
        // validateAmount(amount);
    };

    const handlePayMethodChange = (e: RadioChangeEvent) => {
        setPayMethod(e.target.value);
    };

    const handleRefreshBalance = async () => {
        if (refreshing) return;
        setRefreshing(true);
        setTimeout(() => {
            try {
                toGroupLogin(false, true);
            } finally {
                setRefreshing(false);
            }
        }, 1000);
    };

    const handleSubmit = async () => {
        if (!amount && !selectedQuickAmount) {
            message.error('请输入充值金额');
            return;
        }
        setModalVisible(true);
        const res = await createRechargeOrder({
            TransBalance: amount || selectedQuickAmount,
            PayType: payMethod
        });
        if (res && res.data) {
            window.open(res.data.JumpUrl, '_blank');
        }
        console.log('充值金额:', amount || selectedQuickAmount);
        console.log('支付方式:', payMethod);
    };

    const getRechargePackage = async () => {
        const res = await rechagePackage({});
        if (res && res.data.length > 0) {
            setQuickAmounts(res.data);
            setSelectedQuickAmount(res.data[0].Fee);
        }
    };

    const isSubmitDisabled = () => {
        if (payMethod === 'PUBA') return true;
        console.log(amount, 'amount');
        if (!selectedQuickAmount && (isNaN(Number(amount)) || Number(amount) <= 0)) return true;
       
        // if  return true;
        // if (amount && !validateAmount(amount)) return true;
        return false;
    };
    // useEffect(() => {
    //     if (amount) {
    //         validateAmount(amount);
    //     }
    // }, [amount]);

    
      // 添加格式化函数
  const formatter: StatisticProps['formatter'] = (value) => (
    <CountUp
        end={value as number}
        separator=","
        decimals={2}
        duration={1} // 动画持续时间
        preserveValue
    />
);

    useEffect(() => {
        if (open) {
            getRechargePackage();
        }
    }, [open]);

    return (
        <Modal
            title="充值"
            open={open}
            onCancel={onCancel}
            width={900}
            centered
            footer={null}
            maskClosable={false}
            
        >
            <PaymentModal
                visible={modalVisible}
                onClose={() => {
                    setModalVisible(false)
                    handleRefreshBalance()
                }}
                jumpUrl={payMethod}
                redirectToUrl={() => {
                    setModalVisible(false);
                    handleRefreshBalance();
                }}
            />

            <div className="mb-6">
                <h3 className="text-base mb-4 font-500 text-lg flex items-center">
                    <SvgIcon localIcon="walletsmall" className="w-4 h-4 mr-2" />
                    账户概况
                </h3>
                <div className="bg-gray-50 p-4 rounded-md">
                    <div className="flex items-center">
                        <SvgIcon localIcon="wallet" className="w-20 h-20 mr-4" />
                        <div>
                            <div className="text-gray-500">公司账户余额
                                <Tooltip title="刷新余额">
                                    <SyncOutlined
                                        className={`ml-2 cursor-pointer hover:text-primary ${refreshing ? 'animate-spin' : ''}`}
                                        onClick={handleRefreshBalance}
                                    />
                                </Tooltip>
                            </div>
                            <Statistic
                                prefix="¥"
                                value={userInfo.SumBalance ?? 0}
                                precision={2}
                                formatter={formatter}
                                valueStyle={{
                                    color: '#e1ab51',
                                    fontSize: '26px',
                                    fontWeight: 500
                                }}
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div className="mb-6">
                <h3 className="text-base mb-4 font-500 text-lg flex items-center">
                    <SvgIcon localIcon="money" className="w-4 h-4 mr-2 text-lg" />
                    快捷充值
                </h3>
                <div className="mb-4 flex items-center">
                    <span className="mr-4 whitespace-nowrap">充值金额：</span>
                    <Space wrap size={12}>
                        {quickAmounts.map((item) => (
                            <Button
                                key={item.ID}
                                type="text"
                                onClick={() => handleQuickAmount(item.Fee)}
                                className={`h-10 min-w-[120px] border rounded-none relative ${selectedQuickAmount === item.Fee
                                        ? 'border-primary text-primary bg-primary/5'
                                        : 'border-gray-200 text-gray-600'
                                    }`}
                            >
                                ¥ {item.PackageName}
                                {selectedQuickAmount === item.Fee && (
                                    <div className="absolute -top-[1px] -left-[1px] w-8 h-8">
                                        <div className="absolute left-0 top-0 w-0 h-0 border-[13px] border-primary border-b-transparent border-r-transparent" />
                                        <Icon icon="mdi:check-bold" className="text-white absolute left-0 top-0 w-4 h-4" />
                                    </div>
                                )}
                            </Button>
                        ))}
                       
                    </Space>
                </div>
                <div className="relative flex items-center">
                            <span className="mr-4 whitespace-nowrap">其他金额：</span>
                            <Input
                                placeholder="请输入金额"
                                value={amount}
                                onChange={handleAmountChange}
                                onFocus={handleInputFocus}
                                onBlur={handleInputBlur}
                                style={{ width: 120 }}

                                // 只能输入数字和小数点
                                pattern="[0-9.]*"
                                prefix="¥"
                                className={`h-10 <mb-2></mb-2> rounded-none ${inputError ? 'border-red-500' : ''}`}
                                status={inputError ? 'error' : undefined}
                            />
                            {inputError && (
                                <div className="absolute left-0 top-full mt-1 text-red-500 text-xs whitespace-nowrap">
                                    {inputError}
                                </div>
                            )}
                        </div>
            </div>

            <div className="mb-6 flex items-center">
                <span className="mr-4">支付方式：</span>
                <Space direction="horizontal" size={16}>
                    {paymentMethods.map((method) => (
                        <div
                            key={method.value}
                            onClick={() => handlePayMethodChange({ target: { value: method.value } })}
                            className={`flex items-center p-4 border rounded-none min-w-[200px] relative cursor-pointer ${payMethod === method.value
                                ? 'border-primary bg-primary/5'
                                : 'border-gray-200'
                                }`}
                        >
                            <SvgIcon localIcon={method.value} className="w-8 h-8 mr-3" />
                            <div>
                                <div className="font-medium">{method.label}</div>
                                <div className="text-gray-400 text-sm">{method.subLabel}</div>
                            </div>
                            {payMethod === method.value && (
                                <div className="absolute -top-[1px] -left-[1px] w-8 h-8">
                                    <div className="absolute left-0 top-0 w-0 h-0 border-[13px] border-primary border-b-transparent border-r-transparent"></div>
                                    <Icon icon="mdi:check-bold" className="text-white absolute left-0 top-0 w-4 h-4" />
                                </div>
                            )}
                        </div>
                    ))}
                </Space>
            </div>

            {payMethod === 'PUBA' && (
                <div className="mb-6 bg-gray-50 p-4 rounded-md">
                    <h4 className="text-base mb-4 font-medium">转账信息</h4>
                    <div className="space-y-3">
                        {transferInfo.map((item) => (
                            <div key={item.label} className="flex items-center">
                                <span className="text-gray-500 w-20">{item.label}：</span>
                                <Paragraph copyable className="flex-1 font-medium mb-0" style={{ marginBottom: 0 }}>
                                    {item.value}
                                </Paragraph>
                            </div>
                        ))}
                    </div>
                    <div className="mt-4 text-gray-500 text-sm flex items-center justify-between">
                        <p>
                            <span>请在转账备注中填写您的用户ID：</span>
                            <Paragraph copyable className="text-primary font-medium mx-1 inline-block mb-0">
                                {userInfo.OwnerID}
                            </Paragraph>
                        </p>
                    </div>
                </div>
            )}

            <div className="flex justify-end items-center mt-8">
                {isSubmitDisabled() && (
                    <div className="text-red-500 text-xs whitespace-nowrap">
                        支付宝充值必须大于￥0且不能超过￥10000，大额充值请选择对公转账！
                    </div>
                )}
                <Button
                    type="primary"
                    size="large"
                    onClick={handleSubmit}
                    disabled={isSubmitDisabled()}
                >
                    立即充值
                </Button>
            </div>
        </Modal>
    );
}