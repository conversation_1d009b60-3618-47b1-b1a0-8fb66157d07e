import { Col, Form, Row, Select } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect } from 'react';
interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading }) => {
  useEffect(() => {
    handleSearch();
  }, []);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      InvoiceState: values.InvoiceState,
    };
    search(params);
  };

  return (
    <Form disabled={loading} form={form}>
      <Row gutter={[16, 16]} wrap>
        {/* <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="BalanceType" label="支付类型">
            <Select
              placeholder="请选择支付类型"
              allowClear
              onChange={handleSearch}
              options={[
                { label: '充值', value: 'INCO' },
                { label: '消费', value: 'CONS' },
                { label: '退款', value: 'RETU' },
              ]}
            />
          </Form.Item>
        </Col> */}

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="InvoiceState" label="状态">
            <Select
              placeholder="请选择状态"
              allowClear
              onChange={handleSearch}
              options={[
                // `InvoiceState` tinyint(1) DEFAULT '0' COMMENT '0 申请中 1 开票中 2 已经完毕 -1 拒绝失败',
                { label: '开票中', value: 0 },
                { label: '开票成功', value: 2 },
                { label: '开票失败', value: -1 },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;