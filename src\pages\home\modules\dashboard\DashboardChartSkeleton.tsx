import React from 'react';
import { Card, Spin } from 'antd';

const DashboardChartSkeleton: React.FC = () => {
  return (
    <Card className="shadow-sm">
      <div 
        style={{ 
          height: '350px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
        //   background: '#fafafa',
          borderRadius: '4px'
        }}
      >
        <Spin  tip="Loading..." />
      </div>
    </Card>
  );
};

export default DashboardChartSkeleton; 