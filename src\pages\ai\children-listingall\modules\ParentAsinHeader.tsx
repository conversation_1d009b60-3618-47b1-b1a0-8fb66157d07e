import { Skeleton } from 'antd';
import { memo, FC, useEffect, useState } from 'react';

interface Props {
    dataSource: any
    searchParams: any
}

const ParentAsinHeader: FC<Props> = memo(({ dataSource, searchParams }) => {
    const [record, setRecord] = useState<any>({});
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (dataSource.length > 0) {
            const last_child = dataSource[dataSource.length - 1];
            console.log(last_child[searchParams.get('asin')], "last_child====");
            setRecord({
                ...last_child[searchParams.get('asin')],
                market: searchParams.get('market'),
                isAuth: searchParams.get('isAuth'),
                haveNum: searchParams.get('haveNum')
            });
            setLoading(false); // Set loading to false once data is set
        } else {
            setLoading(false);
        }
        console.log(dataSource, "dataSource====");
        console.log(searchParams, "searchParams====");
        console.log(record, "record====");
    }, [dataSource]);

    return (
        <ACard>
            <Skeleton loading={loading} active>
                <div className='flex items-center justify-between h-full w-full flex-col sm:flex-row'>
                    <div className='flex gap-10px h-full text-base'>
                           <LazyImage
                                        src={record.first_image}
                                        alt={record.item_name || "--"}
                                        className='flex-shrink-0 h-full'
                                        width={120}
                                    />
                        <div className='flex flex-col justify-between w-full'>
                            {/* <Tooltip title={record?.item_name || "--"} className=''> */}
                            <div className='line-clamp-2 text-left'>
                                {record?.item_name || "--"}
                            </div>
                            {/* </Tooltip> */}
                            <div className='text-start flex items-center py-6'>
                                <div className='mr-2'>
                                    <span className='text-gray-400 mr-1'>SKU:</span>
                                    {record?.seller_sku || "-"}
                                </div>

                                <span className='mx-2'> | </span>
                                <div>
                                    <span className='text-gray-400 mr-1'>库存:</span>
                                    {searchParams.get('haveNum') || "--"}
                                </div>
                            </div>
                            <div className='text-start'>
                                <span className='text-gray-400 mr-1'> 父ASIN: </span>
                                {searchParams.get('asin') || "--"}
                            </div>
                        </div>
                    </div>

                    <div className='flex flex-col text-lg pl-6 pr-2 w-full sm:w-1/5'>
                        <span className='text-gray-400 whitespace-nowrap flex justify-end'>价格</span>
                        <div className='flex items-center flex-nowrap justify-end'>
                            {record?.min_price === record?.max_price ? <span>
                                <CurrencySymbol countryCode={searchParams.get('market')} value={record?.min_price} /></span> :
                                <><CurrencySymbol countryCode={searchParams.get('market')} value={record?.min_price} />~<CurrencySymbol countryCode={searchParams.get('market')} value={record?.max_price} /></>}
                        </div>
                    </div>
                </div>
            </Skeleton>
        </ACard>
    );
});

export default ParentAsinHeader;