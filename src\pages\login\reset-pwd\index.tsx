import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import { useFormRules } from '@/hooks/common/form';

export const Component = () => {
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  const [form] = Form.useForm();
  const { formRules } = useFormRules();
  const [confirmLoading, setConfirmLoading] = useState(false);

  async function handleSubmit() {
    try {
      await form.validateFields();
      setConfirmLoading(true);

      // 这里只是模拟发送重置密码邮件的过程
      // 实际应用中应该调用API发送重置密码邮件
      setTimeout(() => {
        window.$message?.success(t('theme.configOperation.resetSuccessMsg'));
        setConfirmLoading(false);
        toggleLoginModule('pwd-login');
      }, 1500);
    } catch (error) {
      setConfirmLoading(false);
      console.error(error);
    }
  }

  return (
    <>
      <h1 className="mb-4 text-4xl font-semibold">{t('page.login.resetPwd.title')}</h1>

      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="email"
          label={t('page.signup.email')}
          rules={formRules.email}
          className="mb-4"
        >
          <Input
            placeholder={t('page.signup.emailPlaceholder')}
            size="large"
          />
        </Form.Item>

        <div className="mt-8 flex flex-col gap-4">
          <Button
            type="primary"
            size="large"
            block
            loading={confirmLoading}
            onClick={handleSubmit}
          >
            Reset your Password
          </Button>

          <Button
            size="large"
            block
            onClick={() => toggleLoginModule('pwd-login')}
          >
            Login
          </Button>
        </div>
      </Form>
    </>
  );
};

Component.displayName = 'ResetPwd';
