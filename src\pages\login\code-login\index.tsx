import { Button, Form, Input, Space, Modal } from 'antd'; // 添加 Modal
import CaptchaQRCode from '../captch-qr-code';
import { useLogin } from '@/hooks/common/login';
export const Component = () => {
  const [form] = Form.useForm();
  const { label, isCounting, loading, getCaptcha } = useCaptcha();
  const { t } = useTranslation();
  const { toggleLoginModule } = useRouterPush();
  const { formRules } = useFormRules();
  const captchaRef = useRef<any>(null);
  const [captchaKey, setCaptchaKey] = useState('');
  const { loading:toLoginLoading, toLogin } = useLogin();
  const getCaptchaCode = () => {
    form.validateFields(['phone','captchaValue']).then(() => {
      const params = form.getFieldsValue();
      getCaptcha(form,{...params,captchaKey},(data:any)=>{
        captchaRef.current.refreshCaptcha();
      }); // 在确认后调用 getCaptcha
    });
  };



  async function handleSubmit() {
    const params = await form.validateFields();
    console.log(params,"params===");
    // return
    toLogin({...params,lang: "zh" },captchaRef);
    // request to reset password
    // window.$message?.success(t('page.login.common.validateSuccess'));
  }

  useKeyPress('enter', () => {
    handleSubmit();
  });

  return (
    <>
      <h3 className="text-18px text-primary font-medium">{t('page.login.codeLogin.title')}</h3>
      <Form
        className="pt-24px"
        form={form}
      >
        <Form.Item
          name="phone"
          rules={formRules.phone}
        >
          <Input placeholder={t('page.login.common.phonePlaceholder')}></Input>
        </Form.Item>
     
       <CaptchaQRCode
          ref={captchaRef}
          onCaptchaChange={(value, key) => {
            setCaptchaKey(key);
          }}
        />
        <Form.Item
          name="code"
          rules={formRules.phoneCode}
        >
          <div className="w-full flex-y-center gap-16px">
            <Input
              v-model:value="model.code"
              placeholder={t('page.login.common.codePlaceholder')}
            />
            <Button
              disabled={isCounting}
              loading={loading}
              onClick={getCaptchaCode} 
            >
              {label}
            </Button> 
          </div>
        </Form.Item>
        <Space
          direction="vertical"
          className="w-full"
          size={18}
        >
          <Button
            type="primary"
            size="large"
            shape="round"
            block
            loading={toLoginLoading}
            onClick={handleSubmit}
          >
            {t('common.confirm')}
          </Button>

          <Button
            size="large"
            shape="round"
            block
            onClick={() => toggleLoginModule('pwd-login')}
          >
            {t('page.login.common.back')}
          </Button>
        </Space>

      </Form>

     
    </>
  );
};

Component.displayName = 'CodeLogin';