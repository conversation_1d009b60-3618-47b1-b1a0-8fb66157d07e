import { Spin } from 'antd'; // 假设 ACard 是从 antd 引入的
import type { FC } from 'react';
import { getDailyAsinSumData } from '@/service/api';
import BigNumber from "bignumber.js";
const VchartMain = lazy(() => import('@/components/vchart-main'));
import { createListingDaily } from '@/components/weekly-vchart/chart'
import { selectUserInfo } from '@/store/slice/auth';
import { Icon } from '@iconify/react';
import './listing.css'
import original from '@/assets/imgs/original.png'
import { useTranslation } from 'react-i18next';
// import { DownOutlined } from '@ant-design/icons'; // Import the DownOutlined icon


interface VisibleItem {
    title: string;
    value: string;
    color: string;
    isActive: boolean;
    reverseColors?: boolean;
    isInteger?: boolean;
}

const ListingDashboard: FC = ({ params, authLoading = true }: any) => {
    const { t } = useTranslation();
    
    const data = [
        { title: t('page.listingall.dashboard.totalSales'), value: "总销售额", color: "#154EC1", isShowCurrency: true },
        { title: t('page.listingall.dashboard.adTotalSales'), value: "广告总销量", color: "#7ED321", isShowCurrency: true },
        // 加入是否需要展示货币
        { title: "ACOS", value: "ACOS", color: "#a3317b", reverseColors: true, isPercent: true },
        { title: t('page.listingall.dashboard.adImpressions'), value: "曝光量", color: "#9013FE", isInteger: true },
        { title: "TACOS", value: "TACOS", color: "#B8E986", reverseColors: true, isPercent: true },
        { title: t('page.listingall.dashboard.naturalSalesRatio'), value: "自然销售额比例", color: "#50E3C2", isPercent: true },
        { title: t('page.listingall.dashboard.adSpend'), value: "广告总花费", color: "#BD10E0", isShowCurrency: true },
        { title: t('page.listingall.dashboard.totalOrders'), value: "总订单量", color: "#F5A623", isInteger: true },
        { title: t('page.listingall.dashboard.naturalSales'), value: "自然销售额", color: "#D0021B", isShowCurrency: true },
        { title: t('page.listingall.dashboard.clicks'), value: "点击量", color: "#417505", isInteger: true },
        { title: t('page.listingall.dashboard.orders'), value: "订单量", color: "#FF5733", isInteger: true },
        { title: t('page.listingall.dashboard.ctr'), value: "CTR", color: "#C70039", isPercent: true },
        { title: t('page.listingall.dashboard.conversionRate'), value: "conversion_rate", color: "#900C3F", isPercent: true },
        { title: t('page.listingall.dashboard.naturalSalesRatio'), value: "自然销售额比例", color: "#50E3C2", isPercent: true },
    ];

    // 初始化时，前三个为亮，后三个为不亮
    const [visibleItems, setVisibleItems] = useState<VisibleItem[]>(data.slice(0, 5).map((item, index) => ({
        ...item,
        isActive: index < 4
    })));

    const [loading, setLoading] = useState(true);
    const [sumList, setSumList] = useState([]);
    const userInfo = useAppSelector(selectUserInfo);
    // 是否没有数据
    const [isDataEmpty, setIsDataEmpty] = useState(false);

    const [sumDailyChart, setSumDailyChart] = useState({
        loading: false,
        height: 450,
        chart: createListingDaily(),
    });

    const toggleItemActive = (index: number) => {
        const newVisibleItems = [...visibleItems];
        newVisibleItems[index].isActive = !newVisibleItems[index].isActive;
        setVisibleItems(newVisibleItems);
    };

    const handleSwitch = (index: number, newItem: any) => {
        const newVisibleItems = [...visibleItems];
        newVisibleItems[index] = { ...newItem, isActive: newVisibleItems[index].isActive };
        setVisibleItems(newVisibleItems);
    };

    const getDailyAsinSumDataList = async (params: any) => {
        const res = await getDailyAsinSumData({
            UID: params.UID,
            CountryCode: params.CountryCode,
            StartDate: params.StartDate,
            EndDate: params.EndDate
        });
        // console.log(res);
        if (res && res.data) {
            const { data } = res.data;

            const sum = data.sum;
            const sum_old = Object.fromEntries(
                Object.entries(data.sum_old).map(([key, value]) => [`old_${key}`, value === "None" ? "" : value])
            );

            sum.ACOS = new BigNumber(sum.广告总花费).dividedBy(sum.广告总销量).multipliedBy(100).toFixed(2) + "%";
            sum.TACOS = new BigNumber(sum.广告总花费).dividedBy(sum.总销售额).multipliedBy(100).toFixed(2) + "%";
            sum.CTR = new BigNumber(sum.点击量).dividedBy(sum.曝光量).multipliedBy(100).toFixed(2) + "%";
            sum.conversion_rate = new BigNumber(sum.订单量).dividedBy(sum.点击量).multipliedBy(100).toFixed(2) + "%";
            sum.自然销售额比例 = new BigNumber(sum.自然销售额).dividedBy(sum.总销售额).multipliedBy(100).toFixed(2) + "%";

            sum_old.old_ACOS = new BigNumber(sum_old.old_广告总花费).dividedBy(sum_old.old_广告总销量).multipliedBy(100).toFixed(2) + "%";
            sum_old.old_TACOS = new BigNumber(sum_old.old_广告总花费).dividedBy(sum_old.old_总销售额).multipliedBy(100).toFixed(2) + "%";
            sum_old.old_CTR = new BigNumber(sum_old.old_点击量).dividedBy(sum_old.old_曝光量).multipliedBy(100).toFixed(2) + "%";
            sum_old.old_conversion_rate = new BigNumber(sum_old.old_订单量).dividedBy(sum_old.old_点击量).multipliedBy(100).toFixed(2) + "%";
            sum_old.old_自然销售额比例 = new BigNumber(sum_old.old_自然销售额).dividedBy(sum_old.old_总销售额).multipliedBy(100).toFixed(2) + "%";

            // 计算这几个比例的环比
            // sum.ACOS_环比 = new BigNumber(sum.ACOS).minus(sum_old.old_ACOS).dividedBy(sum_old.old_ACOS).multipliedBy(100).toFixed(2) + "%";
            // sum.TACOS_环比 = new BigNumber(sum.TACOS).minus(sum_old.old_TACOS).dividedBy(sum_old.old_TACOS).multipliedBy(100).toFixed(2) + "%";
            // sum.CTR_环比 = new BigNumber(sum.CTR).minus(sum_old.old_CTR).dividedBy(sum_old.old_CTR).multipliedBy(100).toFixed(2) + "%";
            // sum.conversion_rate_环比 = new BigNumber(sum.conversion_rate).minus(sum_old.old_conversion_rate).dividedBy(sum_old.old_conversion_rate).multipliedBy(100).toFixed(2) + "%";
            // sum.自然销售额比例_环比 = new BigNumber(sum.自然销售额比例).minus(sum_old.old_自然销售额比例).dividedBy(sum_old.old_自然销售额比例).multipliedBy(100).toFixed(2) + "%";

            const newData = {
                ...sum_old,
                ...sum,
            };
            setSumList(newData);

            const sumDaily = data.sum_daily;

            const chartData = Array.isArray(sumDaily) 
            ? sumDaily.map((item: any) => {
                const filteredData = visibleItems.reduce((acc: any, visibleItem: any) => {
                    if (item[visibleItem.value] !== undefined) {
                        acc[visibleItem.value] = item[visibleItem.value];
                    }
                    acc.date = item.date;
                    return acc;
                }, {});
                return filteredData;
            })
            : [];
            
            // const chartData = sumDaily.map((item: any) => {
            //     const filteredData = visibleItems.reduce((acc: any, visibleItem: any) => {
            //         if (item[visibleItem.value] !== undefined) {
            //             acc[visibleItem.value] = item[visibleItem.value];
            //         }
            //         acc.date = item.date;
            //         return acc;
            //     }, {});
            //     return filteredData;
            // });



            const activeItems = visibleItems.filter(item => item.isActive);

            const dataEntries = activeItems.map((item, index) => ({
                id: `data${index}`,
                values: chartData.map(data => ({
                    date: data.date,
                    value: parseFloat(data[item.value].replace("%", "")) || 0,
                    type: item.title,
                    isPercent: data[item.value].includes("%")
                }))
            }));

            const seriesEntries = activeItems.map((item, index) => {
                // 只有全等ACOS才能是柱状图
                const isBar = item.title === "总销售额";

                return {
                    type: !isBar ? 'line' : 'bar',
                    // id: `${isAreaChart ? 'area' : 'bar'}${index}`,
                    // type:'line',
                    id: `area${index}`,
                    dataId: `data${index}`,
                    // xField: ['date', 'type'],
                    // xField: isAreaChart ? 'date' : ['date', 'type'],
                    xField: 'date',
                    yField: 'value',
                    seriesField: 'type',
                    color: item.color,
                    // sortDataByAxis: true,
                    // zIndex: isBar ? 0 : 1,
                    barMaxWidth: 15,
                    animationAppear: {
                        duration: 800,
                        easing: 'linear'
                    },
                    stack: false,
                    bar: {
                        style: {
                            cornerRadius: 10,
                        }
                    },
                    point: {
                        style: {
                            size: 0,
                            fill: 'white',
                            stroke: null,
                            lineWidth: 2,
                            zIndex: isBar ? 1 : 9
                        },
                        state: {
                            dimension_hover: {
                                size: 10,
                                // 根据索引展示不同的
                                symbolType: index === 0 ? 'circle' : index === 1 ? 'square' : index === 2 ? 'triangle' : index === 3 ? 'diamond' : 'star',
                                // zIndex: 1000
                                zIndex: isBar ? 1 : 9

                            }
                        }
                    },
                    line: {

                        style: {
                            curveType: 'monotone'
                        }
                    },
                    // point: {
                    //     visible: true
                    // }
                };
            });

            const axesEntries = activeItems.map((item, index) => ({
                orient: index % 2 === 0 ? 'left' : 'right',
                // seriesId: [`${index % 2 === 0 ? 'area' : 'bar'}${index}`],
                seriesId: [`area${index}`],
                label: {
                    style: {
                        fill: item.color
                    },
                    formatMethod: value => {
                        // 判断 isPercent
                        if (item.isPercent) {
                            return `${value}%`;
                        }
                        if (item.isShowCurrency) {
                            const currencyCode = params.CountryCode ? getCurrency(params.CountryCode) || '' : '';
                            const symbol = currencyCode ? getCurrencySymbol(currencyCode) || '' : '';
                            return `${symbol}${value}`;
                        }
                        return value;
                    }
                }
            }));

            axesEntries.push({
                orient: 'bottom',
                label: { visible: true },
                // type: 'band',
                // trimPadding: true
            });

            const colors = activeItems.map(item => item.color);

            setSumDailyChart({
                ...sumDailyChart,
                chart: {
                    ...sumDailyChart.chart,
                    data: dataEntries,
                    series: seriesEntries,
                    axes: axesEntries,
                    color: colors
                }
            });

            console.log(sumDailyChart, "sumDailyChart")

            setLoading(false);
            setIsDataEmpty(false);
        } else {
            console.log("没有数据")
            setSumList([]);
            setSumDailyChart({
                ...sumDailyChart,
                chart: {
                    ...sumDailyChart.chart,
                    data: [],
                }
            });
            setLoading(false);
            setIsDataEmpty(true);
        }
    };

    useEffect(() => {
        setLoading(true);
        console.log(params, "params")
        if (Object.keys(params).length > 0) {
            getDailyAsinSumDataList(params);
        } else {
            setLoading(false);
        }
    }, [params, visibleItems]);
    return (

        <ACard title={
            <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                    {t('page.listingall.dashboard.dataOverview')}
                <div className='ml-4 text-red-500'>
                    {(!params?.CountryCode || !params?.GetAmazonData || !userInfo?.active_shop_id) && !authLoading ? t('page.listingall.dashboard.dataCollecting') : ""}
                </div>
                </div>
                <DocumentButton
                    text={t('page.listingall.dashboard.dataCollectionGuide')}
                    link="https://deepthought.feishu.cn/wiki/Pp1ow76f7iGDETk8VXQc34L0nbe"
                />
            </div>
        } bordered={false}>
            <Spin spinning={loading}

                className='custom-spin'
                // indicator={<LoadingOutlined style={{ color: '#353535' }} />}
                // size="large"
                tip={t('page.listingall.dashboard.loadingChart')}
                style={{
                    minHeight: '450px',
                }}
            >

                <div className="flex flex-wrap justify-between w-full listing-dashboard">
                    {visibleItems.map((item, index) => (
                        <ACard
                            key={item.title}
                            className={`h-full cursor-pointer border-t-4 text-start text-center w-full sm:w-1/3 md:w-1/4 lg:w-1/5 ${item.isActive ? '' : 'border-t-gray-400'}`}
                            style={{
                                borderTopColor: item.isActive ? item.color : '#ccc', width: 'calc(20% - 20px)', // Adjust width to account for margin
                                margin: '10px'
                            }}
                            onClick={() => toggleItemActive(index)}
                        >
                            <ASelect
                                onClick={(e) => e.stopPropagation()}
                                variant={'borderless'}
                                popupMatchSelectWidth={false}
                                onChange={(value) => {
                                    const selectedItem = data.find(d => d.title === value);
                                    if (selectedItem) handleSwitch(index, selectedItem);
                                }}
                                value={item.title}


                            // Use Ant Design's Icon component for the arrow
                            >
                                {data.map(option => (
                                    <ASelect.Option

                                        key={option.title}
                                        value={option.title}
                                        disabled={visibleItems.some(visibleItem => visibleItem.title === option.title)}
                                    >
                                        {option.title}
                                    </ASelect.Option>
                                ))}
                            </ASelect>
                            <div className="font-500 ">
                                {
                                    <CurrencySymbol className='ListingDashboard' showOld={true} countryCode={item.isShowCurrency ? params.CountryCode : ""} value={sumList[item.value]?.toString() || '--'} oldValue={sumList[`old_${item.value}`]?.toString() || '--'} isShowContrast={true} isInteger={item.isInteger} reverseColors={item.reverseColors || false} />
                                }
                            </div>
                        </ACard>
                    ))}

                    <div className='w-full'>
                        <VchartMain item={sumDailyChart} />
                        {isDataEmpty && (
                            <div className="flex flex-col items-center justify-center h-full absolute top-0 left-0 w-full">
                                {/* <Icon icon="nonicons:not-found-16" width="48" height="48" className="mb-4 text-#597EF7" /> */}
                                <AEmpty
                                    image={original}
                                    imageStyle={{
                                        height: 100,
                                    }}
                                    description={
                                        <span>
                                            {/* Customize <a href="#API">Description</a> */}
                                        </span>
                                    }
                                ></AEmpty>
                                {/* <div className="text-gray-500 mb-4">没有数据</div> */}
                                {params.CountryCode && <AButton type="link" onClick={() => {
                                    setIsDataEmpty(false);
                                    getDailyAsinSumDataList(params)
                                }}>{t('page.listingall.dashboard.reload')}</AButton>}
                            </div>
                        )
                        }
                    </div>
                </div>
            </Spin>
        </ACard>
    );
};

export default ListingDashboard;