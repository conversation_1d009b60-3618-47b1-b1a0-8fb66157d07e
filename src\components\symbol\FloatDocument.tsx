import { Icon } from '@iconify/react';
import { useLocation } from 'react-router-dom';

const FloatService: React.FC = () => {
  const location = useLocation();
  const isLoginPage = location.pathname;
  const { t } = useTranslation();
  const handleGoDocument = () => {
    switch (isLoginPage) {
      // 店铺管理
      case '/management/auth':
        window.open('https://deepthought.feishu.cn/wiki/KIV3wmNU0ivQ3bk5bVcc66ZGnte', '_blank');
        break;
      // 子账号
      case '/management/subaccounts':
        window.open('https://deepthought.feishu.cn/wiki/XBaUwUByziFf4AkYwkOcvasynDc', '_blank');
        break;
      // AI托管记录
      case '/management/listinghistory':
        window.open('https://deepthought.feishu.cn/wiki/BTLXwBi2aiPgDOkMGQbcPgaZndb', '_blank');
        break;
      // 订单管理
      case '/management/order/service':
      case '/management/order/rechage':
        window.open('https://deepthought.feishu.cn/wiki/HBQ4wNO9Nif4S1ksIZJceMWfnlg', '_blank');
        break;
      // 发票管理
      case '/management/bill':
        window.open('https://deepthought.feishu.cn/wiki/N0Icwwj4jiz1sfkmvQOcO9bcnEo', '_blank');
        break;
      // 用户管理
      case '/management/user':
        window.open('https://deepthought.feishu.cn/wiki/MrMvwoW5DiSD3vkz3iocU9jUnrb', '_blank');
        break;
      // 所有listing
      case '/ai/listingall':
        window.open('https://deepthought.feishu.cn/wiki/H0ouwkCOhiKaA4kL1YOc9bdtnu8', '_blank');
        break;
      // 托管listing
      case '/ai/listing':
        window.open('https://deepthought.feishu.cn/wiki/DtvGwAFDJieI26kNYcjcKchanvb', '_blank');
        break;
      // 数据对比
      case '/ai/daily':
        window.open('https://deepthought.feishu.cn/wiki/Y4fEwXLvAiS13XkLko7cKuxjnfh', '_blank');
        break;
      // 库存管理
      case '/ai/inventory':
        window.open('https://deepthought.feishu.cn/wiki/IOVRwrhGIifCA4kerG3cxDqMnQg', '_blank');
        break;
      // 操作记录
      case '/ai/operationrecord':
        window.open('https://deepthought.feishu.cn/wiki/SwqwwN0KiihZOwkQa7XciElJnCe', '_blank');
        break;
      // 首页
      case '/home':
        window.open('https://deepthought.feishu.cn/wiki/MdzYwBLpzi68e8kB1TocbB7Fnbg', '_blank');
        break;
      default:
        if (isLoginPage.includes('/management/buy-auth')) {
          window.open('https://deepthought.feishu.cn/wiki/GAvRw5uNris9YpkHPf6cm8Ewnid', '_blank');
        }
        break;
    }
  };

  return (
    <ButtonIcon
      className="relative"
      tooltipContent={t('page.home.Doc')}
      onClick={handleGoDocument}
    >
      <Icon
        icon="proicons:book-marked"
        className="text-20px"
      />
    </ButtonIcon>
  );
};

export default FloatService;
