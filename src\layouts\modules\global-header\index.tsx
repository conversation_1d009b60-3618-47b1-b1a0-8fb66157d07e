import DarkModeContainer from '@/components/stateless/common/DarkModeContainer';
import ThemeSchemaSwitch from '@/components/stateful/ThemeSchemaSwitch';
// import LangSwitch from '@/components/stateful/LangSwitch';
import FullScreen from '@/components/stateless/common/FullScreen';
import { GLOBAL_HEADER_MENU_ID } from '@/constants/app';
import { selectUserInfo } from '@/store/slice/auth';
import GlobalLogo from '../global-logo';
import GlobalSearch from '../global-search';
import GlobalBreadcrumb from '../global-breadcrumb';
import ThemeButton from './components/ThemeButton';
import UserAvatar from './components/UserAvatar';
interface Props {
  isMobile: boolean;
  mode: UnionKey.ThemeLayoutMode;
  siderWidth: number;
  reverse?: boolean;
}

const HEADER_PROPS_CONFIG: Record<UnionKey.ThemeLayoutMode, App.Global.HeaderProps> = {
  vertical: {
    showLogo: false,
    showMenu: false,
    showMenuToggler: true
  },
  'vertical-mix': {
    showLogo: false,
    showMenu: false,
    showMenuToggler: false
  },
  horizontal: {
    showLogo: true,
    showMenu: true,
    showMenuToggler: false
  },
  'horizontal-mix': {
    showLogo: true,
    showMenu: true,
    showMenuToggler: false
  }
};

const GlobalHeader: FC<Props> = memo(({ mode, isMobile, siderWidth, reverse }) => {
  const userInfo = useAppSelector(selectUserInfo);
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(document.body);
  const serviceExpirationModelRef = useRef<any>(null);
  const { showLogo, showMenu, showMenuToggler } = HEADER_PROPS_CONFIG[mode];

  const showToggler = reverse ? true : showMenuToggler;

  useEffect(() => {
    if (userInfo?.alert_country && userInfo?.first_login) {
      // 合并alert_country_3 和 alert_country_7 判断长度
      const alertCountry = [...userInfo.alert_country.alert_country_3, ...userInfo.alert_country.alert_country_7];
      if (alertCountry.length > 0) {
        serviceExpirationModelRef.current?.showModal();
      }
    }
  }, [userInfo]);

  return (
    <DarkModeContainer className="h-full flex-y-center px-12px shadow-header">
      {showLogo && (
        <GlobalLogo
          className="h-full"
          style={{ width: `${siderWidth}px` }}
        />
      )}
      <div>{reverse ? true : showMenuToggler}</div>

      {showToggler && <MenuToggler />}

      <div
        id={GLOBAL_HEADER_MENU_ID}
        className="h-full flex-y-center flex-1-hidden"
      >
        {/* {!isMobile && !showMenu && <GlobalBreadcrumb className="ml-12px" />} */}
      </div>
      <ServiceExpirationModel ref={serviceExpirationModelRef} />
      <div className="h-full flex-y-center justify-end">
        <GlobalSearch />
        {/* {!isMobile && (
          <FullScreen
            className="px-12px"
            full={isFullscreen}
            toggleFullscreen={toggleFullscreen}
          />
        )} */}
        {/* <LangSwitch className="px-12px" />
        <ThemeSchemaSwitch className="px-12px" /> */}
        {/* 自定义主题样式 */}
        <ThemeButton />
        {/* 消息 */}
        <InternalMessage />
        {/* 文档 */}
        <FloatDocument />
        {/* 客服 */}
        {/* <FloatService /> */}
        {/* 用户头像 */}
        <UserAvatar />
      </div>
    </DarkModeContainer>
  );
});

export default GlobalHeader;
