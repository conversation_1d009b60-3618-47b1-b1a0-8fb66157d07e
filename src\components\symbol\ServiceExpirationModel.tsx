import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Modal, Typography } from 'antd';
import { selectUserInfo } from '@/store/slice/auth';
import { rechagePackage, fetchGetUserInfo } from '@/service/api';
import { updateUserInfo, getUerName } from '@/store/slice/auth';
import { useAppDispatch } from '@/hooks/business/useStore';
import { Icon } from '@iconify/react';
import { localStg } from '@/utils/storage';
import { spawn } from 'child_process';
import { $t } from '@/locales';
const ServiceExpirationModel = forwardRef((props: any, ref) => {
    const { Text, Paragraph } = Typography;
    const userInfo = useAppSelector(selectUserInfo);
    const [modalVisible, setModalVisible] = useState(false);
    const [alertCountry, setAlertCountry] = useState<any[]>([]);
    const dispatch = useAppDispatch();
    const nav = useNavigate();
    useImperativeHandle(ref, () => ({
        showModal: () => setModalVisible(true),
        hideModal: () => setModalVisible(false),
    }));

    const getRechargePackage = async () => {
        const res = await rechagePackage({});
        if (res && res.data.length > 0) {
            // Handle the response
        }
    };

    useEffect(() => {
        if (modalVisible) {
            const combinedAlerts = [...userInfo.alert_country.alert_country_3, ...userInfo.alert_country.alert_country_7];
            const alertMap = new Map<number, any>();

            combinedAlerts.forEach((item) => {
                const existingEntry = alertMap.get(item.UID);
                if (existingEntry) {
                    existingEntry.children.push({
                        ...item,
                        key: item.UID + item.CountryCode,
                    });
                } else {
                    alertMap.set(item.UID, {
                        UID: item.UID,
                        key: item.UID,
                        ShopName: userInfo.group_shop[item.UID]?.ShopName || '--',
                        children: [item],
                    });
                }
            });

            const mergedAlerts = Array.from(alertMap.values());

            console.log(mergedAlerts, 'mergedAlerts')
            setAlertCountry(mergedAlerts);
        }
    }, [modalVisible, userInfo.alert_country]);
    const columns = [
        {
            title: $t('page.table.columns.shopname'),
            dataIndex: 'ShopName',
            key: 'ShopName',
            // align: 'center'
        },
        {
            title: $t('page.table.columns.country'),
            dataIndex: 'CountryCode',
            key: 'CountryCode',
            // align: 'center',
            render: (text: string) => {
                return (
                    <div className="flex items-center" >
                        {/* {`circle-flags:${text}`} */}
                        <Icon
                            className="mr-2"
                            icon={`circle-flags:${text?.toLowerCase()}`}
                            width={30}
                            height={30}
                        />
                        {text && <Text>
                        {$t(`page.setting.country.${text}`)}</Text>}
                        {/* <Text >{text}</Text> */}
                    </div>
                )
            }
        },
        {
            title: $t('page.table.columns.serviceexpire'),
            dataIndex: 'AdServiceEndDatetime',
            key: 'AdServiceEndDatetime',
            align: 'center',
            render: (text: string, record: any) => {
                const now = new Date();
                const endDate = new Date(text);
                const diffTime = endDate.getTime() - now.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
                let colorClass = '';
                let timeDisplay = '';
        
                if (diffDays > 0) {
                    if (diffDays <= 3) {
                        colorClass = 'text-red-500 font-bold';
                    } else if (diffDays <= 7) {
                        colorClass = 'text-yellow-500 font-bold';
                    }
                    timeDisplay = `${diffDays} ${$t('page.table.columns.day')}`;
                } else {
                    const endOfToday = new Date();
                    endOfToday.setHours(23, 59, 59, 999);
                    const remainingHours = Math.ceil((endOfToday.getTime() - now.getTime()) / (1000 * 60 * 60));
                    colorClass = 'text-red-500 font-bold';
                    timeDisplay = `${remainingHours} ${$t('page.table.columns.hour')}`;
                }
        
                return (
                    record.AdServiceEndDatetime && (
                        <span>
                            {$t('page.table.columns.remaining')}
                            <span className={`${colorClass} text-base mx-1`}>{timeDisplay}</span>
                        </span>
                    )
                );
            },
        },
    ];

    // 关闭弹窗 而且userInfo.alert_country 中的  alert_country_7 和 alert_country_3 清空
    const handleCancel = async (redirect = false) => {
        setModalVisible(false);
        // 更新 Redux 状态

        // const { data: info, error: userInfoError } = await fetchGetUserInfo();
        // if (error) {
        //     dispatch(updateUserInfo({ userInfo: info }));
        //     //   const UserName = dispatch(getUerName());
        //     localStg.se$t('userInfo', info);
        //     if (redirect) {
        //         window.location.href = '/management/auth';
        //     }
        // } else {
            // 手动将 alert_country_7 和 alert_country_3 清空
            let newUserInfo = { ...userInfo, alert_country: { alert_country_7: [], alert_country_3: [] } };
            dispatch(updateUserInfo({ userInfo: newUserInfo }));
            // 更新 roles
            newUserInfo.roles = newUserInfo.OwnerFlag == 0 ? ["R_ADMIN"] : ["R_USER_COMMON"]
            localStg.se$t('userInfo', newUserInfo);
            if (redirect) {
                // window.location.href = '/management/auth';
                nav('/management/auth');
            }
        // }
        
    }
    return (
        <Modal
            title={$t('page.setting.auth.serviceexpire')}
            open={modalVisible}
            closable={false}
            
            width={900}
            maskClosable={false}
            footer={
                <div className="flex justify-end">
                    <AButton type="link" className="mr-2" onClick={() => handleCancel(false)}>{$t('page.setting.auth.serviceexpire1')}</AButton>
                    <AButton type="primary" onClick={() => handleCancel(true)}>{$t('page.setting.auth.serviceexpire2')}</AButton>
                </div>
            }
        >
            {alertCountry.length > 0 && <ATable
                dataSource={alertCountry}
                columns={columns}
                rowKey="key"
                scroll={{ y: 500 }}
                bordered={false}
                pagination={false}
                expandable={{
                    // defaultExpandedRowKeys
                    showExpandColumn: false,
                    defaultExpandAllRows: true,
                }}
            />}
        </Modal>
    );
});

export default ServiceExpirationModel;