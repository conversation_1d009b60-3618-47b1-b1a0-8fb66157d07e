import React, { useState, useRef, useEffect } from 'react';
import { Modal, Form, Input, Button, message, InputNumber } from 'antd';
import { useTranslation } from "react-i18next";
import { AuthAsinCheck, AuthAsinAdd } from "@/service/api";
import { useNavigate } from 'react-router-dom';
interface NewAntdModalComponentProps {
    visible: boolean;
    closeDrawer: () => void;
    form: any;
    onRefresh: () => void;
    tableloading:boolean
}
 
interface AsinData {
    Asin: string;
    PreAcos: number;
    AuthMaxMoney: number;
}

const NewAntdModalComponent: React.FC<NewAntdModalComponentProps> = ({ visible, closeDrawer, form, onRefresh,tableloading }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [step, setStep] = useState<number>(1);
    const [newAuthAsin, setNewAuthAsin] = useState<AsinData[]>([]);
    const [errorAsinText, setErrorAsinText] = useState<string>('');
    const [newAuthAsinText, setNewAuthAsinText] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const formRef = useRef<any>(null);

    const handleSubmitStep1 = async (values: any) => {
        if(tableloading){
            return
        }
        setLoading(true);
        let asin_data = values.asinTextArea || '';
        if (values.newAuthAsinTextArea) {
            asin_data += `\n${values.newAuthAsinTextArea}`;
        }
        if (values.errorAsinTextArea) {
            asin_data += `\n${values.errorAsinTextArea}`;
        }
        try {
            let errMsg = [];
            const res = await AuthAsinCheck({ asin_data, CountryCode: form.getFieldValue('country') });
            if (res.data) {
                if (!res.data.have_all_asin) {
                    window.$notification?.warning({
                        message: "数据采集中，请稍后再试",
                        description: <div>您可前往<span className="text-blue-500 cursor-pointer" onClick={() => window.location.href='/message'}>消息页面</span>查看具体信息</div>
                    });
                    setLoading(false);
                    return;
                }

                if (res.data.new_auth_asin.length === 0) {
                    // Check for authorized ASINs
                    if (res.data.authed_asin.length > 0) {
                        errMsg.push(`ASIN已授权: ${res.data.authed_asin.join(', ')}`);
                    }

                    // Check for non-existent ASINs
                    if (res.data.not_exsit_asin.length > 0) {
                        errMsg.push(`ASIN不存在: ${res.data.not_exsit_asin.join(', ')}`);
                    }
                    if (res.data.storage_not_enough.length > 0) {
                        errMsg.push(`库存不足${res.data.AsinLimitStore}：${res.data.storage_not_enough.join(', ')}`);
                    }
                    if (res.data.processing_asin.length > 0) {
                        errMsg.push(`ASIN关系正在获取中,请稍后: ${res.data.processing_asin.join(', ')}`);
                    }

                    // Display the error message if there are any
                    if (errMsg.length > 0) {
                        // message.error(errMsg.join('\n'));
                        window.$notification?.error({
                            message: (
                                <div>
                                    {errMsg.map((msg, index) => (
                                        <div key={index}>{msg}</div>
                                    ))}
                                </div>
                            )
                        });
                        setLoading(false);
                        return;
                    }
                }
                const new_auth_asin = res.data.new_auth_asin.map((item: string) => ({ Asin: item, PreAcos: 0.2, AuthMaxMoney: 200 }));
                const error_asin = res.data.error_asin.map((item: string) => ({ Asin: item, PreAcos: 0.2, AuthMaxMoney: 200 }));
                setNewAuthAsinText(res.data.new_auth_asin.join('\n'));
                setErrorAsinText(res.data.error_asin.join('\n'));
                if (error_asin.length === 0) {
                    setNewAuthAsin([...new_auth_asin]);
                    handleSubmitStep2([...new_auth_asin]);
                } else {
                    window.$notification?.error({
                        message: "存在未校验通过ASIN，请修改未通过的ASIN",
                    });
                    setLoading(false);
                }
            } else {
                setLoading(false);
            }
        } catch (error) {
            setLoading(false);
        }
    };

    const handleSubmitStep2 = async (values: AsinData[]) => {
        setLoading(true);
        try {
            const res = await AuthAsinAdd({ AsinData: values, CountryCode: form.getFieldValue('country') });
            if (res.data) {
                window.$notification?.success({
                    message: "添加成功",
                });
                closeDrawer();
                setLoading(false);
                onRefresh();
            } else {
                const asin_data = [...res.data.failed, ...res.data.error].join(',');
                window.$notification?.error({
                    message: `添加失败: ${asin_data}`,
                });
                setLoading(false);
            }
        } catch (error) {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            setStep(1);
            setNewAuthAsin([]);
            setErrorAsinText('');
            setNewAuthAsinText('');
            formRef.current && formRef.current.resetFields();
        }
    }, [visible]);

    return (
        <Modal
            title={`${t(`page.setting.country.${form.getFieldValue('country')}`)} 新增托管ASIN`}
            open={visible}
            onCancel={() => { if (!loading) { closeDrawer(); } }}
            footer={null}
            width={800}
            maskClosable={false}
        >
            <Form
                ref={formRef}
                onFinish={step === 1 ? handleSubmitStep1 : handleSubmitStep2}
                layout="vertical"
            >
                {step === 1 ? (
                    <>
                        {!errorAsinText && !newAuthAsinText && (
                            <Form.Item
                                name="asinTextArea"
                                label="ASIN或者URL"
                                rules={[{ required: true, message: '请输入ASIN或者亚马逊商品链接' }]}
                            >
                                <Input.TextArea
                                    rows={10}
                                    placeholder='在这里输入您的ASIN或者亚马逊商品链接,每行一个，可批量添加'
                                />
                            </Form.Item>
                        )}
                        {errorAsinText && (
                            <Form.Item
                                name="errorAsinTextArea"
                                label="未通过的ASIN"
                                initialValue={errorAsinText}
                            >
                                <Input.TextArea rows={5}  />
                            </Form.Item>
                        )}
                        {newAuthAsinText && (
                            <Form.Item
                                name="newAuthAsinTextArea"
                                label="通过的ASIN"
                                initialValue={newAuthAsinText}
                            >
                                <Input.TextArea rows={5} readOnly />
                            </Form.Item>
                        )}
                        <div className="p-4 bg-blue-100 rounded-md text-blue-900">
                            注意：
                            <br />
                            系统的授权最小单位是父ASIN，授权时按照整个Listing进行。
                            <br />
                            输入为子ASIN时，会自动反查其对应的父ASIN，并将整个父ASIN（及其下所有子ASIN）添加至授权列表中。
                            <br />
                            如无对应父ASIN，系统将视该子ASIN为父ASIN进行授权。
                            <br />
                            DeepBI系统仅对父ASIN大于50的起效，请确保库存大于50再进行托管
                        </div>
                        <div className="flex justify-end w-full">
                            <Button loading={loading} type="primary" htmlType="submit" className="mt-4">
                                确认托管
                            </Button>
                        </div>
                    </>
                ) : (
                    <>
                        {newAuthAsin.map((asin, index) => (
                            <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                                <Form.Item
                                    name={['rules', index, 'Asin']}
                                    label="ASIN或者URL"
                                    initialValue={asin.Asin}
                                    rules={[{ required: true, message: '请输入ASIN' }]}
                                >
                                    <Input disabled style={{ width: 220, marginRight: 30 }} />
                                </Form.Item>
                                <Form.Item
                                    name={['rules', index, 'PreAcos']}
                                    label="预期ACOS"
                                    initialValue={asin.PreAcos}
                                    rules={[{ required: true, message: '请输入预期ACOS' }]}
                                >
                                    <InputNumber min={0} step={0.2} style={{ width: 120, marginRight: 30 }} />
                                </Form.Item>
                                <Form.Item
                                    name={['rules', index, 'AuthMaxMoney']}
                                    label="预算（日）"
                                    initialValue={asin.AuthMaxMoney}
                                    rules={[{ required: true, message: '请输入预算' }]}
                                >
                                    <InputNumber min={0} step={10} style={{ width: 120 }} />
                                </Form.Item>
                            </div>
                        ))}
                        <div className="flex justify-end w-full">
                            <Button loading={loading} onClick={() => setStep(1)} className="mt-4 mr-4">
                                返回
                            </Button>
                            <Button loading={loading} type="primary" htmlType="submit" className="mt-4">
                                确认
                            </Button>
                        </div>
                    </>
                )}
            </Form>
        </Modal>
    );
};

export default NewAntdModalComponent;