const page: App.I18n.Schema['translation']['page'] = {
  login: {
    common: {
      loginOrRegister: '登录 / 注册',
      userNamePlaceholder: '请输入用户名',
      phonePlaceholder: '请输入手机号',
      companyNamePlaceholder: '请输入公司名称',
      codePlaceholder: '请输入验证码',
      switching: '切换中',
      emailPhonePlaceholder: '请输入邮箱/手机号',
      captcha: '图形验证码',
      captchaPlaceholder: '请输入图形验证码',
      passwordPlaceholder: '请输入密码',
      confirmPasswordPlaceholder: '请再次输入密码',
      codeLogin: '验证码登录',
      confirm: '确定',
      back: '返回',
      validateSuccess: '验证成功',
      loginSuccess: '登录成功',
      welcomeBack: '欢迎回来，',
      passwordRule: '请输入最短8位，包含大小写字母、数字的密码',
      agree: '请勾选协议',
      agreeText: '阅读并同意',
      agreeLink: 'ATLAS软件条款和条件',
      switchshop: '切换店铺'
    },
    description: {
      title: '亚马逊全托管AI广告助手',
      description: '亚马逊官方认证SPN服务商',
      serviceprice: '服务价格',
      price: '299元/国家/月',
      servicepriceunit: '（单一国家最多可托管50个Listing）',
      applyseller: '适用卖家类型',
      applysellerdesc: '精品卖家,精铺卖家,铺货卖家',
      productscope: '产品适用范围',
      productscope1: '成熟产品（已有稳定订单积累、转化率具备市场竞争力、表现明显优于30%同类竞品）',
      productscope1op: '投放约 2 周可获得明显的数据优化效果',
      productscope2: '全新产品（新品上线，转化率尚不确定）',
      productscope2op:
        '投放 1-2 周后可诊断转化表现，并明确是否需进一步优化转化率。系统将参考AI所探索到的优秀竞品信息，指导Listing优化（包括图片、标题、定价、详情页、评论等因素）。',
      productscope3: '半成熟产品（已有一段投放周期，但订单量增长缓慢）',
      productscope3op:
        '投放 1-2 周后可诊断转化表现，并明确是否需进一步优化转化率。系统将参考AI所探索到的优秀竞品信息，指导Listing优化（包括图片、标题、定价、详情页、评论等因素）。',
      systemprinciple: 'AI系统基本原理',
      systemprinciple1: ' AI核心目标',
      systemprinciple1op: '以低成本、高效率自动挖掘和探索精准流量，快速发现当前市场中性价比最高的流量入口。',
      systemprinciple2: ' AI核心逻辑',
      systemprinciple2op: '通过SP-Asin定向投放至大量相关竞品位置，转化率表现越具竞争力，获取的竞品流量规模越大。',
      systemprinciple3: ' AI成本控制策略',
      systemprinciple3op:
        '在初期探索阶段，系统每日为每个Asin和关键词精确控制曝光量（每日0-50个曝光），迅速筛选出点击率高、转化率强的有效流量入口。',
      systemprinciple4: ' AI关键词选择策略',
      systemprinciple4op:
        '关键词源自实际成单词汇，精准且高效。AI不进行高价排名争抢，自动根据实时性价比数据动态调整预算及出价配置。',
      supportcountry: '支持国家范围',
      supportcountry1: '北美：美国、加拿大',
      supportcountry2: '欧洲：英国、德国',
      supportcountry3: '亚洲：日本、印度、阿联酋',
      supportcountry4: '南美：巴西',
      supportcountry5: '大洋洲：澳大利亚'
    },
    pwdLogin: {
      title: '登录',
      rememberMe: '记住我',
      forgetPassword: '忘记密码？',
      register: '注册账号',
      otherAccountLogin: '其他账号登录',
      otherLoginMode: '其他登录方式',
      superAdmin: '超级管理员',
      admin: '管理员',
      user: '普通用户',
      loginWithGoogle: '使用Google登录',
      newToDeepBIAtlas: '新用户？'
    },
    codeLogin: {
      title: '验证码',
      getCode: '获取验证码',
      reGetCode: '{{time}}秒',
      sendCodeSuccess: '验证码发送成功',
      imageCodePlaceholder: '请输入图片验证码'
    },
    register: {
      title: '注册账号',
      agreement: '我已经仔细阅读并接受',
      protocol: '《用户协议》',
      policy: '《隐私权政策》'
    },
    resetPwd: {
      title: '重置密码',
      resetPassword: '重置你的密码'
    },
    bindWeChat: {
      title: '绑定微信'
    }
  },
  signup: {
    title: '注册账号',
    haveAccount: '已有账号？',
    login: '登录',
    email: '邮箱',
    emailPlaceholder: '请输入邮箱',
    emailRequired: '请输入有效的邮箱地址',
    password: '密码',
    passwordPlaceholder: '请输入密码',
    passwordRequired: '请输入密码',
    passwordTip: '密码需至少8个字符：包含字母、数字和特殊符号',
    repeatPassword: '确认密码',
    repeatPasswordPlaceholder: '请再次输入密码',
    repeatPasswordRequired: '请确认密码',
    passwordMismatch: '两次输入的密码不一致',
    createAccount: '创建账号',
    signupWithGoogle: '使用Google注册'
  },
  about: {
    title: '关于',
    introduction: `DeepBI ATLAS是一个优雅且功能强大的后台管理模板，基于最新的前端技术栈，包括 React18.3, Vite5, TypeScript,ReactRouter6.4,Redux/toolkit 和 UnoCSS。它内置了丰富的主题配置和组件，代码规范严谨，实现了自动化的文件路由系统。此外，它还采用了基于 ApiFox 的在线Mock数据方案。DeepBI ATLAS为您提供了一站式的后台管理解决方案，无需额外配置，开箱即用。同样是一个快速学习前沿技术的最佳实践。`,
    projectInfo: {
      title: '项目信息',
      version: '版本',
      latestBuildTime: '最新构建时间',
      githubLink: 'Github 地址',
      previewLink: '预览地址'
    },
    prdDep: '生产依赖',
    devDep: '开发依赖'
  },
  home: {
    greeting: '早安，{{userName}}, 今天又是充满活力的一天!',
    weatherDesc: '今日多云转晴，20℃ - 25℃!',
    projectCount: '项目数',
    todo: '待办',
    message: '消息',
    Doc: '产品文档',
    downloadCount: '下载量',
    registerCount: '注册量',
    schedule: '作息安排',
    study: '学习',
    work: '工作',
    rest: '休息',
    entertainment: '娱乐',
    visitCount: '访问量',
    turnover: '成交额',
    dealCount: '成交量',
    creativity: '创意',
    dashboard: {
      description: '描述',
      selectDateRange: '请选择日期范围',
      editCoreMetrics: '编辑核心指标',
      maxSelectMetrics: '最多选择6个指标',
      minSelectMetrics: '请至少选择一个指标',
      previousPeriod: '上期',
      noData: '暂无数据',
      metrics: {
        ad_spend: {
          title: 'AI 广告销售额',
          description: '由AI优化的广告所产生的销售额。',
          formula: 'AI 广告销售占比=(AI广告销售额 / 广告销售额)× 100%',
          thirdLine: 'AI 广告销售占比:'
        },
        total_sales: {
          title: '总销售额',
          description: '包括广告和自然流量产生的所有销售额总和。',
          formula: '广告销售额+自然销售额'
        },
        ad_sales_ratio: {
          title: 'AI 广告费用占比',
          description: 'AI广告花费占总广告花费的百分比，反映AI广告在整体广告投入中的比重。',
          formula: '(AI广告花费 / 广告总花费) × 100%',
          thirdLine: '总广告费用:'
        },
        natural_sales: {
          title: 'AI 带动自然流量销售额',
          description: '通过AI广告带动的自然流量销售额，根据AI广告销售额占比计算。',
          formula: '(AI销售额 / SP总销售额) × 自然销售额',
          thirdLine: '占自然流量销售额比例:'
        },
        sales_count: {
          title: 'AI 销售数量',
          description: 'AI广告带来的销售数量，根据AI销量占比计算。',
          formula: '(AI订单数 / 广告总订单数) × 广告总订单数'
        },
        order_count: {
          title: 'AI 订单数',
          description: 'AI广告带来的订单数量。',
          formula: 'AI订单数'
        },
        ad_savings: {
          title: 'AI 节省广告费',
          description: 'AI广告与非AI广告的ACOS差异百分比，反映AI优化带来的广告费用节省。正值表示AI广告更高效。',
          formula: [
            'AI 节省广告费 = (非AI花费 / 非AI销售额 - AI花费/AI销售额) × 100%',
            'ACOS下降 = (上期AI广告花费 - 本期AI广告花费) / 上期广告销售额',
            'AI贡献率 = (上期AI广告花费 - 本期AI广告花费) / (上期广告销售额 - 本期广告销售额)'
          ],
          thirdLine1: 'ACOS下降:',
          thirdLine2: 'AI贡献率:'
        },
        ai_op_count: {
          title: 'AI 运营调优次数',
          description: 'AI系统执行的运营优化操作次数，包括关键词优化、出价调整等。',
          formula: '节省运营工作量 = AI运营调优次数 / 200',
          thirdLine: '节省运营工作量:'
        },
        keyword_create: {
          title: 'AI 选词优化',
          description: 'AI系统执行的关键词选择和优化操作次数。',
          formula: 'AI选词操作的累计次数'
        },
        targeting_create: {
          title: 'AI 自动加ASIN',
          description: 'AI系统自动添加ASIN定向的操作次数。',
          formula: 'AI添加ASIN的累计次数'
        }
      },
      charts: {
        salesTrend: '销售趋势',
        aiVsNonAiRatio: 'AI vs 非 AI 广告销售额占比',
        aiSalesAndCostRatio: 'AI 销售 & 广告费用占比对比',
        aiDrivenOrganicSalesTrend: 'AI 带动自然流量销售额趋势',
        aiSalesQuantityTrend: 'AI 销售数量趋势',
        aiOrderCountTrend: 'AI 订单数趋势',
        aiOptimizationTrend: 'AI 运营调优趋势',
        aiKeywordOptimizationTrend: 'AI 选词优化趋势',
        aiAsinAddingTrend: 'AI 自动加 ASIN 趋势',
        adSavingsTrend: '节省广告费趋势',
        // 图表数据分类
        sales: '销售额',
        aiSales: 'AI 销售额',
        aiAdCost: 'AI 广告费用',
        aiDrivenOrganicSales: 'AI带动自然流量销售额',
        aiAdSales: 'AI广告销售额',
        nonAiAdSales: '非AI广告销售额',
        aiSalesQuantity: 'AI销售数量',
        aiOrderCount: 'AI订单数',
        campaignOptimization: '广告活动优化',
        keywordOptimization: '关键词优化',
        asinOptimization: 'ASIN优化',
        aiKeyword: 'AI选词',
        asinAdding: 'ASIN添加',
        adSavings: '节省广告费'
      }
    }
  },
  aidrivenads: {
    columns: {
      market: '市场',
      date: '日期',
      changeSite: '操作类型',
      campaignName: '广告系列名称',
      content: '内容',
      operate: '操作',
      timeRange: '时间范围'
    },
    title: {
      Impression: '曝光量',
      Clicks: '点击量',
      CTR: '点击率',
      Order: '订单量',
      ConversionRate: '转化率',
      ACOS: '广告投入产出比',
      AdsType: '广告类型占比'
    }
  },
  setting: {
    country: {
      CA: '加拿大',
      US: '美国',
      MX: '墨西哥',
      BR: '巴西',
      ES: '西班牙',
      UK: '英国',
      GB: '英国',
      FR: '法国',
      BE: '比利时',
      NL: '荷兰',
      DE: '德国',
      IT: '意大利',
      SE: '瑞典',
      PL: '波兰',
      EG: '埃及',
      TR: '土耳其',
      SA: '沙特阿拉伯',
      AE: '阿联酋',
      IN: '印度',
      SG: '新加坡',
      AU: '澳大利亚',
      JP: '日本',
      NA: '北美',
      EU: '欧洲',
      IE: '爱尔兰'
    },
    authmodel: {
      region: '区域',
      spauthconfirm: '确认您要授权的店铺以及区域',
      authshop: '授权店铺',
      startdiagnosis: '开始诊断',
      title: '亚马逊授权',
      regiontitle: '请选择帐号的授权区域',
      accountname: '账号名称',
      accountnameplaceholder: '用于卖家区分各个账号',
      formrule: '该项为必填项',
      auth: '授权',
      authsuccess: '授权成功',
      notifyText: '将自动授权该帐号下其余站点(如果已开通):',
      step: '操作步骤',
      step1: '在待授权的亚马逊账号的登录电脑上，登录亚马逊账号和DeepBI ATLAS账号；',
      step3: '选择要授权的站点，然后点击下方的授权按钮；在跳转后的页面点击同意授权即可。',
      step2: '小语种站点如日本，墨西哥等， 需要在亚马逊后台设置报告语言为英文才可正常获取数据',
      howtysetup: '如何设置'
    },
    auth: {
      shop: '店铺',
      Subscribe: '订阅',
      Unsubscribe: '取消订阅',
      country: '国家',
      advertise: '广告授权',
      report: '报告状态',
      shopauth: '店铺授权',
      shopauthtime: '店铺授权时间',
      operation: '操作',
      cancelauth: '取消授权',
      cancelauthdesc: '此操作将取消',
      cancelauthdesc1: '所有ASIN授权，请谨慎操作。',
      updateauth: '更新授权',
      pauseauth: '暂停同步',
      advertiseauth: '跳转亚马逊广告后台授权？',
      advertiseauthdesc: '点击前往将离开ATLAS，跳转亚马逊广告后台进行授权，请确保在常用环境操作，避免关联到其他账号',
      advertiseauthconfirm: '确认您要进行广告授权的店铺以及区域',
      advertiseauthbtn: '前往授权',
      serviceexpire: '服务到期提醒',
      serviceexpire1: '我已知晓',
      serviceexpire2: '前往续费',
      advertisestatus: '广告服务状态',
      cancel: '已取消',
      expired: '已到期',
      active: '已激活',
      inactive: '未激活',
      remove: '天后移除',
      daystodate: '天后到期',
      more: '更多',
      advertisingserviceactivation: '广告服务激活',
      gosite: '前往站点',
      extendadvertisingservice: '延长广告服务时间',
      addshop: '新增店铺',
      editshop: '修改店铺',
      addshopsuccess: '新增店铺成功',
      addshopdesc: '如果您是供应商（VC）账户，请联系销售人员添加！',
      isdistributor: '是否为供应商',
      no: '否',
      yes: '是',
      confirmdelete: '确认删除',
      confirmdelete1: '您确定要删除店铺',
      confirmdelete2: '此操作无法撤销',
      authorizationoperationinstructions: '授权操作说明',
      shopnotauthorized: '店铺未授权',
      shopnotauthorizeddesc: '请先授权店铺',
      modifyshopsuccess: '修改店铺成功',
      deleteshopsuccess: '删除店铺成功',
      switchsiteconfirm: '切换站点确认',
      targetsite: '目标站点',
      switchsiteconfirmtext: '确认要切换到该站点吗？',
      switchsiteconfirmtext1: '切换后将跳转到AI托管Listing页面',
      authnotcomplete: '授权未完成',
      shopauthnotcomplete: '请先完成店铺授权，再进行广告服务激活',
      adsauthnotcomplete: '请先完成广告授权，再进行广告服务激活',
      switchsuccess: '切换成功',
      switchfailed: '切换失败',
      reportLanguage: {
        datacollectionexception: '数据采集异常',
        reportfailed: '数据采集失败处理指引',
        reportfaileddesc: '经系统检测，该店铺',
        reportfailed1: '因报告默认语言未设置为英语，触发数据采集失败。',
        reportfailed2: '请按以下路径更新配置：',
        reportfailed3: '亚马逊后台',
        reportfailed4: '设置',
        reportfailed5: '业务信息',
        reportfailed6: '上传数据处理报告语言',
        reportfailed7: "将'当前默认语言'设置为英语",
        reportfailed8: '操作示例：',
        clicktoview: '点击查看大图',
        example1: '示例1',
        example2: '示例2'
      },
      serviceDescription: {
        title: '服务说明',
        description1: '广告服务激活：激活广告服务后，您可托管Listing。AI将根据设置自动新建广告并优化广告表现。',
        description2: '取消授权的处理：',
        description3:
          '在广告服务期间，可取消站点授权，请先暂停或取消站点内所有AI托管的Listing。待所有广告投放完全暂停后，方可取消授权。',
        description4: '注意：取消授权不会影响广告服务状态。服务激活期间，您可重新授权并继续托管Listing。',
        description5:
          '服务到期的处理：服务到期后，所有托管Listing的广告投放将自动暂停，并在15天后移除所有广告。原有的广告优化策略将不再保留。',
        description6:
          '正常运行广告：在服务未到期或激活状态下，可正常使用AI广告服务，托管Listing以新建广告并保持广告投放。',
        description7: '取消授权的影响：取消站点授权将导致数据与优化策略不可恢复，请谨慎操作。'
      }
    }
  },
  function: {
    tab: {
      tabOperate: {
        title: '标签页操作',
        addTab: '添加标签页',
        addTabDesc: '跳转到关于页面',
        closeTab: '关闭标签页',
        closeCurrentTab: '关闭当前标签页',
        closeAboutTab: '关闭"关于"标签页',
        addMultiTab: '添加多标签页',
        addMultiTabDesc1: '跳转到多标签页页面',
        addMultiTabDesc2: '跳转到多标签页页面(带有查询参数)'
      },
      tabTitle: {
        title: '标签页标题',
        changeTitle: '修改标题',
        change: '修改',
        resetTitle: '重置标题',
        reset: '重置'
      }
    },
    multiTab: {
      routeParam: '路由参数',
      backTab: '返回 function_tab'
    },
    toggleAuth: {
      toggleAccount: '切换账号',
      authHook: '权限钩子函数 `hasAuth`',
      superAdminVisible: '超级管理员可见',
      adminVisible: '管理员可见',
      adminOrUserVisible: '管理员和用户可见'
    },
    request: {
      repeatedErrorOccurOnce: '重复请求错误只出现一次',
      repeatedError: '重复请求错误',
      repeatedErrorMsg1: '自定义请求错误 1',
      repeatedErrorMsg2: '自定义请求错误 2'
    }
  },
  manage: {
    common: {
      status: {
        enable: '启用',
        disable: '禁用'
      }
    },
    role: {
      title: '角色列表',
      roleName: '角色名称',
      roleCode: '角色编码',
      roleStatus: '角色状态',
      roleDesc: '角色描述',
      menuAuth: '菜单权限',
      buttonAuth: '按钮权限',
      form: {
        roleName: '请输入角色名称',
        roleCode: '请输入角色编码',
        roleStatus: '请选择角色状态',
        roleDesc: '请输入角色描述'
      },
      addRole: '新增角色',
      editRole: '编辑角色'
    },
    user: {
      title: '用户列表',
      userName: '用户名',
      userGender: '性别',
      nickName: '昵称',
      userPhone: '手机号',
      userEmail: '邮箱',
      userStatus: '用户状态',
      userRole: '用户角色',
      oldPassword: '旧密码',
      newPassword: '新密码',
      confirmPassword: '确认密码',
      form: {
        userName: '请输入用户名',
        userGender: '请选择性别',
        nickName: '请输入昵称',
        userPhone: '请输入手机号',
        userEmail: '请输入邮箱',
        userStatus: '请选择用户状态',
        userRole: '请选择用户角色',
        oldPasswordPlaceholder: '请输入旧密码',
        newPasswordPlaceholder: '请输入新密码',
        confirmPasswordPlaceholder: '请输入确认密码'
      },
      addUser: '新增用户',
      editUser: '编辑用户',
      gender: {
        male: '男',
        female: '女'
      }
    },
    userDetail: {
      explain: '这个页面仅仅是为了展示 react-router-dom 的 loader 的强大能力，数据是随机的对不上很正常',
      content: `loader 会让网络请求跟懒加载的文件几乎一起发出请求 然后 一边解析懒加载的文件 一边去等待 网络请求
        待到网络请求完成页面 一起显示 配合react的fiber架构 可以做到 用户如果嫌弃等待时间较长 在等待期间用户可以去
        切换不同的页面 这是react 框架和react-router数据路由器的优势 而不用非得等到 页面的显现 而不是常规的
        请求懒加载的文件 - 解析 - 请求懒加载的文件 - 挂载之后去发出网络请求 - 然后渲染页面 - 渲染完成
        还要自己加loading效果`
    },
    menu: {
      home: '首页',
      dashboard: '仪表盘',
      title: '菜单列表',
      id: 'ID',
      parentId: '父级菜单ID',
      menuType: '菜单类型',
      menuName: '菜单名称',
      routeName: '路由名称',
      routePath: '路由路径',
      pathParam: '路径参数',
      layout: '布局',
      page: '页面组件',
      i18nKey: '国际化key',
      icon: '图标',
      localIcon: '本地图标',
      iconTypeTitle: '图标类型',
      order: '排序',
      constant: '常量路由',
      keepAlive: '缓存路由',
      href: '外链',
      hideInMenu: '隐藏菜单',
      activeMenu: '高亮的菜单',
      multiTab: '支持多页签',
      fixedIndexInTab: '固定在页签中的序号',
      query: '路由参数',
      button: '按钮',
      buttonCode: '按钮编码',
      buttonDesc: '按钮描述',
      menuStatus: '菜单状态',
      form: {
        home: '请选择首页',
        menuType: '请选择菜单类型',
        menuName: '请输入菜单名称',
        routeName: '请输入路由名称',
        routePath: '请输入路由路径',
        pathParam: '请输入路径参数',
        page: '请选择页面组件',
        layout: '请选择布局组件',
        i18nKey: '请输入国际化key',
        icon: '请输入图标',
        localIcon: '请选择本地图标',
        order: '请输入排序',
        keepAlive: '请选择是否缓存路由',
        href: '请输入外链',
        hideInMenu: '请选择是否隐藏菜单',
        activeMenu: '请选择高亮的菜单的路由名称',
        multiTab: '请选择是否支持多标签',
        fixedInTab: '请选择是否固定在页签中',
        fixedIndexInTab: '请输入固定在页签中的序号',
        queryKey: '请输入路由参数Key',
        queryValue: '请输入路由参数Value',
        button: '请选择是否按钮',
        buttonCode: '请输入按钮编码',
        buttonDesc: '请输入按钮描述',
        menuStatus: '请选择菜单状态'
      },
      addMenu: '新增菜单',
      editMenu: '编辑菜单',
      addChildMenu: '新增子菜单',
      type: {
        directory: '目录',
        menu: '菜单'
      },
      iconType: {
        iconify: 'iconify图标',
        local: '本地图标'
      }
    }
  },
  table: {
    columns: {
      shopname: '店铺名称',
      country: '国家',
      serviceexpire: '服务到期时间',

      operation: '操作',
      day: '天',
      hour: '小时',
      remaining: '剩余'
    }
  },
  listingall: {
    column: {
      parentAsin: '父ASIN',
      parentAsinExtra: '有库存或销售额',
      price: '价格',
      inventory: '库存',
      totalSales: '总销售额',
      adTotalSales: '广告销售额',
      acos: 'ACOS',
      naturalSalesRatio: '自然销售占比',
      tacos: 'TACOS',
      spAdSalesRatio: 'SP广告销售额占比',
      adTotalCost: '广告总花费',
      totalOrders: '总订单量',
      aiHost: 'AI托管'
    },
    button: {
      batchHost: '批量托管',
      refreshPage: '刷新页面'
    },
    tooltip: {
      clickAsinDetails: '点击查看ASIN详情',
      inventoryLessThan: '库存小于{{limit}}，无法进行托管'
    },
    search: {
      country: '国家',
      selectCountry: '请选择国家',
      dateRange: '时间范围',
      near3Days: '近三天',
      near7Days: '近七天',
      near30Days: '近三十天',
      dataDetails: '数据详情',
      parentAsin: '父ASIN',
      inputParentAsin: '输入父ASIN',
      hostingStatus: '托管状态',
      selectHostingStatus: '选择托管状态',
      all: '全部',
      hosted: '已托管',
      unhosted: '未托管',
      activated: '已激活'
    },
    message: {
      existingHosted: '存在已托管Listing',
      hostSuccess: 'AI托管成功'
    },
    pagination: {
      total: '共 {{total}} 条'
    },
    dashboard: {
      dataOverview: '数据总览',
      dataCollecting: '数据正在采集中，请稍后',
      dataCollectionGuide: '数据采集说明',
      loadingChart: '正在加载图表...',
      reload: '重新加载',
      totalSales: '总销售额',
      adTotalSales: '广告总销售额',
      adImpressions: '广告展现量',
      naturalSalesRatio: '自然销售额占比',
      adSpend: '广告花费',
      totalOrders: '总订单量',
      naturalSales: '自然销售额',
      clicks: '点击量',
      orders: '订单量',
      ctr: '点击率',
      conversionRate: '转化率'
    },
    notice: {
      adStatsNoSb: '！商品中心广告数据统计不包括SB广告数据',
      recentDataBias: '！受Amazon官方数据影响，近三天数据可能存在偏差',
      minimumUnit: '！DeepBI Atlas系统的授权最小单位是父ASIN'
    },
    modal: {
      title: 'AI广告诊断',
      currentSite: '当前诊断站点：',
      remainingCount: '本月剩余诊断次数：',
      info: '此操作将对所选站点的所有Listing进行广告诊断，需要等待5-10分钟。\n诊断过程可能耗费一定时间，请勿频繁操作。如诊断完成后需查看详细数据，请前往站点诊断记录页面',
      insufficient: '当前站点诊断次数不足，如需诊断请联系销售~',
      generating: '当前站点诊断报告正在生成中，您可前往AI诊断报告页面查看',
      createSuccess: '创建诊断任务成功,请前往AI诊断报告页面查看'
    }
  },
  ailisting: {
    title: 'AI托管列表',
    columns: {
      parentAsin: '父ASIN',
      totalSales: '总销售额',
      adSales: '广告销售额',
      adSpend: '广告总花费',
      organicSales: '自然销售额',
      organicSalesRatio: '自然销售占比',
      tacos: 'TACOS',
      aiAcosSp: 'AI ACOS(SP)',
      aiAdSpendSp: 'AI广告花费(SP)',
      nonAiAcosSp: '非AI ACOS(SP)',
      spAdSalesRatio: 'SP广告销售额占比',
      aiAdSales: 'AI广告销售额',
      aiSalesRatio: 'AI销售额占比',
      aiOrderCount: 'AI订单量',
      keywordSeedCount: '关键词种子数',
      asinFlowSeedCount: 'ASIN流量种子数',
      acos: 'ACOS',
      aiDailyBudget: 'AI日预算',
      aiCampaignCount: 'AI广告活动数量',
      targetAcos: '目标ACOS',
      status: '状态',
      operations: '操作'
    },
    status: {
      preparing: '准备中',
      running: '投放中',
      pausing: '暂停中',
      paused: '已暂停',
      cancelling: '取消中',
      recovering: '恢复中',
      cancelled: '已取消授权',
      hosted: '托管'
    },
    tooltips: {
      totalSales: '广告销售额 + 自然销售额',
      adSales: 'AI广告销售额 + 非AI广告销售额',
      adSpend: 'AI广告花费 + 非AI广告花费',
      organicSales: '总销售额 - 广告销售额',
      organicSalesRatio: '自然销售额 / 总销售额',
      tacos: '(AI广告花费 + 非AI广告花费) / (广告销售额 + 自然销售额)',
      aiAcosSp: 'AI SP广告花费 / AI SP广告销售额',
      aiSalesRatio: 'AI广告销售额 / (AI广告销售额 + 非AI广告销售额)',
      keywordSeedCount: '在投开启和关闭状态下的关键词数',
      asinFlowSeedCount: '在投开启和关闭状态下的ASIN数',
      acos: '(AI广告花费 + 非AI广告花费) / 广告销售额',
      targetAcos: '默认为全店ACOS值设置，如有需要，可以对单独的ACOS进行设置。',
      targetAcosStrategy: '优先以整体预期ACOS进行，单独ASIN的会额外做策略补充',
      statusExplanation: 'ASIN投放状态',
      errorPrompt: '错误提示',
      aiDailyBudget: 'AI广告活动日预算',
      aiAdSales: 'AI广告SP销售额',
      nonAiAcosSp: '非AI SP广告的花费 / 非AI SP广告的销售额',
      spAdSalesRatio: '所有SP广告销售额 / 广告销售额',
      aiOrderCount: 'AI广告活动订单量',
      aiExploreKeywords: 'AI会逐步探索增加关键词数量'
    },
    statusDescriptions: {
      preparing: 'AI托管后,在24小时内完成广告新建和优化策略准备。',
      running: '广告新建完成后开始投放,期间DeepBI会逐步探索增加关键词及ASIN。',
      pausing: '广告投放正在暂停中。',
      paused: '广告投放暂停完成,操作点击恢复即可恢复广告投放。',
      cancelling: 'DeepBI更改广告命名,终止广告优化策略,暂停广告投放,完成取消Listing托管。',
      recovering: '广告投放正在恢复,完成后广告投放和优化策略正常进行。'
    },
    buttons: {
      timeBudget: '分时预算',
      aiDiagnosis: 'AI 诊断Listing',
      setTargetAcos: '设置预期ACOS',
      batchOperations: '批量操作',
      addHostedAsin: '新增托管ASIN',
      hostingGuide: '托管操作说明',
      refreshPage: '刷新页面',
      goToListingPage: '前往Listing列表批量托管',
      adCampaignList: '广告活动列表'
    },
    dropdownMenu: {
      setAcos: '设置目标ACOS',
      cancelHosting: '取消托管',
      pauseDelivery: '暂停投放',
      resumeDelivery: '恢复投放'
    },
    confirmDialogs: {
      cancelHosting: {
        title: '确定取消托管吗？',
        description: '取消托管后，将终止AI优化服务，更改广告命名，暂停广告投放。如需继续优化，您将只能重新托管。'
      },
      pauseDelivery: {
        title: '确定暂停投放吗？',
        description: '是否确认暂停广告投放？'
      }
    },
    asinStatus: {
      outOfStock: '库存不足',
      highAcos: 'ACOS过高',
      lowKeywords: '关键词少且ACOS高',
      lowKeywordsGoodAcos: '关键词少但ACOS良好',
      matureGoodAcos: '关键词丰富且ACOS良好'
    },
    acosStrategy: {
      ultraConservative: '≤ 16%',
      moderatelyConservative: '≤ 20%',
      conservative: '≤ 24%',
      balanced: '≤ 28%',
      aggressive: '≤ 32%',
      ultraAggressive: '≤ 50%',
      custom: '自定义≤'
    },
    messages: {
      settingSuccess: '设置成功',
      settingFailed: '设置失败',
      cancelSuccess: '取消成功',
      pauseSuccess: '暂停成功',
      resumeSuccess: '恢复成功',
      selectRunningOrPausedOnly: '请只选择状态为投放中或已暂停的ASIN',
      selectRunningOnly: '请只选择状态为投放中的ASIN',
      selectPausedOnly: '请只选择状态为已暂停的ASIN'
    },
    colorExplanation: {
      defaultSorting: '*排序默认为广告投放颜色分类排序',
      tableColumns: {
        colorCategory: '颜色分类',
        acosCondition: 'Acos条件',
        trafficUnitCondition: '曝光单位条件',
        strategy: '策略'
      },
      items: {
        lowAcosHighTraffic: {
          acos: 'Acos值 ≤ 24%',
          condition: '流量单位充足',
          strategy: '拉高流量单位，增加预算'
        },
        lowAcosLowTraffic: {
          acos: 'Acos值 ≤ 24%',
          condition: '流量单位少',
          strategy: '探索更多流量单位，增加流量单位'
        },
        highAcosHighTraffic: {
          acos: 'Acos值 > 24%',
          condition: '流量单位充足',
          strategy: '压低ACOS值，少量增加流量单位'
        },
        highAcosLowTraffic: {
          acos: 'Acos值 > 24%',
          condition: '流量单位少',
          strategy: '低预算探索，控制Acos值'
        },
        lowInventory: {
          acos: '无具体Acos值条件',
          condition: '库存不足或非应季商品',
          strategy: '极低预算，避免浪费'
        },
        dataCollecting: {
          acos: '无具体Acos值条件',
          condition: '数据采集中或准备中',
          strategy: '--'
        }
      },
      trafficUnitDefinition: '流量单位定义：流量单位为在投词和ASIN数量总和，一个词/ASIN有三个流量单位。',
      sufficientTrafficDefinition: '流量单位充足定义：超过150个流量单位即为流量单位充足。',
      colorClassificationBasis: '广告投放颜色分类以DeepBI计划 SP ACOS值为基准。'
    },
    acosSettingModal: {
      title: 'AI智能托管 - ACOS目标设置',
      targetAcosValue: '目标ACOS值：',
      customAcos: '自定义ACOS ≤ {{value}} %',
      optimizationStrategy: 'AI优化策略说明：',
      strategyPoints: {
        siteBased: '基于站点的ACOS目标，可以对特定Listing进行单独设置，以实现更精细的广告管理。',
        referenceIndicator: 'Listing的ACOS目标将作为AI托管的重要参考指标，请避免频繁修改。'
      },
      customRangeNotice: '自定义预期ACOS值范围：16% ~ 50%'
    },
    aiDiagnosisModal: {
      title: 'AI 诊断Listing（beta）',
      remainingDiagnosis: '本月剩余诊断次数：',
      yourProductAsin: '你的产品ASIN',
      enterYourProductAsin: '请输入你的产品ASIN',
      competitorAsin: '竞品对比ASIN',
      enterCompetitorAsin: '请输入竞品对比ASIN',
      startDiagnosis: '启动AI智能诊断',
      betaVersionNote: 'beta版本 最多只能保留一个报告，如果重新诊断，会覆盖之前的报告',
      reportDownload: '报告下载',
      diagnosisInProgress: '(AI 诊断中，约15分钟生成，期间可离线处理)',
      diagnosisFailed: '(AI 诊断失败，请重新诊断，此次诊断不会扣除次数)',
      clickToDownload: '点击下载报告',
      diagnosisFailedNoDeduction: '诊断失败不会扣除诊断次数，可重新尝试'
    },
    asinStatusSetter: {
      title: '子ASIN投放状态',
      childAsinManagement: '子ASIN管理',
      inputAsin: '输入ASIN',
      adStatus: '广告状态',
      statusOptions: {
        all: '全部',
        enabled: '开启',
        disabled: '关闭',
        processing: '处理中'
      },
      columns: {
        asin: 'ASIN',
        adDeliveryStatus: '广告投放状态',
        operation: '操作'
      },
      price: '价格',
      inventory: '库存',
      processing: '处理中',
      confirmDelete: '确定删除此ASIN吗?',
      operationInProgress: '{{asin}} 操作正在处理中，请稍候...',
      statusSubmitted: '{{asin}} 状态已提交，请等待AI处理',
      totalItems: '共 {{total}} 条',
      batchEnabled: '已开启所选ASIN',
      batchDisabled: '已关闭所选ASIN',
      batchOperationPartialFailed: '部分操作可能未成功，请检查状态',
      batchOperationFailed: '批量设置状态失败',
      selectAsin: '请先选择ASIN'
    },
    competitorStrategy: {
      title: '竞品策略配置',
      competitorStrategy: '竞品策略',
      categories: {
        asin: '竞品ASIN',
        keyword: '关键词',
        history: '历史记录'
      },
      subCategories: {
        positive: {
          asin: '投放竞品ASIN',
          keyword: '投放关键词'
        },
        negative: {
          asin: '否定竞品ASIN',
          keyword: '否定关键词'
        }
      },
      searchPlaceholders: {
        asin: '搜索ASIN',
        keyword: '搜索关键词',
        history: '搜索ASIN/关键词'
      },
      addButton: {
        asin: '添加ASIN',
        keyword: '添加关键词'
      },
      tableColumns: {
        asinKeyword: 'ASIN/关键词',
        addTime: '添加时间',
        addAccount: '添加账户',
        operation: '操作',
        operationType: '操作类型'
      },
      emptyText: {
        asin: '暂无竞品ASIN',
        negativeAsin: '暂无否定竞品ASIN',
        keyword: '暂无投放关键词',
        negativeKeyword: '暂无否定关键词',
        history: '暂无历史记录'
      },
      confirmDelete: {
        asin: '确定删除此ASIN吗?',
        keyword: '确定删除此关键词吗?'
      },
      addModal: {
        titlePositiveAsin: '添加竞品ASIN',
        titleNegativeAsin: '添加否定竞品ASIN',
        titlePositiveKeyword: '添加投放关键词',
        titleNegativeKeyword: '添加否定关键词',
        placeholder: {
          asin: '在这里输入ASIN，每行一个，一次可以批量添加10个，可以多次添加',
          keyword: '在这里输入关键词，每行一个，一次可以批量添加10个，可以多次添加'
        }
      },
      guides: {
        title: '操作指南',
        positiveAsin: 'AI系统具有自动识别和添加竞品ASIN的功能，手动添加竞品ASIN将作为竞品探索的补充，以提升广告效果',
        negativeAsinPoints: [
          '优先依赖AI系统自动否定竞品ASIN，手动操作仅用于补充',
          '否定竞品ASIN对应亚马逊广告后台的"精准否定"功能'
        ],
        negativeWarning: {
          title: '添加否定ASIN前，请务必仔细确认：',
          description: '被否定的ASIN与您推广的商品不存在替代/互补关系。如果将有关联的竞品ASIN加入否定列表，将导致：',
          consequences: ['系统无法触达相关搜索流量', '错失跨商品关联推荐位曝光', '降低广告组整体转化率']
        },
        operationLimit: {
          title: '操作限制：',
          description:
            '单次手动否定不超过10个ASIN，避免过度限制AI系统的优化空间，否则将导致AI模型学习数据量骤减，优化效果下降'
        },
        positiveKeyword: 'AI系统具有自动识别和添加关键词的功能，手动添加关键词将作为关键词探索的补充，以提升广告效果',
        negativeKeywordPoints: [
          '优先依赖AI系统自动否定关键词，手动操作仅用于补充',
          '否定关键词对应亚马逊广告后台的"精准否定"功能'
        ],
        negativeKeywordWarning: {
          title: '添加否定关键词前，请务必仔细确认：',
          description: '被否定的关键词与您推广的商品不存在关联关系。如果将有关联的关键词加入否定列表，将导致：',
          consequences: ['系统无法触达相关搜索流量', '降低广告组整体转化率']
        }
      },
      operationTypes: {
        all: '全部',
        add: '添加',
        delete: '删除'
      },
      processing: '处理中'
    }
  },
  mobileDetector: {
    title: '温馨提示',
    description: '请在电脑端访问此页面以获得最佳体验。',
    description1: '建议使用分辨率 1920*1080 及以上的显示器',
    copy: '复制链接',
    copySuccess: '复制成功',
    copyFailed: '复制失败'
  }
};

export default page;
