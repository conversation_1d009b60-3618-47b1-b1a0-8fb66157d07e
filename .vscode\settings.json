{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "eslint.useFlatConfig": true, "editor.guides.bracketPairs": "active", "editor.formatOnSave": false, "eslint.validate": ["html", "css", "scss", "json", "jsonc"], "i18n-ally.displayLanguage": "zh-cn", "i18n-ally.enabledParsers": ["ts"], "i18n-ally.enabledFrameworks": ["react-i18next"], "i18n-ally.editor.preferEditor": true, "i18n-ally.translate.engines": ["deepl", "google"], "i18n-ally.keystyle": "nested", "i18n-ally.extract.targetPickingStrategy": "most-similar", "i18n-ally.pathMatcher": "{locale}/{namespaces}.ts", "i18n-ally.localesPaths": ["src/locales/langs"], "prettier.enable": false, "typescript.tsdk": "node_modules/typescript/lib", "unocss.root": ["./"], "i18n-ally.sourceLanguage": "zh-cn"}