import React from 'react';
import { getCurrencySymbol, getCurrency } from "@/components/weekly-vchart/chart";

interface CurrencyUnitProps {
  value: string | number;
  countryCode?: string;
  className?: string;
}

const CurrencyUnit: React.FC<CurrencyUnitProps> = ({ 
  value, 
  countryCode = 'US',
  className = ''
}) => {
  // 如果值是null、undefined或者'-'，直接返回'-'
  if (!value || value === '-' || value === 'None') {
    return <span className={className}>-</span>;
  }

  const stringValue = String(value);

  // 检查是否包含百分号
  const isPercentage = stringValue.includes('%');

  // 如果是百分比，直接返回原值
  if (isPercentage) {
    return <span className={className}>{stringValue}</span>;
  }

  // 检查是否为数字
  const numericValue = Number(stringValue);
  if (isNaN(numericValue)) {
    return <span className={className}>{stringValue}</span>;
  }

  // 格式化数字为两位小数
  const formattedValue = numericValue.toFixed(2);
  
  const currencyCode = countryCode ? getCurrency(countryCode) || '' : '';
  // 获取货币符号
  const currencySymbol = getCurrencySymbol(currencyCode);
  
  return (
    <span className={className}>
      {currencySymbol}{formattedValue}
    </span>
  );
};

export default CurrencyUnit;