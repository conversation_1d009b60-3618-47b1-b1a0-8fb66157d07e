const page: App.I18n.Schema['translation']['page'] = {
  login: {
    common: {
      loginOrRegister: 'Login / Register',
      userNamePlaceholder: 'Please enter user name',
      phonePlaceholder: 'Please enter phone number',
      companyNamePlaceholder: 'Please enter company name',
      codePlaceholder: 'Please enter verification code',
      switching: 'Switching',
      emailPhonePlaceholder: 'Please enter email/phone number',
      captcha: 'Captcha',
      captchaPlaceholder: 'Please enter captcha',
      passwordPlaceholder: 'Please enter password',
      confirmPasswordPlaceholder: 'Please enter password again',
      codeLogin: 'Verification code login',
      confirm: 'Confirm',
      back: 'Back',
      validateSuccess: 'Verification passed',
      loginSuccess: 'Login successfully',
      welcomeBack: 'Welcome back,',
      passwordRule:
        'Please enter a password with at least 8 characters, including uppercase and lowercase letters and numbers',
      agree: 'Please check the agreement',
      agreeText: 'Read and agree to',
      agreeLink: 'ATLAS Software Terms and Conditions',
      switchshop: 'Switch Shop'
    },
    description: {
      // Amazon全托管AI广告助手
      title: 'Amazon Full-Service AI Advertising Assistant',
      description: 'Amazon Official Certified SPN Service Provider',
      serviceprice: 'Service Price',
      price: '$299/Country/Month',
      servicepriceunit: '(Up to 50 Listings can be managed per country)',
      applyseller: 'Applicable Seller Types',
      applysellerdesc: 'Premium sellers, Multi-store sellers, High-volume sellers',
      productscope: 'Product Application Range',
      productscope1:
        'Mature Products (with stable order accumulation, competitive conversion rate, significantly outperforming 30% of similar products)',
      productscope1op: 'Noticeable data optimization results can be achieved in about 2 weeks',
      productscope2: 'New Products (newly launched, conversion rate uncertain)',
      productscope2op:
        'After 1-2 weeks of advertising, conversion performance can be diagnosed and determine if further optimization is needed. The system will guide Listing optimization based on AI-discovered competitor information (including images, titles, pricing, detail pages, reviews, etc.)',
      productscope3: 'Semi-mature Products (has advertising history but slow order growth)',
      productscope3op:
        'After 1-2 weeks of advertising, conversion performance can be diagnosed and determine if further optimization is needed. The system will guide Listing optimization based on AI-discovered competitor information (including images, titles, pricing, detail pages, reviews, etc.)',
      systemprinciple: 'AI System Basic Principles',
      systemprinciple1: 'AI Core Objective',
      systemprinciple1op:
        'Automatically discover and explore precise traffic with low cost and high efficiency, quickly finding the most cost-effective traffic sources in the current market.',
      systemprinciple2: 'AI Core Logic',
      systemprinciple2op:
        'Through SP-Asin targeting to multiple relevant competitor positions, the more competitive the conversion rate performance, the larger the competitor traffic scale obtained.',
      systemprinciple3: 'AI Cost Control Strategy',
      systemprinciple3op:
        'During the initial exploration phase, the system precisely controls daily impressions for each Asin and keyword (0-50 impressions per day), quickly identifying effective traffic sources with high click-through and conversion rates.',
      systemprinciple4: 'AI Keyword Selection Strategy',
      systemprinciple4op:
        'Keywords come from actual order vocabulary, precise and efficient. AI does not compete for high-price rankings, automatically adjusts budget and bid configurations based on real-time cost-effectiveness data.',
      supportcountry: 'Supported Countries',
      supportcountry1: 'North America: United States, Canada',
      supportcountry2: 'Europe: United Kingdom, Germany',
      supportcountry3: 'Asia: Japan, India, UAE',
      supportcountry4: 'South America: Brazil',
      supportcountry5: 'Oceania: Australia'
    },
    pwdLogin: {
      title: 'Login',
      rememberMe: 'Remember me',
      forgetPassword: 'Forget password?',
      register: 'Register',
      otherAccountLogin: 'Other Account Login',
      otherLoginMode: 'Other Login Mode',
      superAdmin: 'Super Admin',
      admin: 'Admin',
      user: 'User',
      loginWithGoogle: 'Log in with Google',
      newToDeepBIAtlas: 'New to DeepBI ATLAS?'
    },
    codeLogin: {
      title: 'Verification Code',
      getCode: 'Send Code',
      reGetCode: '{{time}}s',
      sendCodeSuccess: 'Verification code sent successfully',
      imageCodePlaceholder: 'Please enter image verification code'
    },
    register: {
      title: 'Register',
      agreement: 'I have read and agree to',
      protocol: '《User Agreement》',
      policy: '《Privacy Policy》'
    },
    resetPwd: {
      title: 'Reset Password',
      resetPassword: 'Reset your Password'
    },
    bindWeChat: {
      title: 'Bind WeChat'
    }
  },
  signup: {
    title: 'Sign up',
    haveAccount: 'Have an account?',
    login: 'Login',
    email: 'EMAIL',
    emailPlaceholder: 'Email',
    emailRequired: 'Please enter a valid email',
    password: 'PASSWORD',
    passwordPlaceholder: 'Password',
    passwordRequired: 'Password field is required',
    passwordTip: 'Password should be 8+ characters: letters, numbers, and special characters.',
    repeatPassword: 'REPEAT PASSWORD',
    repeatPasswordPlaceholder: 'Repeat Password',
    repeatPasswordRequired: 'Please confirm the password',
    passwordMismatch: 'The two passwords do not match',
    createAccount: 'Create Account',
    signupWithGoogle: 'Sign up with Google'
  },
  about: {
    title: 'About',
    introduction: `DeepBI ATLASis an elegant and powerful admin template, based on the latest front-end technology stack, including React18.3, Vite5, TypeScript, ReactRouter6.4,Redux/toolkitand UnoCSS. It has built-in rich theme configuration and components, strict code specifications, and an automated file routing system. In addition, it also uses the online mock data solution based on ApiFox. DeepBI ATLASprovides you with a one-stop admin solution, no additional configuration, and out of the box. It is also a best practice for learning cutting-edge technologies quickly.`,
    projectInfo: {
      title: 'Project Info',
      version: 'Version',
      latestBuildTime: 'Latest Build Time',
      githubLink: 'Github Link',
      previewLink: 'Preview Link'
    },
    prdDep: 'Production Dependency',
    devDep: 'Development Dependency'
  },
  home: {
    greeting: 'Good morning, {{userName}}, today is another day full of vitality!',
    weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
    projectCount: 'Project Count',
    todo: 'Todo',
    message: 'Message',
    Doc: 'Doc',
    downloadCount: 'Download Count',
    registerCount: 'Register Count',
    schedule: 'Work and rest Schedule',
    study: 'Study',
    work: 'Work',
    rest: 'Rest',
    entertainment: 'Entertainment',
    visitCount: 'Visit Count',
    turnover: 'Turnover',
    dealCount: 'Deal Count',
    creativity: 'Creativity',
    dashboard: {
      description: 'Description',
      selectDateRange: 'Please select date range',
      editCoreMetrics: 'Edit Core Metrics',
      maxSelectMetrics: 'Select up to 6 metrics',
      minSelectMetrics: 'Please select at least one metric',
      previousPeriod: 'Previous period',
      noData: 'No data',
      metrics: {
        ad_spend: {
          title: 'AI Ad Sales',
          description: 'Sales generated by AI-optimized ads.',
          formula: 'AI Ad Sales Ratio = (AI Ad Sales / Ad Sales) × 100%',
          thirdLine: 'AI Ad Sales Ratio:'
        },
        total_sales: {
          title: 'Total Sales',
          description: 'Sum of all sales from both ads and organic traffic.',
          formula: 'Ad Sales + Organic Sales'
        },
        ad_sales_ratio: {
          title: 'AI Ad Cost Ratio',
          description:
            'Percentage of AI ad spending in total ad spending, reflecting the proportion of AI ads in overall ad investment.',
          formula: '(AI Ad Spend / Total Ad Spend) × 100%',
          thirdLine: 'Total Ad Cost:'
        },
        natural_sales: {
          title: 'AI-Driven Organic Sales',
          description: 'Organic sales driven by AI ads, calculated based on AI ad sales ratio.',
          formula: '(AI Sales / SP Total Sales) × Organic Sales',
          thirdLine: 'Percentage of Organic Sales:'
        },
        sales_count: {
          title: 'AI Sales Quantity',
          description: 'Sales quantity from AI ads, calculated based on AI sales ratio.',
          formula: '(AI Order Count / Total Ad Orders) × Total Ad Orders'
        },
        order_count: {
          title: 'AI Order Count',
          description: 'Number of orders from AI ads.',
          formula: 'AI Order Count'
        },
        ad_savings: {
          title: 'AI Ad Cost Savings',
          description:
            'Percentage difference in ACOS between AI and non-AI ads, reflecting cost savings from AI optimization. A positive value indicates more efficient AI ads.',
          formula: [
            'AI Ad Savings = (Non-AI Spend / Non-AI Sales - AI Spend / AI Sales) × 100%',
            'ACOS Reduction = (Previous Period AI Ad Spend - Current Period AI Ad Spend) / Previous Period Ad Sales',
            'AI Contribution Rate = (Previous Period AI Ad Spend - Current Period AI Ad Spend) / (Previous Period Ad Sales - Current Period Ad Sales)'
          ],
          thirdLine1: 'ACOS Red.:',
          thirdLine2: 'AI Contrib.:'
        },
        ai_op_count: {
          title: 'AI Operation Optimization',
          description:
            'Number of operational optimization actions performed by the AI system, including keyword optimization, bid adjustments, etc.',
          formula: 'Operational Workload Saving = AI Operation Optimization Count / 200',
          thirdLine: 'Op Workload Saved:'
        },
        keyword_create: {
          title: 'AI Keyword Optimization',
          description: 'Number of keyword selection and optimization operations performed by the AI system.',
          formula: 'Cumulative count of AI keyword operations'
        },
        targeting_create: {
          title: 'AI Auto ASIN Targeting',
          description: 'Number of ASIN targeting operations automatically performed by the AI system.',
          formula: 'Cumulative count of AI ASIN targeting additions'
        }
      },
      charts: {
        salesTrend: 'Sales Trend',
        aiVsNonAiRatio: 'AI vs Non-AI Ad Sales Ratio',
        aiSalesAndCostRatio: 'AI Sales & Ad Cost Ratio',
        aiDrivenOrganicSalesTrend: 'AI-Driven Organic Sales Trend',
        aiSalesQuantityTrend: 'AI Sales Quantity Trend',
        aiOrderCountTrend: 'AI Order Count Trend',
        aiOptimizationTrend: 'AI Optimization Trend',
        aiKeywordOptimizationTrend: 'AI Keyword Optimization Trend',
        aiAsinAddingTrend: 'AI ASIN Adding Trend',
        adSavingsTrend: 'Ad Cost Savings Trend',
        // 图表数据分类
        sales: 'Sales',
        aiSales: 'AI Sales',
        aiAdCost: 'AI Ad Cost',
        aiDrivenOrganicSales: 'AI-Driven Organic Sales',
        aiAdSales: 'AI Ad Sales',
        nonAiAdSales: 'Non-AI Ad Sales',
        aiSalesQuantity: 'AI Sales Quantity',
        aiOrderCount: 'AI Orders',
        campaignOptimization: 'Campaign Optimization',
        keywordOptimization: 'Keyword Optimization',
        asinOptimization: 'ASIN Optimization',
        aiKeyword: 'AI Keywords',
        asinAdding: 'ASIN Adding',
        adSavings: 'Ad Cost Savings'
      }
    }
  },
  aidrivenads: {
    columns: {
      market: 'Market',
      date: 'Date',
      changeSite: 'Change Site',
      campaignName: 'Campaign Name',
      content: 'Content',
      operate: 'Operate',
      timeRange: 'Time Range'
    },
    title: {
      Impression: 'Impression',
      Clicks: 'Clicks',
      CTR: 'CTR',
      Order: 'Order',
      ConversionRate: 'Conversion Rate',
      ACOS: 'ACOS',
      AdsType: 'Ads type'
    }
  },
  setting: {
    country: {
      CA: 'Canada',
      US: 'United States',
      MX: 'Mexico',
      BR: 'Brazil',
      ES: 'Spain',
      UK: 'United Kingdom',
      GB: 'United Kingdom',
      FR: 'France',
      BE: 'Belgium',
      NL: 'Netherlands',
      DE: 'Germany',
      IT: 'Italy',
      SE: 'Sweden',
      PL: 'Poland',
      EG: 'Egypt',
      TR: 'Turkey',
      SA: 'Saudi Arabia',
      AE: 'United Arab Emirates',
      IN: 'India',
      SG: 'Singapore',
      AU: 'Australia',
      JP: 'Japan',
      NA: 'North America',
      EU: 'Europe',
      IE: 'Ireland'
    },
    authmodel: {
      region: 'Region',
      spauthconfirm: 'Confirm the shop and region you want to authorize',
      authshop: 'Authorize Shop',
      startdiagnosis: 'Start Diagnosis',
      title: 'Amazon Authorization',
      regiontitle: 'Please select the region of the account',
      accountname: 'Account Name',
      accountnameplaceholder: 'Used to distinguish different accounts',
      formrule: 'This is a required field',
      auth: 'Auth',
      authsuccess: 'Authorization Success',
      notifyText: 'The account will be automatically authorized for all other sites (if they are open):',
      step: 'Operation Steps',
      step1:
        'Log in to the computer of the Amazon account to be authorized on the account in the login computer, log in to the Amazon account and DeepBI ATLAS account;',
      step3:
        'Select the site to be authorized, then click the authorization button; In the page that is redirected, click authorize to complete the authorization.',
      step2:
        'Small language sites such as Japan and Mexico need to set the report language to English in the Amazon background to obtain normal data.',
      howtysetup: 'How to set?'
    },
    auth: {
      country: 'Country',
      Subscribe: 'Activate AI Ads',
      Unsubscribe: 'Cancel Subscription',
      advertise: 'Ads Authorization',
      report: 'Report Status',
      shop: 'Store',
      shopauth: 'Store Authorization',
      shopauthtime: 'Authorization Time',
      operation: 'Operation',
      cancelauth: 'Cancel Authorization',
      cancelauthdesc: 'This operation will cancel',
      cancelauthdesc1: 'All ASIN authorization, please be careful.',
      updateauth: 'Update Authorization',
      pauseauth: 'Pause Sync',
      advertiseauth: 'Jump to Amazon Advertising Background Authorization?',
      advertiseauthdesc:
        'Click to go to the Amazon Advertising Background to authorize, please ensure to operate in a common environment, avoid associating with other accounts',
      advertiseauthconfirm: 'Confirm the shop and region you want to authorize',
      advertiseauthbtn: 'Go to Authorization',
      serviceexpire: 'Service Expiration Reminder',
      serviceexpire1: 'I know',
      serviceexpire2: 'Go to Renew',
      advertisestatus: 'AI Service Status',
      cancel: 'Canceled',
      expired: 'Expired',
      active: 'Active',
      inactive: 'Inactive',
      remove: 'Days to Remove',
      daystodate: 'Days to Date',
      more: 'More',
      advertisingserviceactivation: 'Ad Service Activation',
      gosite: 'Go to Site',
      extendadvertisingservice: 'Extend Advertising Service',
      addshop: 'Add Shop',
      editshop: 'Edit Shop',
      addshopsuccess: 'Add Shop Success',
      addshopdesc: 'If you are a supplier (VC) account, please contact the sales team to add!',
      isdistributor: 'Is VC Account',
      no: 'No',
      yes: 'Yes',
      confirmdelete: 'Confirm Delete',
      confirmdelete1: 'You are sure to delete the shop',
      confirmdelete2: 'This operation cannot be reversed',
      authorizationoperationinstructions: 'Authorization Help',
      shopnotauthorized: 'Shop Not Authorized',
      shopnotauthorizeddesc: 'Please authorize the shop first',
      modifyshopsuccess: 'Modify Shop Success',
      deleteshopsuccess: 'Delete Shop Success',
      switchsiteconfirm: 'Switch Site Confirm',
      targetsite: 'Target Site',
      switchsiteconfirmtext: 'Are you sure to switch to this site?',
      switchsiteconfirmtext1: 'After switching, you will be redirected to the AI Listing page',
      authnotcomplete: 'Authorization Not Completed',
      shopauthnotcomplete: 'Please complete the shop authorization first, then activate the ad service',
      adsauthnotcomplete: 'Please complete the ad authorization first, then activate the ad service',
      switchsuccess: 'Switch Success',
      switchfailed: 'Switch Failed',
      reportLanguage: {
        datacollectionexception: 'Collection Exception',
        reportfailed: 'Data Collection Failure Processing Guide',
        reportfaileddesc: 'After system detection, the shop',
        reportfailed1:
          'Due to the default language of the report not being set to English, the data collection failed.',
        reportfailed2: 'Please update the configuration as follows:',
        reportfailed3: 'Amazon Background',
        reportfailed4: 'setting',
        reportfailed5: 'business information',
        reportfailed6: 'Upload Data Processing Report Language',
        reportfailed7: "Set the 'Current Default Language' to English",
        reportfailed8: 'Operation Example:',
        clicktoview: 'Click to View Large Image',
        example1: 'Example 1',
        example2: 'Example 2'
      },
      serviceDescription: {
        title: 'Service Description',
        description1:
          'Ad Service Activation: After activating the ad service, you can manage listings. AI will automatically create ads and optimize ad performance based on settings.',
        description2: 'Cancel Authorization Process:',
        description3:
          'During the ad service period, you can cancel the site authorization. Please first pause or cancel all AI-managed listings in the site. Once all ad placements are fully paused, you can cancel the authorization.',
        description4: 'Note: Canceling authorization does not affect the ad service status.',
        description5:
          'Service Expiration Process: After the ad service expires, all AI-managed listings will automatically pause advertising, and all ads will be removed after 15 days.',
        description6:
          'Normal Running Ads: In the ad service is not expired or activated, you can use the AI ad service normally, the AI will continue to create ads and keep the ad placement.',
        description7:
          'Cancel Authorization Impact: The data and optimization strategies cannot be recovered after canceling the site authorization, please be careful.'
      }
    }
  },
  function: {
    tab: {
      tabOperate: {
        title: 'Tab Operation',
        addTab: 'Add Tab',
        addTabDesc: 'To about page',
        closeTab: 'Close Tab',
        closeCurrentTab: 'Close Current Tab',
        closeAboutTab: 'Close "About" Tab',
        addMultiTab: 'Add Multi Tab',
        addMultiTabDesc1: 'To MultiTab page',
        addMultiTabDesc2: 'To MultiTab page(with query params)'
      },
      tabTitle: {
        title: 'Tab Title',
        changeTitle: 'Change Title',
        change: 'Change',
        resetTitle: 'Reset Title',
        reset: 'Reset'
      }
    },
    multiTab: {
      routeParam: 'Route Param',
      backTab: 'Back function_tab'
    },
    toggleAuth: {
      toggleAccount: 'Toggle Account',
      authHook: 'Auth Hook Function `hasAuth`',
      superAdminVisible: 'Super Admin Visible',
      adminVisible: 'Admin Visible',
      adminOrUserVisible: 'Admin and User Visible'
    },
    request: {
      repeatedErrorOccurOnce: 'Repeated Request Error Occurs Once',
      repeatedError: 'Repeated Request Error',
      repeatedErrorMsg1: 'Custom Request Error 1',
      repeatedErrorMsg2: 'Custom Request Error 2'
    }
  },
  manage: {
    common: {
      status: {
        enable: 'Enable',
        disable: 'Disable'
      }
    },
    role: {
      title: 'Role List',
      roleName: 'Role Name',
      roleCode: 'Role Code',
      roleStatus: 'Role Status',
      roleDesc: 'Role Description',
      menuAuth: 'Menu Auth',
      buttonAuth: 'Button Auth',
      form: {
        roleName: 'Please enter role name',
        roleCode: 'Please enter role code',
        roleStatus: 'Please select role status',
        roleDesc: 'Please enter role description'
      },
      addRole: 'Add Role',
      editRole: 'Edit Role'
    },
    user: {
      title: 'User List',
      userName: 'User Name',
      userGender: 'Gender',
      nickName: 'Nick Name',
      userPhone: 'Phone Number',
      userEmail: 'Email',
      userStatus: 'User Status',
      userRole: 'User Role',
      oldPassword: 'Old Password',
      newPassword: 'New Password',
      confirmPassword: 'Confirm Password',
      form: {
        userName: 'Please enter user name',
        userGender: 'Please select gender',
        nickName: 'Please enter nick name',
        userPhone: 'Please enter phone number',
        userEmail: 'Please enter email',
        userStatus: 'Please select user status',
        userRole: 'Please select user role',
        oldPasswordPlaceholder: 'Please enter old password',
        newPasswordPlaceholder: 'Please enter new password',
        confirmPasswordPlaceholder: 'Please enter confirm password'
      },
      addUser: 'Add User',
      editUser: 'Edit User',
      gender: {
        male: 'Male',
        female: 'Female'
      }
    },
    menu: {
      home: 'Home',
      title: 'Menu List',
      id: 'ID',
      parentId: 'Parent ID',
      menuType: 'Menu Type',
      menuName: 'Menu Name',
      routeName: 'Route Name',
      routePath: 'Route Path',
      pathParam: 'Path Param',
      layout: 'Layout Component',
      page: 'Page Component',
      i18nKey: 'I18n Key',
      icon: 'Icon',
      localIcon: 'Local Icon',
      iconTypeTitle: 'Icon Type',
      order: 'Order',
      constant: 'Constant',
      keepAlive: 'Keep Alive',
      href: 'Href',
      hideInMenu: 'Hide In Menu',
      activeMenu: 'Active Menu',
      multiTab: 'Multi Tab',
      fixedIndexInTab: 'Fixed Index In Tab',
      query: 'Query Params',
      button: 'Button',
      buttonCode: 'Button Code',
      buttonDesc: 'Button Desc',
      menuStatus: 'Menu Status',
      form: {
        home: 'Please select home',
        menuType: 'Please select menu type',
        menuName: 'Please enter menu name',
        routeName: 'Please enter route name',
        routePath: 'Please enter route path',
        pathParam: 'Please enter path param',
        page: 'Please select page component',
        layout: 'Please select layout component',
        i18nKey: 'Please enter i18n key',
        icon: 'Please enter iconify name',
        localIcon: 'Please enter local icon name',
        order: 'Please enter order',
        keepAlive: 'Please select whether to cache route',
        href: 'Please enter href',
        hideInMenu: 'Please select whether to hide menu',
        activeMenu: 'Please select route name of the highlighted menu',
        multiTab: 'Please select whether to support multiple tabs',
        fixedInTab: 'Please select whether to fix in the tab',
        fixedIndexInTab: 'Please enter the index fixed in the tab',
        queryKey: 'Please enter route parameter Key',
        queryValue: 'Please enter route parameter Value',
        button: 'Please select whether it is a button',
        buttonCode: 'Please enter button code',
        buttonDesc: 'Please enter button description',
        menuStatus: 'Please select menu status'
      },
      addMenu: 'Add Menu',
      editMenu: 'Edit Menu',
      addChildMenu: 'Add Child Menu',
      type: {
        directory: 'Directory',
        menu: 'Menu'
      },
      iconType: {
        iconify: 'Iconify Icon',
        local: 'Local Icon'
      }
    },
    userDetail: {
      explain: `This page is solely for demonstrating the powerful capabilities of react-router-dom's loader. The data is random and may not match.`,
      content: `The loader allows network requests and lazy-loaded files to be triggered almost simultaneously, enabling the lazy-loaded files to be parsed while waiting for the network request to complete. Once the network request finishes, the page is displayed all at once. Leveraging React's Fiber architecture, if users find the waiting time too long, they can switch to different pages during the wait. This is an advantage of the React framework and React Router's data loader, as it avoids the conventional sequence of: request lazy-loaded file -> parse -> mount -> send network request -> render page -> display, and eliminates the need for manually adding a loading effect.`
    }
  },
  table: {
    columns: {
      shopname: 'Shop Name',
      country: 'Country',
      serviceexpire: 'Service Expiration Time',
      operation: 'Operation',
      day: 'Day',
      hour: 'Hour',
      minute: 'Minute',
      second: 'Second',
      remaining: 'Remaining'
    }
  },
  listingall: {
    column: {
      parentAsin: 'Parent ASIN',
      parentAsinExtra: 'With Inventory or Sales',
      price: 'Price',
      inventory: 'Inventory',
      totalSales: 'Total Sales',
      adTotalSales: 'Ad Sales',
      acos: 'ACOS',
      naturalSalesRatio: 'Organic Sales Ratio',
      tacos: 'TACOS',
      spAdSalesRatio: 'SP Ad Sales Ratio',
      adTotalCost: 'Ad Total Cost',
      totalOrders: 'Total Orders',
      aiHost: 'AI Hosting'
    },
    button: {
      batchHost: 'Batch Host',
      refreshPage: 'Refresh Page'
    },
    tooltip: {
      clickAsinDetails: 'Click to view ASIN details',
      inventoryLessThan: 'Inventory less than {{limit}}, cannot be hosted'
    },
    search: {
      country: 'Country',
      selectCountry: 'Please select country',
      dateRange: 'Date Range',
      near3Days: 'Last 3 Days',
      near7Days: 'Last 7 Days',
      near30Days: 'Last 30 Days',
      dataDetails: 'Data Details',
      parentAsin: 'Parent ASIN',
      inputParentAsin: 'Enter parent ASIN',
      hostingStatus: 'Hosting Status',
      selectHostingStatus: 'Select hosting status',
      all: 'All',
      hosted: 'Hosted',
      unhosted: 'Unhosted',
      activated: 'Activated'
    },
    message: {
      existingHosted: 'There are already hosted listings',
      hostSuccess: 'AI hosting successful'
    },
    pagination: {
      total: 'Total {{total}} items'
    },
    dashboard: {
      dataOverview: 'Data Overview',
      dataCollecting: 'Data is collecting, please wait',
      dataCollectionGuide: 'Data Collection Guide',
      loadingChart: 'Loading chart...',
      reload: 'Reload',
      totalSales: 'Total Sales',
      adTotalSales: 'Ad Total Sales',
      adImpressions: 'Ad Impressions',
      naturalSalesRatio: 'Organic Sales Ratio',
      adSpend: 'Ad Spend',
      totalOrders: 'Total Orders',
      naturalSales: 'Organic Sales',
      clicks: 'Clicks',
      orders: 'Orders',
      ctr: 'CTR',
      conversionRate: 'Conversion Rate'
    },
    notice: {
      adStatsNoSb: 'Product center ad statistics do not include SB ad data',
      recentDataBias: 'Data of the last three days may deviate due to Amazon official data',
      minimumUnit: 'The minimum authorization unit of DeepBI Atlas system is parent ASIN'
    },
    modal: {
      title: 'AI Ad Diagnosis',
      currentSite: 'Current diagnose site:',
      remainingCount: 'Remaining diagnosis count this month:',
      info: 'This operation will perform ad diagnosis on all listings of the selected site, which takes 5-10 minutes.\nThe diagnosis process may take some time, please do not operate frequently. After the diagnosis is completed, please go to the site diagnosis record page for details.',
      insufficient: 'Insufficient diagnosis times for current site, please contact sales',
      generating: 'Diagnosis report is being generated, you can view it on the AI diagnosis report page',
      createSuccess: 'Created diagnosis task successfully, please go to AI diagnosis report page to view'
    }
  },
  ailisting: {
    title: 'AI Hosted Listings',
    columns: {
      parentAsin: 'Parent ASIN',
      totalSales: 'Total Sales',
      adSales: 'Ad Sales',
      adSpend: 'Ad Spend',
      organicSales: 'Organic Sales',
      organicSalesRatio: 'Organic Sales Ratio',
      tacos: 'TACOS',
      aiAcosSp: 'AI ACOS (SP)',
      aiAdSpendSp: 'AI Ad Spend (SP)',
      nonAiAcosSp: 'Non-AI ACOS (SP)',
      spAdSalesRatio: 'SP Ad Sales Ratio',
      aiAdSales: 'AI Ad Sales',
      aiSalesRatio: 'AI Sales Ratio',
      aiOrderCount: 'AI Order Count',
      keywordSeedCount: 'Keyword Seed Count',
      asinFlowSeedCount: 'ASIN Flow Seed Count',
      acos: 'ACOS',
      aiDailyBudget: 'AI Daily Budget',
      aiCampaignCount: 'AI Campaign Count',
      targetAcos: 'Target ACOS',
      status: 'Status',
      operations: 'Operations'
    },
    status: {
      preparing: 'Preparing',
      running: 'Running',
      pausing: 'Pausing',
      paused: 'Paused',
      cancelling: 'Cancelling',
      recovering: 'Recovering',
      cancelled: 'Cancelled',
      hosted: 'Hosted'
    },
    tooltips: {
      totalSales: 'Ad Sales + Organic Sales',
      adSales: 'AI Ad Sales + Non-AI Ad Sales',
      adSpend: 'AI Ad Spend + Non-AI Ad Spend',
      organicSales: 'Total Sales - Ad Sales',
      organicSalesRatio: 'Organic Sales / Total Sales',
      tacos: '(AI Ad Spend + Non-AI Ad Spend) / (Ad Sales + Organic Sales)',
      aiAcosSp: 'AI SP Ad Spend / AI SP Ad Sales',
      aiSalesRatio: 'AI Ad Sales / (AI Ad Sales + Non-AI Ad Sales)',
      keywordSeedCount: 'Number of keywords in active and paused campaigns',
      asinFlowSeedCount: 'Number of ASINs in active and paused campaigns',
      acos: '(AI Ad Spend + Non-AI Ad Spend) / Ad Sales',
      targetAcos: 'Default is the store-wide ACOS value. Individual ASIN settings can be configured if needed.',
      targetAcosStrategy:
        'Priority is given to the overall expected ACOS, with individual ASIN strategies as supplementary.',
      statusExplanation: 'ASIN ad status',
      errorPrompt: 'Error prompt',
      aiDailyBudget: 'AI campaign daily budget',
      aiAdSales: 'AI SP ad sales',
      nonAiAcosSp: 'Non-AI SP ad spend / Non-AI SP ad sales',
      spAdSalesRatio: 'All SP ad sales / Total ad sales',
      aiOrderCount: 'AI ad campaign order count',
      aiExploreKeywords: 'AI will gradually explore and add more keywords'
    },
    statusDescriptions: {
      preparing: 'AI completes ad creation and optimization strategy preparation within 24 hours after hosting.',
      running: 'After ad creation, DeepBI gradually explores and adds keywords and ASINs.',
      pausing: 'Ad delivery is being paused.',
      paused: 'Ad delivery has been paused. Click resume to restart ad delivery.',
      cancelling:
        'DeepBI changes ad naming, terminates ad optimization strategy, pauses ad delivery, and completes Listing hosting cancellation.',
      recovering:
        'Ad delivery is being restored. After completion, ad delivery and optimization strategy will proceed normally.'
    },
    buttons: {
      timeBudget: 'Time Budget',
      aiDiagnosis: 'AI Diagnosis',
      setTargetAcos: 'Set Target ACOS',
      batchOperations: 'Batch Operations',
      addHostedAsin: 'Add Hosted ASIN',
      hostingGuide: 'Hosting Guide',
      refreshPage: 'Refresh Page',
      goToListingPage: 'Go to Listing Page for Batch Hosting',
      adCampaignList: 'Ad Campaign List'
    },
    dropdownMenu: {
      setAcos: 'Set Target ACOS',
      cancelHosting: 'Cancel Hosting',
      pauseDelivery: 'Pause Delivery',
      resumeDelivery: 'Resume Delivery'
    },
    confirmDialogs: {
      cancelHosting: {
        title: 'Confirm cancellation of hosting?',
        description:
          'After cancelling hosting, AI optimization service will be terminated, ad naming will be changed, and ad delivery will be paused. If you need to continue optimization, you will need to host again.'
      },
      pauseDelivery: {
        title: 'Confirm pause of delivery?',
        description: 'Are you sure you want to pause ad delivery?'
      }
    },
    asinStatus: {
      outOfStock: 'Out of stock',
      highAcos: 'High ACOS',
      lowKeywords: 'Few keywords, high ACOS',
      lowKeywordsGoodAcos: 'Few keywords, good ACOS',
      matureGoodAcos: 'Mature, good ACOS'
    },
    acosStrategy: {
      ultraConservative: '≤ 16%',
      moderatelyConservative: '≤ 20%',
      conservative: '≤ 24%',
      balanced: '≤ 28%',
      aggressive: '≤ 32%',
      ultraAggressive: '≤ 50%',
      custom: 'Custom ≤'
    },
    messages: {
      settingSuccess: 'Setting successful',
      settingFailed: 'Setting failed',
      cancelSuccess: 'Cancel successful',
      pauseSuccess: 'Pause successful',
      resumeSuccess: 'Resume successful',
      selectRunningOrPausedOnly: 'Please select only ASINs with Running or Paused status',
      selectRunningOnly: 'Please select only ASINs with Running status',
      selectPausedOnly: 'Please select only ASINs with Paused status'
    },
    colorExplanation: {
      defaultSorting: '*Sorting defaults to ad delivery color classification',
      tableColumns: {
        colorCategory: 'Color Classification',
        acosCondition: 'ACOS Condition',
        trafficUnitCondition: 'Traffic Unit Condition',
        strategy: 'Strategy'
      },
      items: {
        lowAcosHighTraffic: {
          acos: 'ACOS Value ≤ 24%',
          condition: 'Sufficient Traffic Units',
          strategy: 'Increase traffic units, increase budget'
        },
        lowAcosLowTraffic: {
          acos: 'ACOS Value ≤ 24%',
          condition: 'Few Traffic Units',
          strategy: 'Explore more traffic units, increase traffic units'
        },
        highAcosHighTraffic: {
          acos: 'ACOS Value > 24%',
          condition: 'Sufficient Traffic Units',
          strategy: 'Lower ACOS value, slightly increase traffic units'
        },
        highAcosLowTraffic: {
          acos: 'ACOS Value > 24%',
          condition: 'Few Traffic Units',
          strategy: 'Low budget exploration, control ACOS value'
        },
        lowInventory: {
          acos: 'No specific ACOS condition',
          condition: 'Low inventory or off-season products',
          strategy: 'Extremely low budget, avoid waste'
        },
        dataCollecting: {
          acos: 'No specific ACOS condition',
          condition: 'Data collection in progress',
          strategy: '--'
        }
      },
      trafficUnitDefinition:
        'Traffic Unit Definition: Traffic units are the sum of keywords and ASINs being advertised, one keyword/ASIN has three traffic units.',
      sufficientTrafficDefinition:
        'Sufficient Traffic Definition: More than 150 traffic units is considered sufficient.',
      colorClassificationBasis: 'Ad delivery color classification is based on DeepBI plan SP ACOS value.'
    },
    acosSettingModal: {
      title: 'AI Smart Hosting - ACOS Target Setting',
      targetAcosValue: 'Target ACOS Value:',
      customAcos: 'Custom ACOS ≤ {{value}} %',
      optimizationStrategy: 'AI Optimization Strategy:',
      strategyPoints: {
        siteBased:
          "Based on the site's ACOS target, specific listings can be set individually for more precise ad management.",
        referenceIndicator:
          'The ACOS target of the listing will serve as an important reference indicator for AI hosting. Please avoid frequent modifications.'
      },
      customRangeNotice: 'Custom expected ACOS value range: 16% ~ 50%'
    },
    aiDiagnosisModal: {
      title: 'AI Diagnosis Listing (beta)',
      remainingDiagnosis: 'Remaining diagnosis count this month:',
      yourProductAsin: 'Your Product ASIN',
      enterYourProductAsin: 'Please enter your product ASIN',
      competitorAsin: 'Competitor Comparison ASIN',
      enterCompetitorAsin: 'Please enter competitor comparison ASIN',
      startDiagnosis: 'Start AI Smart Diagnosis',
      betaVersionNote:
        'Beta version can only keep one report at a time. If you diagnose again, it will replace the previous report',
      reportDownload: 'Download',
      diagnosisInProgress: '(AI diagnosis in progress, takes about 15 minutes)',
      diagnosisFailed: '(AI diagnosis failed, please diagnose again)',
      clickToDownload: 'Click to download report',
      diagnosisFailedNoDeduction: 'Failed diagnosis does not deduct from diagnosis count, you can try again'
    },
    asinStatusSetter: {
      title: 'Child ASIN Delivery Status',
      childAsinManagement: 'Child ASIN Management',
      inputAsin: 'Enter ASIN',
      adStatus: 'Ad Status',
      statusOptions: {
        all: 'All',
        enabled: 'Enabled',
        disabled: 'Disabled',
        processing: 'Processing'
      },
      columns: {
        asin: 'ASIN',
        adDeliveryStatus: 'Ad Delivery Status',
        operation: 'Operation'
      },
      price: 'Price',
      inventory: 'Inventory',
      processing: 'Processing',
      confirmDelete: 'Confirm delete this ASIN?',
      operationInProgress: '{{asin}} operation is processing, please wait...',
      statusSubmitted: '{{asin}} status has been submitted, please wait for AI processing',
      totalItems: 'Total {{total}} items',
      batchEnabled: 'Selected ASINs have been enabled',
      batchDisabled: 'Selected ASINs have been disabled',
      batchOperationPartialFailed: 'Some operations may have failed, please check the status',
      batchOperationFailed: 'Batch status setting failed',
      selectAsin: 'Please select ASIN first'
    },
    competitorStrategy: {
      title: 'Competitor Strategy Configuration',
      competitorStrategy: 'Competitor Strategy',
      categories: {
        asin: 'Competitor ASIN',
        keyword: 'Keywords',
        history: 'History Records'
      },
      subCategories: {
        positive: {
          asin: 'Target Competitor ASINs',
          keyword: 'Target Keywords'
        },
        negative: {
          asin: 'Negative Competitor ASINs',
          keyword: 'Negative Keywords'
        }
      },
      searchPlaceholders: {
        asin: 'Search ASIN',
        keyword: 'Search Keyword',
        history: 'Search ASIN/Keyword'
      },
      addButton: {
        asin: 'Add ASIN',
        keyword: 'Add Keyword'
      },
      tableColumns: {
        asinKeyword: 'ASIN/Keyword',
        addTime: 'Add Time',
        addAccount: 'Add Account',
        operation: 'Operation',
        operationType: 'Operation Type'
      },
      emptyText: {
        asin: 'No competitor ASINs',
        negativeAsin: 'No negative competitor ASINs',
        keyword: 'No target keywords',
        negativeKeyword: 'No negative keywords',
        history: 'No history records'
      },
      confirmDelete: {
        asin: 'Confirm delete this ASIN?',
        keyword: 'Confirm delete this keyword?'
      },
      addModal: {
        titlePositiveAsin: 'Add Competitor ASIN',
        titleNegativeAsin: 'Add Negative Competitor ASIN',
        titlePositiveKeyword: 'Add Target Keyword',
        titleNegativeKeyword: 'Add Negative Keyword',
        placeholder: {
          asin: 'Enter ASINs here, one per line. You can add up to 10 ASINs at once and add multiple times.',
          keyword: 'Enter keywords here, one per line. You can add up to 10 keywords at once and add multiple times.'
        }
      },
      guides: {
        title: 'Operation Guide',
        positiveAsin:
          'The AI system has the capability to automatically identify and add competitor ASINs. Manual addition of competitor ASINs will serve as a supplement to competitor exploration to enhance advertising effectiveness.',
        negativeAsinPoints: [
          'Primarily rely on the AI system for automatic negative competitor ASINs, manual operations are only for supplementation.',
          'Negative competitor ASINs correspond to the "Exact Negative" function in the Amazon Advertising backend.'
        ],
        negativeWarning: {
          title: 'Before adding negative ASINs, please carefully confirm:',
          description:
            'The negated ASIN does not have a substitute/complementary relationship with your promoted product. If you add related competitor ASINs to the negative list, it will lead to:',
          consequences: [
            'The system cannot reach relevant search traffic',
            'Missing cross-product recommendation exposure opportunities',
            'Reduced overall conversion rate of the ad group'
          ]
        },
        operationLimit: {
          title: 'Operation Limit:',
          description:
            "Do not manually negate more than 10 ASINs at once to avoid excessively limiting the AI system's optimization space, which would lead to a drastic reduction in AI model learning data and decreased optimization effectiveness."
        },
        positiveKeyword:
          'The AI system has the capability to automatically identify and add keywords. Manual addition of keywords will serve as a supplement to keyword exploration to enhance advertising effectiveness.',
        negativeKeywordPoints: [
          'Primarily rely on the AI system for automatic negative keywords, manual operations are only for supplementation.',
          'Negative keywords correspond to the "Exact Negative" function in the Amazon Advertising backend.'
        ],
        negativeKeywordWarning: {
          title: 'Before adding negative keywords, please carefully confirm:',
          description:
            'The negated keyword does not have a relationship with your promoted product. If you add related keywords to the negative list, it will lead to:',
          consequences: [
            'The system cannot reach relevant search traffic',
            'Reduced overall conversion rate of the ad group'
          ]
        }
      },
      operationTypes: {
        all: 'All',
        add: 'Add',
        delete: 'Delete'
      },
      processing: 'Processing'
    }
  },
  mobileDetector: {
    title: 'Reminder',
    description: 'Please use the computer to access this page for the best experience.',
    description1: 'It is recommended to use a display with a resolution of 1920*1080 or higher.',
    copy: 'Copy Link',
    copySuccess: 'Copy Success',
    copyFailed: 'Copy Failed'
  }
};
export default page;
