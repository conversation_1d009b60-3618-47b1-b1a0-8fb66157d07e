// src/store/slice/message/index.ts
import { createSelector } from '@reduxjs/toolkit';
import { createAppSlice } from '../../createAppSlice';

const initialState = {
  unreadCount: 0
};

export const messageSlice = createAppSlice({
  name: 'message',
  initialState,
  reducers: create => ({
    setUnreadCount: create.reducer((state:any, { payload }) => {
      console.log('Setting unread count:', payload);
      state.unreadCount = payload;
    }),
    decreaseUnreadCount: create.reducer((state:any, { payload }) => {
      console.log('Decreasing unread count:', payload);
      // 如果相等，则不减
      // if(state.unreadCount === payload){
      //   console.log('Unread count is equal to payload, not decreasing');
      //   return;
      // }
      state.unreadCount = payload;
    })
  }),
  selectors: {
    selectUnreadCount: message => message.unreadCount
  }
});

// Export actions and selectors
export const { setUnreadCount, decreaseUnreadCount } = messageSlice.actions;
export const { selectUnreadCount } = messageSlice.selectors;

// Optional: Create additional selectors if needed
export const getUnreadCount = createSelector(
  [selectUnreadCount],
  unreadCount => unreadCount
);

export default messageSlice.reducer;