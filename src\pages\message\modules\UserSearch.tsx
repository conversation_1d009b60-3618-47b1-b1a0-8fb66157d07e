import { Col, Form, Row, Select } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect } from 'react';

interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading }) => {
  useEffect(() => {
    // 设置默认值并触发搜索
    form.setFieldsValue({
      is_read: true
    });
    handleSearch();
  }, []);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      is_read: values.is_read
    };
    search(params);
  };

  return (
    <Form disabled={loading} form={form}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={12} lg={4}>
          <Form.Item 
            className="m-0" 
            name="is_read" 
            label=""
            valuePropName="checked"
          >
          <ACheckbox onChange={handleSearch}>只看未读</ACheckbox>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;