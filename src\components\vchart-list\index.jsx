
import { VChart } from "@visactor/react-vchart";
import { Icon } from '@iconify/react';
// import specData from './spec'; // 确保spec.js文件已正确导入
import specData2 from './ADSpec'; // 确保spec.js文件已正确导入
export default function VchartList({ pageType = "", VchartListData }) {
  const [chartHeight, setChartHeight] = useState(window.innerHeight / 3); // 初始高度设置为屏幕高度的三分之一
  const [specDataList, setSpecData] = useState(pageType === "aidrivenads" ? specData2 : specData);
  const { t } = useTranslation();
  useEffect(() => {
    // console.log(JSON.stringify(specDataList[0],replacer))
    const handleResize = () => {
      setChartHeight(window.innerHeight / 3); // 更新高度为屏幕高度的三分之一
    };

    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };



  }, []);
  const getSpan = (index) => {
    if (pageType === 'aidrivenads') {

      // 第一行三个，每个 span 为 8
      // 第二行四个，每个 span 为 6
      return index < 3 ? 8 : 6;
    } else {
      // 默认情况下，每行三个，每个 span 为 8
      return 8;
    }
  };
  const getChartHeight = (index) => {
    if (index >= 6) {
      return chartHeight - 33.5;
    } else {
      return chartHeight - 65;
    }
  }
  return (
    <div className="grid vchart-list">
      <ARow gutter={[6, 6]}>
        {VchartListData.map((item, index) => (
          <ACol key={index} span={getSpan(index)}>
            <ACard shadows={"always"}>
              <div className="flex justify-between pl-5 pr-5 pt-3 pb-1 items-baseline">
                <div className="flex items-center text-base text-gray-600">
                  <p className='flex items-center text-#333'>
                    <span>{item.title}</span>
                    {/* <Tooltip content={item.tip}>
                      <IconHelpCircle style={{ color: '#999' }} size="default" className='ml-1' />
                    </Tooltip> */}

                  </p>
                </div>
                {
                  item.title != "Ads type" && <div className="flex flex-col items-end">
                    <div className="text-xl font-bold text-black">{item.value}</div>
                    <div className="flex items-center mt-1 text-base">
                      {
                        item.type !== "stable" &&
                        <Icon icon={`ant-design:${item.type === 'rise' ? 'rise-outlined' : 'fall-outlined'}`} className={`mr-2 ${item.type === 'rise' ? 'text-green-500' : 'text-red-500'}`} width={22} height={22} />
                      }
                      {
                        item.num !== "N/A" ? <span className={`font-medium ${item.type === 'rise' ? 'text-green-500' : 'text-red-500'}`}>{item.num}%</span> : <span className={`font-medium`}>{item.num}</span>
                      }
                    </div>
                  </div>
                }
              </div>
              {/* <div className="pl-10 pr-10">
                  <Divider className='mt-2' />
                </div> */}
              <VChart
                spec={{
                  height: getChartHeight(index),
                  ...item.data,
                }}
                option={{
                  mode: "desktop-browser",
                }}
              />
            </ACard>
          </ACol>
        ))}
      </ARow>
    </div>
  );
}