import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Modal, Select, Radio } from 'antd';
import OrderSelectionModal from './OrderModel';
import { getinvoiceInfo, invoiceConfig, invoiceApply, invoiceDownload } from '@/service/api/bill';

interface InvoiceInfo {
    CompanyName: string;
    TaxNum: string;
    BankCard: string;
    BankName: string;
    Addr: string;
    Tel: string;
    ID: number;
}


interface AddBillProps {
    onInvoiceApplied: () => void;
}
/**
 * 验证税号
 * 15或者17或者18或者20位字母、数字组成
 * @param obj
 * @returns {Boolean}
 */
function checkTax(obj: string): boolean {
    return /^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/.test(obj);
}

/**
 * 验证电话号码（手机号码+电话号码）
 * @param obj
 * @returns {Boolean}
 */
function checkPhoneNum(obj: string): boolean {
    return /^((\d{3}-\d{8}|\d{4}-\d{7,8})|(1[3|5|7|8][0-9]{9}))$/.test(obj);
}

//   /**
//    * 验证银行卡号
//    * 匹配16-19位数字的卡号
//    * @param cardNumber
//    * @returns {Boolean}
//    */
//   function validateCardNumber(cardNumber: string): boolean {
//     const regex = /^[0-9]{16,19}$/;
//     return regex.test(cardNumber);
//   }

const { Option } = Select;

const AddBill: React.FC<AddBillProps> = forwardRef((props, ref) => {
    const [form] = Form.useForm();
    const [invoiceForm] = Form.useForm();
    const [invoiceInfo, setInvoiceInfo] = useState<InvoiceInfo | null>(null);
    const [visible, setVisible] = useState(false);
    const [editVisible, setEditVisible] = useState(false);
    const [orderSelectionVisible, setOrderSelectionVisible] = useState(false);
    const [selectedOrders, setSelectedOrders] = useState({
        checkedRowKeys: [],
        Amount: 0
    });
    // 添加发票类型状态
    const [invoiceType, setInvoiceType] = useState(0);

    useEffect(() => {
        if (!visible) {
            setSelectedOrders({
                checkedRowKeys: [],
                Amount: 0
            });
            // setInvoiceType(0);
            form?.resetFields();
            console.log("form", form);
            // invoiceForm?.resetFields();
        }
    }, [visible]);

    useImperativeHandle(ref, () => ({
        showModal: () => setVisible(true),
        hideModal: () => setVisible(false),
    }));

    const handleOk = async () => {
        form.validateFields()
            .then(async values => {
                console.log('Form values:', values);
                // Submit the form values
                const res = await invoiceApply(values);
                console.log(res);
                if (res && res.data) {
                    window?.$message?.success('申请开票成功');
                    setVisible(false);
                    props.onInvoiceApplied();
                }

            })
            .catch(info => {
                console.log('Validate Failed:', info);
            });
    };

    const handleCancel = () => {
        setVisible(false);

    };

    const handleEditOk = () => {
        let values = ['CompanyName','TaxNum'];
        console.log(values);
        if(invoiceType === 1){
            values = ['CompanyName','TaxNum','BankName','Addr','Tel']
        }




        invoiceForm.validateFields()
            .then(async values => {
                console.log(values);
               
                const res = await invoiceConfig(values);
                console.log(res);
                if (res && res.data) {
                    setInvoiceInfo(values);
                    form.setFieldValue('InvoiceInfo', values?.CompanyName || "");
                    // 重新触发form的验证
                    form.validateFields(["InvoiceInfo"]);
                    window?.$message?.success('编辑成功');
                }
                setEditVisible(false);
            })
            .catch(info => {
                console.log('Validate Failed:', info);
            });
    };

    const handleEditCancel = () => {
        setEditVisible(false);
    };


    useEffect(() => {
        if (editVisible || visible) {
            // invoiceForm?.resetFields();
            // 请求发票信息
            getinvoiceInfo().then(res => {
                console.log(res);
                if (res && res.data) {
                    if (Array.isArray(res.data)) {
                        res.data = {}
                    }
                    setInvoiceInfo(res.data);
                    form.setFieldValue('InvoiceInfo', res.data?.ID || "");
                    invoiceForm.setFieldsValue(res.data);
                }
            });
        }
    }, [editVisible, visible]);

    
   // 检查增值税发票必填信息
   const checkRequiredFields = () => {
        if (!invoiceInfo?.BankName || !invoiceInfo?.Addr || !invoiceInfo?.Tel) {
            window?.$message?.warning('增值税专用发票需要完善开户行、地址和电话信息');
            
            setEditVisible(true);
        }
    };

    // 监听发票类型变化
    const handleInvoiceTypeChange = (e: any) => {
        const type = e.target.value;
        setInvoiceType(type);

        // 动态设置表单验证规则
        if (type === 1) {
            checkRequiredFields();
        }
    };

    return (
        <>
            <Modal
                title="申请开票"
                open={visible}
                width={600}
                centered
                maskClosable={false}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <div className='max-h-[600px] overflow-y-auto'>
                    <ul className='text-sm my-4 text-#41455b bg-[#f0f4ff] px-4 py-2' style={{ lineHeight: '26px' }}>
                        <li>1. 发票类型：数电票是全面数字化电子发票，数电票的法律效力、基本用途等与现有纸质发票相同。其中，带有“增值税专用发票”字样的数电票，其法律效力、基本用途与现有增值税专用发票相同；带有“普通发票”字样的数电票，其法律效力、基本用途与现有的普通发票相同。</li>
                        {/* <li>2. 开票周期：限付款周期近一年的订单开票，超出时间不予开票。</li> */}
                        <li>2. 系统将自动过滤异常订单、已申请还未开票，已开票的订单。</li>
                        <li>3. 开票时间1-3个工作日</li>
                        {/* 开票流程 */}
                        <li className='font-bold'>开票流程：</li>
                        <li>① 选择发票类型 {'>'} ② 编辑发票信息 {'>'} ③ 选择订单 {'>'} ④ 等待开票</li>
                    </ul>

                    <Form form={form} layout="vertical" >
                       
                        <Form.Item
                            className='mb-2'
                            name="InvoiceType"
                            label="发票类型"
                            initialValue={0}
                            rules={[{ required: true, message: '请选择发票类型' }]}
                        >
                            <Radio.Group onChange={handleInvoiceTypeChange}>
                                <Radio value={0}>普通发票</Radio>
                                <Radio value={1}>增值税专用发票</Radio>
                            </Radio.Group>
                        </Form.Item>
                        <Form.Item className='mb-2' label="发票信息" name="InvoiceInfo" initialValue={1} rules={[{ required: true, message: '请编辑发票信息' }]}>
                            <AButton type='link' onClick={() => setEditVisible(true)}>
                                编辑发票信息
                            </AButton>
                        </Form.Item>
                        <Form.Item
                            className='mb-2'
                            name="InvoiceIds"
                            label="选择开票订单"
                            rules={[{ required: true, message: '请选择开票订单' }]}
                        >

                            <div className='flex items-center'>
                                {selectedOrders.checkedRowKeys.length > 0 && <p className='text-sm text-primary' onClick={() => setOrderSelectionVisible(true)}>
                                    订单：<span className='text-[#ff8a00]'>{selectedOrders.checkedRowKeys.length}</span> 个，开票金额￥
                                    <span className='text-[#ff8a00]'>
                                        {selectedOrders.Amount}
                                    </span> 元
                                </p>
                                }

                                <AButton type='link' onClick={() => {
                                    if (selectedOrders.checkedRowKeys.length > 0) {
                                        setSelectedOrders({
                                            checkedRowKeys: [],
                                            Amount: 0
                                        });
                                    }
                                    setOrderSelectionVisible(true);
                                }}>
                                    {selectedOrders.checkedRowKeys.length > 0 ? '重新选择订单' : '选择订单'}
                                </AButton>
                            </div>
                        </Form.Item>
                    </Form>
                </div>
                <OrderSelectionModal
                    visible={orderSelectionVisible}
                    onClose={() => setOrderSelectionVisible(false)}
                    onSelectOrders={(orders) => {
                        setSelectedOrders(orders);
                        form.setFieldValue('InvoiceIds', orders?.checkedRowKeys.join(','));
                        form.validateFields(['InvoiceIds']);
                    }}
                />
                <Modal
                    title="编辑发票信息"
                    open={editVisible}
                    onOk={handleEditOk}
                    maskClosable={false}
                   
                    centered
                    onCancel={handleEditCancel}
                >
                    <Form   key={invoiceType} form={invoiceForm} layout="vertical" >
                        <Form.Item
                            name="CompanyName"
                            label="公司名称"
                            rules={[{ required: true, message: '请输入公司名称' }]}
                        >
                            <Input />
                        </Form.Item>
                        <Form.Item
                            name="TaxNum"
                            label="税号"
                            rules={[
                                { required: true, message: '请输入税号' },
                                { validator: (_, value) => checkTax(value) ? Promise.resolve() : Promise.reject('税号格式不正确') }
                            ]}
                        >
                            <Input />
                        </Form.Item>
                        {/* <Form.Item
                            name="BankCard"
                            label="银行账号"
                        rules={[
                            { required: true, message: '请输入银行账号' },
                            // { validator: (_, value) => validateCardNumber(value) ? Promise.resolve() : Promise.reject('银行账号格式不正确') }
                        ]}
                        >
                            <Input />
                        </Form.Item> */}
                        <Form.Item
                            name="BankName"
                            label="开户行"
                            rules={[{ required: invoiceType === 1, message: '请输入开户行' }]}
                        >
                            <Input />
                        </Form.Item>
                        <Form.Item
                            name="Addr"
                            label="地址"
                            rules={[{ required: invoiceType === 1, message: '请输入地址' }]}
                        >
                            <Input />
                        </Form.Item>
                        <Form.Item
                            name="Tel"
                            label="电话"
                            rules={[{ required: invoiceType === 1, message: '请输入电话' }]}
                        >
                            <Input />
                        </Form.Item>
                    </Form>
                </Modal>
            </Modal>


        </>
    );
});

export default AddBill;