import { Badge, Button, Dropdown, List, Typography } from 'antd';
import { BellOutlined } from '@ant-design/icons';
import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { getIsLogin } from '@/store/slice/auth';
import { fetchGetUserInfo } from '@/service/api';
import { selectUnreadCount, decreaseUnreadCount, setUnreadCount } from '@/store/slice/message';
import { useAppSelector, useAppDispatch } from '@/hooks/business/useStore';

interface Message {
  id: string;
  title: string;
  content: string;
  timestamp: string;
  isRead?: boolean;
}

// 生成唯一的标签页ID
const generateTabId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// 轮询协调器 - 管理多标签页的轮询
const POLL_MASTER_KEY = 'internal_message_poll_master';
const LAST_POLL_TIME_KEY = 'internal_message_last_poll_time';
const POLL_INTERVAL = 5 * 60 * 1000; // 5分钟轮询间隔
const UNREAD_COUNT_KEY = 'internal_message_unread_count'; // 用于存储未读消息数的key

const InternalMessage = () => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [polling, setPolling] = useState<NodeJS.Timeout | null>(null);
  const [tabId] = useState(() => generateTabId());
  const [isMaster, setIsMaster] = useState(false);
  const isLogin = useAppSelector(getIsLogin);
  const navigate = useNavigate();
  const { Text } = Typography;
  const unreadCount = useAppSelector(selectUnreadCount);
  const dispatch = useAppDispatch();
  const initializedRef = useRef(false);

  // 从localStorage同步未读消息数
  const syncUnreadCountFromStorage = () => {
    const storedCount = localStorage.getItem(UNREAD_COUNT_KEY);
    if (storedCount !== null) {
      const count = parseInt(storedCount, 10);
      if (!isNaN(count) && count !== unreadCount) {
        // 使用类型断言解决类型错误
        dispatch(setUnreadCount(count as any));
      }
    }
  };

  // 获取用户信息的函数
  const fetchUserInfo = async () => {
    try {
      const { data: info, error: userInfoError } = await fetchGetUserInfo({
        need_read_msg: 1
      });
      if (info && !userInfoError) {
        // 更新最后一次轮询时间
        localStorage.setItem(LAST_POLL_TIME_KEY, Date.now().toString());
        
        // 使用类型断言来避免TypeScript类型检查错误
        const anyInfo = info as any;
        // 根据API返回的消息数量类型进行处理
        const unreadMsgCount = anyInfo.not_read_msg_count || 
                              anyInfo.unreadMsgCount || 
                              anyInfo.need_op_number || 
                              0;
        
        // 更新Redux状态，使用类型断言解决类型错误
        dispatch(decreaseUnreadCount(unreadMsgCount as any));
        
        // 同时更新localStorage，以便在不同标签页之间共享
        localStorage.setItem(UNREAD_COUNT_KEY, String(unreadMsgCount));
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  // 设置当前标签页为主标签页（负责轮询）
  const setAsMaster = () => {
    localStorage.setItem(POLL_MASTER_KEY, tabId);
    setIsMaster(true);
  };

  // 检查当前标签页是否为主标签页
  const checkIfMaster = () => {
    const currentMaster = localStorage.getItem(POLL_MASTER_KEY);
    return currentMaster === tabId;
  };

  // 处理标签页可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isLogin) {
        // 如果当前没有主标签页或上次轮询时间过长，则设置当前标签为主标签
        const currentMaster = localStorage.getItem(POLL_MASTER_KEY);
        const lastPollTime = Number(localStorage.getItem(LAST_POLL_TIME_KEY) || '0');
        const now = Date.now();
        
        if (!currentMaster || now - lastPollTime > POLL_INTERVAL) {
          setAsMaster();
          // 立即执行一次轮询
          fetchUserInfo();
        } else {
          // 如果不是主标签，则从localStorage同步未读消息数
          syncUnreadCountFromStorage();
        }
      }
    };

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 初始化检查 - 仅第一次运行时执行
    if (!initializedRef.current && isLogin) {
      initializedRef.current = true;
      
      if (document.visibilityState === 'visible') {
        handleVisibilityChange();
      }
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isLogin]);

  // 监听存储变化以同步状态
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      // 如果另一个标签页成为了主标签页，则当前标签页停止轮询
      if (e.key === POLL_MASTER_KEY && e.newValue !== tabId) {
        setIsMaster(false);
        if (polling) {
          clearInterval(polling);
          setPolling(null);
        }
      }
      
      // 如果未读消息数量更新，则同步状态
      if (e.key === UNREAD_COUNT_KEY && e.newValue) {
        const newCount = parseInt(e.newValue, 10);
        if (!isNaN(newCount) && newCount !== unreadCount) {
          // 使用类型断言解决类型错误
          dispatch(setUnreadCount(newCount as any));
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [polling, tabId, unreadCount, dispatch]);

  // 主要轮询逻辑
  useEffect(() => {
    // 组件挂载时，从localStorage同步未读消息数
    syncUnreadCountFromStorage();
    
    if (isLogin && isMaster) {
      // 设置轮询
      const timer = setInterval(fetchUserInfo, POLL_INTERVAL);
      setPolling(timer);

      // 窗口关闭前清除主标签状态
      const handleBeforeUnload = () => {
        if (localStorage.getItem(POLL_MASTER_KEY) === tabId) {
          localStorage.removeItem(POLL_MASTER_KEY);
        }
      };
      
      window.addEventListener('beforeunload', handleBeforeUnload);

      // 清理函数
      return () => {
        if (polling) {
          clearInterval(polling);
          setPolling(null);
        }
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    } else if (!isLogin && polling) {
      // 未登录时清除轮询
      clearInterval(polling);
      setPolling(null);
      // 清除主标签标识
      if (localStorage.getItem(POLL_MASTER_KEY) === tabId) {
        localStorage.removeItem(POLL_MASTER_KEY);
      }
    }
  }, [isLogin, isMaster]);

  // 模拟获取消息列表数据
  // useEffect(() => {
  //   if (isLogin) {
  //     // 这里替换为实际的API调用
  //     const mockMessages = [
  //       {
  //         id: '1',
  //         title: 'OSS违规文件冻结处理通知',
  //         content: '您的OSS存储中发现违规文件...',
  //         timestamp: '2024年12月11日 10:14:48',
  //         isRead: false
  //       },
  //       {
  //         id: '2',
  //         title: '阿里云百炼上线新模型可支持100万超长上下文',
  //         content: '通知内容...',
  //         timestamp: '2024年12月9日 10:35:52',
  //         isRead: false
  //       },
  //     ];

  //     setMessages(mockMessages);
  //   }
  // }, [isLogin]);

  const handleViewMore = () => {
    // 跳转到消息中心页面
    navigate('/message');
  };

  const handleMessageClick = (message: Message) => {
    // 处理消息点击事件，可以标记为已读等
    console.log('Clicked message:', message);
  };

  const messageContent = (
    <div className="w-400px max-h-500px overflow-auto bg-white border-1 border-gray-200 rounded-md">
      <div className="p-4 border-b">
        <Text strong>未读消息 ({unreadCount})</Text>
      </div>
      <List
        className="px-2"
        dataSource={messages}
        renderItem={(item) => (
          <List.Item 
            className="cursor-pointer hover:bg-gray-50 px-2"
            onClick={() => handleMessageClick(item)}
          >
            <div className="w-full">
              <div className="text-sm">{item.title}</div>
              <div className="text-gray-400 text-xs mt-1">{item.timestamp}</div>
            </div>
          </List.Item>
        )}
      />
      <div className="p-3 border-t flex justify-between">
        <a className='text-primary' onClick={handleViewMore}>查看更多</a>
        <a className='text-primary' onClick={() => navigate('/message-settings')}>消息接收管理</a>
      </div>
    </div>
  );

  // 如果未登录，不显示消息组件
  if (!isLogin) {
    return null;
  }

  return (
    // <Dropdown 
    //   overlay={messageContent} 
    //   trigger={['click']}
    //   placement="bottomRight" 
    //   arrow
    // >
     <ButtonIcon  tooltipContent={t('page.home.message')} onClick={handleViewMore}>
       <Badge  count={unreadCount} className="cursor-pointer">
        <BellOutlined style={{ fontSize: '18px' }} />
      </Badge>
    </ButtonIcon>
  );
};

export default InternalMessage;