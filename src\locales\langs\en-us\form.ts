const form: App.I18n.Schema['translation']['form'] = {
  required: 'Cannot be empty',
  companyName: {
    required: 'Please enter company name',
    invalid: 'Company name format is incorrect'
  },  
  userName: {
    required: 'Please enter account',
    invalid: 'Account format is incorrect'
  },
  phone: {
    required: 'Please enter phone number',
    invalid: 'Phone number format is incorrect'
  },
  pwd: {
    required: 'Please enter password',
    invalid: 'Password format is incorrect, at least 8 characters, including uppercase and lowercase letters, numbers',
    sameAsOld: 'New password cannot be the same as the old password',
    notMatch: 'The two passwords are inconsistent'
  },
  confirmPwd: {
    required: 'Please enter password again',
    invalid: 'The two passwords are inconsistent'
  },
  code: {
    required: 'Please enter verification code',
    invalid: 'Verification code format is incorrect'
  },
  email: {
    required: 'Please enter email',
    invalid: 'Email format is incorrect'
  },
};
export default form;
