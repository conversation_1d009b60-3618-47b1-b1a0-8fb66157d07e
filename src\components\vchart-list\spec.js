const spec = [
    {
        title:"Average Price",
        value:"$ 56.1",
        type:"rise",
        num:"8.5",
        data:{
            type: 'area',
            data: {
                values: [
                    { time: '2:00', value: 10 },
                    { time: '4:00', value: 12 },
                    { time: '6:00', value: 11 },
                    { time: '8:00', value: 14 },
                    { time: '10:00', value: 16 },
                    { time: '12:00', value: 10 },
                    { time: '14:00', value: 12 },
                    { time: '16:00', value: 13 },
                    { time: '18:00', value: 14 }
                ]
            },
            xField: 'time',
            yField: 'value',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        max: 16
                    }
                },
                {
                    orient: 'bottom',
                    visible: false
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title:"Change Price Record",
        value:"67",
        type:"decline",
        num:"4.3",
        data:{
            type: 'area',
            data: {
                values: [
                    { time: '2:00', value: 10 },
                    { time: '4:00', value: 12 },
                    { time: '6:00', value: 11 },
                    { time: '8:00', value: 14 },
                    { time: '10:00', value: 16 },
                    { time: '12:00', value: 10 },
                    { time: '14:00', value: 12 },
                    { time: '16:00', value: 13 },
                    { time: '18:00', value: 14 }
                ]
            },
            xField: 'time',
            yField: 'value',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        max: 20
                    }
                },
                {
                    orient: 'bottom',
                    visible: false
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title:"Traffic",
        value:"500",
        type:"decline",
        num:"9.4",
        data:{
            type: 'area',
            data: {
                values: [
                    { time: '2:00', value: 10 },
                    { time: '4:00', value: 12 },
                    { time: '6:00', value: 11 },
                    { time: '8:00', value: 14 },
                    { time: '10:00', value: 16 },
                    { time: '12:00', value: 10 },
                    { time: '14:00', value: 12 },
                    { time: '16:00', value: 13 },
                    { time: '18:00', value: 14 }
                ]
            },
            xField: 'time',
            yField: 'value',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        max: 20
                    }
                },
                {
                    orient: 'bottom',
                    visible: false
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title:"Conversion Rate",
        value:"7.5 %",
        type:"rise",
        num:"3.5",
        data:{
            type: 'area',
            data: {
                values: [
                    { time: '2:00', value: 10 },
                    { time: '4:00', value: 12 },
                    { time: '6:00', value: 11 },
                    { time: '8:00', value: 14 },
                    { time: '10:00', value: 16 },
                    { time: '12:00', value: 10 },
                    { time: '14:00', value: 12 },
                    { time: '16:00', value: 13 },
                    { time: '18:00', value: 14 }
                ]
            },
            xField: 'time',
            yField: 'value',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        max: 20
                    }
                },
                {
                    orient: 'bottom',
                    visible: false
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title:"Turnover",
        value:"35",
        type:"rise",
        num:"3.9",
        data:{
            type: 'area',
            data: {
                values: [
                    { time: '2:00', value: 10 },
                    { time: '4:00', value: 12 },
                    { time: '6:00', value: 11 },
                    { time: '8:00', value: 14 },
                    { time: '10:00', value: 16 },
                    { time: '12:00', value: 10 },
                    { time: '14:00', value: 12 },
                    { time: '16:00', value: 13 },
                    { time: '18:00', value: 14 }
                ]
            },
            xField: 'time',
            yField: 'value',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        max: 20
                    }
                },
                {
                    orient: 'bottom',
                    visible: false
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title:"Payout fee",
        value:"12.8%",
        type:"rise",
        num:"2.2",
        data:{
            type: 'area',
            data: {
                values: [
                    { time: '2:00', value: 10 },
                    { time: '4:00', value: 12 },
                    { time: '6:00', value: 11 },
                    { time: '8:00', value: 14 },
                    { time: '10:00', value: 16 },
                    { time: '12:00', value: 10 },
                    { time: '14:00', value: 12 },
                    { time: '16:00', value: 13 },
                    { time: '18:00', value: 14 }
                ]
            },
            xField: 'time',
            yField: 'value',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        max: 20
                    }
                },
                {
                    orient: 'bottom',
                    visible: false
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
]

export default spec;