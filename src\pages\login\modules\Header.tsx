import SystemLogo from '@/components/stateless/common/SystemLogo';
import ThemeSchemaSwitch from '@/components/stateful/ThemeSchemaSwitch';
// import LangSwitch from '@/components/stateful/LangSwitch';

const Header = memo(() => {
  const { t } = useTranslation();

  return (
    <header className="flex-y-center justify-start">
      <SystemLogo className="text-46px text-primary lt-sm:text-48px" />
      <h3 className="text-22px text-primary font-500 lt-sm:text-22px ml-2">
        {/* {t('system.title')} */}
        DeepBI   <span className="text-black ">
            AT<span className="text-red-600">L</span>AS
          </span>
      </h3>
      <div className="i-flex-col">
        {/* <ThemeSchemaSwitch
          showTooltip={false}
          className="text-20px lt-sm:text-18px"
        /> */}
        {/* <LangSwitch showTooltip={false} /> */}
      </div>
    </header>
  );
});

export default Header;
