import React, { useState } from 'react';
import { Modal, Table, Button } from 'antd';
import { serviceBuyList } from '@/service/api';
interface Order {
    key: string;
    orderNumber: string;
    amount: number;
}

interface OrderSelectionModalProps {
    visible: boolean;
    onClose: () => void;
    onSelectOrders: (selectedOrders: {checkedRowKeys: React.Key[],Amount: string}) => void;
}

const OrderSelectionModal: React.FC<OrderSelectionModalProps> = ({ visible, onClose, onSelectOrders }) => {

    const orders: Order[] = [
        { key: '1', orderNumber: '订单1', amount: 100 },
        { key: '2', orderNumber: '订单2', amount: 200 },
        // Add more orders as needed
    ];

    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: serviceBuyList,
            apiParams: {
                page: 1,
                pagesize: 20,
                where: {}
            },
            immediate: false,
            columns: () => [
                {
                    key: 'ID',
                    dataIndex: 'ID',
                    title: '订单ID',
                    align: 'center',
                    hidden: true, // 如果不需要显示，可以保持隐藏
                },
                {
                    key: 'OrderNum',
                    dataIndex: 'OrderNum',
                    title: '订单编号',
                    align: 'center',
                    checked: true,
                    width: 170,
                    // ellipsis: {
                    //     showTitle: false,
                    // },
                    // 用tooltip显示
                    // render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                },
                {
                    key: 'ServiceName',
                    dataIndex: 'ServiceName',
                    title: '服务名称',
                    align: 'center',
                    checked: true,
                    width: 100,
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (text: string) => <ATooltip placement="topLeft" title={text}>{text}</ATooltip>,
                },

                {
                    key: 'Content',
                    dataIndex: 'Content',
                    title: '服务内容',
                    align: 'center',
                    checked: true,
                    width: 110,
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (text: string) => {
                        const contentArray = JSON.parse(text);
                        
                        // Group by ShopName
                        const groupedData = contentArray.reduce((acc: any, curr: any) => {
                            if (!acc[curr.ShopName]) {
                                acc[curr.ShopName] = [];
                            }
                            acc[curr.ShopName].push(curr.CountryCode);
                            return acc;
                        }, {});
                
                        // Create content for display
                        const content = Object.keys(groupedData).join(', ');
                        
                        // Create table content for popover
                        const popoverContent = (
                            <ATable 
                                size="small"
                                pagination={false}
                                dataSource={Object.entries(groupedData).map(([shop, countries]) => ({
                                    key: shop,
                                    shop,
                                    countries: (countries as string[]).join(', ')
                                }))}
                                columns={[
                                    {
                                        title: '店铺名称',
                                        dataIndex: 'shop',
                                        key: 'shop',
                                    },
                                    {
                                        title: '国家',
                                        dataIndex: 'countries',
                                        key: 'countries',
                                    }
                                ]}
                            />
                        );
                
                        return (
                            <APopover 
                                content={popoverContent}
                                title="服务详情"
                                trigger="hover"
                            >
                                {/* 下划线 */}
                                <span className="cursor-pointer text-primary underline">{content}</span>
                            </APopover>
                        );
                    },
                },
                {
                    key: 'ServicePeriod',
                    dataIndex: 'FactServiceDay',
                    title: '服务周期',
                    align: 'center',
                    width: 170,
                    checked: true,
                    sorter: (a: any, b: any) => parseFloat(a.FactServiceDay) - parseFloat(b.FactServiceDay),
                    render: (_, record: any) => {
                        const months = record.PackageMonths ? `${record.PackageMonths}个月` : '';
                        const days  = record.PackageMonths ? `${record.PackageMonths * 30}天` : '';
                        const addDays = record.PackageAddDay ? `赠送${record.PackageAddDay}天` : '';
                        // 如果months和addDays都有值，则用+号连接，否则用空字符串
                        // const total = `${record.FactServiceDay}天`;
                        
                        return (
                            <ATooltip title={`${months}（${days}${addDays}）`}>
                                {months}（{days}{addDays}）
                            </ATooltip>
                        );
                    }
                },
                {
                    key: 'Pay',
                    dataIndex: 'Pay',
                    title: '订单金额',
                    align: 'center',
                    checked: true,
                    width: 100,
                    sorter: (a: any, b: any) => parseFloat(a.Pay) - parseFloat(b.Pay),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },

                // {
                //     key: 'CouponID',
                //     dataIndex: 'CouponID',
                //     title: '优惠券ID',
                //     align: 'center',
                //     checked: true,
                // },
                {
                    key: 'CouponMoney',
                    dataIndex: 'CouponMoney',
                    title: '优惠金额',
                    align: 'center',
                    checked: true,
                    width: 110,
                    sorter: (a: any, b: any) => parseFloat(a.CouponMoney) - parseFloat(b.CouponMoney),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                {
                    key: 'BalancePay',
                    dataIndex: 'BalancePay',
                    title: '可开票金额',
                    align: 'center',
                    checked: true,
                    width: 100,
                    sorter: (a: any, b: any) => parseFloat(a.BalancePay) - parseFloat(b.BalancePay),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                {
                    key: 'AddDatetime',
                    dataIndex: 'AddDatetime',
                    title: '创建时间',
                    align: 'center',
                    checked: true,
                    width: 150,
                    ellipsis: {
                        showTitle: false,
                    },
                    sorter: (a: any, b: any) => new Date(a.AddDatetime).getTime() - new Date(b.AddDatetime).getTime(),
                    render: (text: string) => <ATooltip placement="topLeft" title={text}>{text}</ATooltip>,
                },
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        clearCheckedRowKeys,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);


    const handleOk = () => {
        onSelectOrders({checkedRowKeys,Amount:calculateTotalAmount()});
        clearAll();
    };
    const clearAll = () => {
        clearCheckedRowKeys();
        onClose();
    }

    const getServiceBuyList = async () => {
        // const res = await serviceBuyList({});
        // console.log(res);
        // if(res && res.data) {
        //     setData(res.data.data);
        // }
    // console.log(params, "params====")
        // return
        // run(params);
        const formValues = form.getFieldsValue();
        console.log(formValues, "formValues====")
        const searchParams = {
            where: {
                ...(formValues?.PayState ? { PayState: `${formValues.PayState}` } : {}),
                "BalancePay":['>',0],
                "InvoiceFlag":0
            },
        };
        handleRun({
            page: 1,
            pagesize: 20,
            ...searchParams,
            CountryCode: "CN"
        });
    }
    const filterData = () => {
        if(!tableProps.dataSource) {
            return []
        }
        // 为tableProps.dataSource 按照索引加入ID
        return tableProps.dataSource.map((item: any, index: number) => ({
            ...item,
            ID: item['OrderNum']
        }));
        
    }
    
    const calculateTotalAmount = () => {
        // 遍历tableProps.dataSource 找到 在checkedRowKeys中的OrderNum 然后计算BalancePay
        return tableProps.dataSource.reduce((total, order) => {
            if(checkedRowKeys.includes(order.OrderNum)) {
                return total + parseFloat(order.BalancePay);
            }
            return total;
        }, 0).toFixed(2);
    };

    useEffect(() => {
        if(visible) {
            getServiceBuyList();
        }
    }, [visible]);

    return (
        <Modal
            title="选择开票订单"
            open={visible}
            // style={{ top: 20 }}
            // height={500}
            onOk={handleOk}
            onCancel={clearAll}
            maskClosable={false}
            centered
            footer={
                <div className='flex justify-between items-center'>
                    <p className='text-sm text-gray-500'>
                        已选订单：<span className='text-primary'>{checkedRowKeys.length}</span> 个，共￥ 
                        <span className='text-primary'>
                            {/* checkedRowKeys */}
                            {calculateTotalAmount()}
                        </span> 元
                    </p>
                    <AButton type='primary' disabled={checkedRowKeys.length === 0} onClick={handleOk}>确定</AButton>
                </div>
            }
            width={1300}
        >
                <ATable
                    scroll={{y: 400 }}
                    rowSelection={rowSelection}
                    size="small"
                    {...tableProps}
                    dataSource={filterData()}
                    key="OrderNum"
                />
        </Modal>
    );
};

export default OrderSelectionModal;