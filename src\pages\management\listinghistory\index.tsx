import { Tooltip, Tag } from 'antd';
import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { AsinOpLog, getAsinsInfo } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {
    const { t } = useTranslation();

    const userInfo = useAppSelector(selectUserInfo);

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const [listingHistoryLoading, setListingHistoryLoading] = useState(true);

    const nav = useNavigate();
    // 批量操作弹窗
    const [open, setOpen] = useState(false);

    const [tableData, setTableData] = useState<any[]>([]);


    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: AsinOpLog,
            apiParams: {
            },
            immediate: false,
            columns: () => [
                // {
                //   key: 'ID',
                //   title:"ID",
                //   dataIndex: 'ID',
                //   align: 'center',
                //   hidden: true
                // },
                {
                    key: 'Asin',
                    dataIndex: 'Asin',
                    title: '父ASIN',
                    align: 'center',
                    width: 350,
                    checked: true,
                    render: (_, record) => {
                        return (
                            <div className='flex items-stretch h-full gap-2 cursor-pointer'>
                                  <LazyImage
                                        src={record.first_image}
                                        alt={record.item_name || "--"}
                                        className='max-h-[100px] object-contain min-w-[80px]'
                                        width={100}
                                    />
                                <div onClick={(event) => {
                                    // 检查是否有文本被选中
                                    const selection = window.getSelection();
                                    if (selection && selection.toString().length > 0) {
                                        return; // 如果有文本被选中，则不执行跳转
                                    }
                                    nav(`/ai/camtar-listing?market=${form.getFieldValue('country')}&asin=${record.parent_asin}&isAuth=${record.IsAuth}`, '_blank');

                                    // router.push({name:"ai_camtar-listing",query:{market:form.getFieldValue('country'),asin:record.parent_asin}})
                                }} className='flex flex-col justify-between w-full'>
                                    <Tooltip title={record.item_name || "--"} className='w-full'>
                                        <div className='line-clamp-2 text-left' >
                                            {record.item_name || "--"}
                                        </div>
                                    </Tooltip>
                                    <div className='text-start flex flex-nowrap'>
                                        <span className='text-gray-400 mr-1 whitespace-nowrap'>价格:</span>
                                        <div className='flex flex-nowrap'>
                                            {record.min_price === record.max_price ?

                                                <CurrencySymbol countryCode={form.getFieldValue('country')} value={record.min_price} />
                                                :
                                                <>
                                                    <CurrencySymbol countryCode={form.getFieldValue('country')} value={record.min_price} />~<CurrencySymbol countryCode={form.getFieldValue('country')} value={record.max_price} />
                                                </>
                                            }
                                        </div>
                                        <span className='mx-1'> | </span>
                                        <span className='whitespace-nowrap'>
                                            <span className='text-gray-400 mr-1'>库存:</span>
                                            {record.HaveNum || "--"}
                                        </span>
                                    </div>
                                    <div className='text-start flex flex-col gap-1'>
                                    {/* 总是显示父 ASIN */}
                                    <div>
                                      {record.ParentAsin?'子':'父'}ASIN:
                                        <a 
                                        className='text-primary ml-1' 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        onClick={(event) => {
                                            event.stopPropagation();
                                            if(record.Asin && record.Asin !== '-'){
                                            window.open(`https://www.${getSalesChannel(form.getFieldValue('country'))}/dp/${record.Asin}`, '_blank');
                                            }
                                        }} 
                                        >
                                        {record.Asin || "-"}
                                        </a>
                                    </div>
                                    
                                    {/* 如果存在 ParentAsin，则显示子 ASIN */}
                                    {record.ParentAsin && (
                                        <div>
                                        父ASIN:
                                        <a 
                                            className='text-primary ml-1' 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            onClick={(event) => {
                                            event.stopPropagation();
                                            if(record.ParentAsin && record.ParentAsin !== '-'){
                                                window.open(`https://www.${getSalesChannel(form.getFieldValue('country'))}/dp/${record.ParentAsin}`, '_blank');
                                            }
                                            }} 
                                        >
                                            {record.ParentAsin || "-"}
                                        </a>
                                        </div>
                                    )}
                                    </div>
                                </div>
                            </div>
                        );
                    }
                },
                {
                    key: 'Content',
                    dataIndex: 'Content',
                    title: "操作类型",
                    align: 'center',
                    width: 100,
                    checked: true,
                },
                {
                    key: 'UOOther',
                    dataIndex: 'UOOther',
                    title: "操作账户",
                    align: 'center',
                    width: 100,
                    checked: true,
                    render: (_, record) => {
                        return <Tag color="blue">{record?.UOOther}</Tag>
                    }
                },
                {
                    key: 'Addtime',
                    dataIndex: 'Addtime',
                    title: "托管时间",
                    align: 'center',
                    width: 90,
                    checked: true,
                    sorter: (a: any, b: any) => {
                        // 转换为时间戳
                        const aTime = new Date(a.Addtime).getTime() || 0;
                        const bTime = new Date(b.Addtime).getTime() || 0;
                        return aTime - bTime
                    },
                }
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);

    // 定义asin 颜色
    const getColor = (asin_state: number) => {
        switch (asin_state) {
            case 0:
                return '#FFFFFF'; // light gray
            case 1:
                return '#ccc'; // light gray
            case 2:
                return '#FFA726'; // light red
            case 3:
                return '#1664ff'; // light blue
            case 4:
                return '#F44336'; // light yellow
            case 5:
                return '#66BB6A'; // light green
        }

    };

    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async (params: any = {}) => {
        // console.log(params, "params====")
        // run(params);
        handleRun({
            page: 1,
            pagesize: 20,
            ...(params?.Asin ? {
                Asin: `${params.Asin}`
            } : {}),
            CountryCode: form.getFieldValue('country'),
        });
    }


    const filteredData = async() => {
        if (tableProps.dataSource.length === 0){
            if (!tableProps.loading) {
                setListingHistoryLoading(false);
            }
            return []
        };
        // console.log(tableProps.dataSource, "tableProps.dataSource")
        let newData = []
        const res = await getAsinsInfo(
            {
                UID: userInfo.active_shop_id,
                CountryCode: form.getFieldValue('country'), 
                Asins: tableProps.dataSource.map((item: any) => {
                    // 如果存在 ParentAsin，则使用 ParentAsin，否则使用 Asin
                    return item.ParentAsin || item.Asin;
                }).join(',')
            }
        )
        if (res && res.data) {
            //    遍历res.data.data 和 newData  将newData的Asin 和 res.data.data 中的resolved_asin 进行匹配 如果匹配上 则将res.data.data 中的数据添加到newData中
            newData = tableProps.dataSource.map((item: any) => {
                const asinToMatch = item.ParentAsin || item.Asin;
                const match = res.data.data.asin_info.find((item2: any) => item2.resolved_asin === asinToMatch);
                const storage_num = res.data.data.storage_num[asinToMatch];
                if(storage_num){
                    // 遍历 storage_num child_asins 将其中的HaveNum加和
                    const HaveNum = JSON.parse(storage_num.child_asins).reduce((total: number, item: any) => total + item.HaveNum, 0);
                    storage_num.HaveNum = HaveNum;
                }
                return {
                    ...item,
                    ...match,
                    ...storage_num
                }
            })
            setListingHistoryLoading(false);
        }else{
            setListingHistoryLoading(false);
        }
        return newData;
    }

    useEffect(() => {
        setListingHistoryLoading(true);
        if (tableProps.dataSource.length >= 0) {
        const fetchData = async () => {
            const newData = await filteredData();
            // console.log(newData, "newData====");
            setTableData(newData);
            // setListingHistoryLoading(false);
        };
        fetchData();
        }
        
    }, [tableProps.dataSource]);
    return (
        <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
            <ACard>

                <UserSearch
                    search={getTableData}
                    reset={reset}
                    form={form}
                    loading={tableProps.loading}
                />
            </ACard>

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={() => {
                            getTableData({})
                        }}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <div>
                        AI托管记录
                    </div>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={scrollConfig}
                    // rowSelection={rowSelection}
                    size="small"
                    {...tableProps}
                    dataSource={tableData}
                    loading={listingHistoryLoading}
                // tableLayout="auto"
                //   dataSource={formattedData}
                />
            </ACard>
        </div>
    );
}
