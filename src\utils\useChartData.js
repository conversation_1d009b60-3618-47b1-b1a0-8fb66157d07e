import { useMemo } from 'react';
import BigNumber from 'bignumber.js';

const getMonth = (num = 0) => {
  const date = new Date();
  date.setMonth(date.getMonth() - num);
  const month = date.getMonth() + 1; // getMonth() returns 0-11, so add 1
  const year = date.getFullYear();
  return `${year}-${month < 10 ? `0${month}` : month}`; // Ensure month is two digits
};

// 合并chart数据
const mergeChartData = (results, country) => {
  const newAcc = results.flat();
  // console.log(newAcc, "newAcc");
  // return
  // Separate W-KL items for special merging
  const filterKLnewAcc = newAcc.filter(item => item.name === 'W-KL');
  const otherItems = newAcc.filter(item => item.name !== 'W-KL');

  if (filterKLnewAcc.length > 0) {
    // Merge W-KL items
    const mergedKLData = filterKLnewAcc.reduce((acc, item) => {
      item.chart.data.values.forEach(value => {
        const existingValue = acc.find(v => v.date === value.date && v.type === value.type);
        if (existingValue) {
          existingValue.quantity += value.quantity;
        } else {
          acc.push({ ...value });
        }
      });
      return acc;
    }, []);

    // Add merged W-KL item to other items
    otherItems.push({
      ...filterKLnewAcc[0],
      chart: {
        ...filterKLnewAcc[0].chart,
        data: {
          ...filterKLnewAcc[0].chart.data,
          values: mergedKLData
        }
      }
    });
  }

  // Merge other items
  const mergedItems = otherItems.reduce((acc, item) => {
    const existingItem = acc.find(i => i.name === item.name);
    if (existingItem) {
      if (Array.isArray(existingItem.chart.data)) {
        existingItem.chart.data.forEach((dataItem, index) => {
          dataItem.values.push(...item.chart.data[index].values);
        });
      } else {
        existingItem.chart.data.values.push(...item.chart.data.values);
      }
    } else {
      acc.push(item);
    }
    return acc;
  }, []);
  // console.log(mergedItems, "mergedItems");

  // 单独处理order相关图标 增加total曲线
  if (country.length > 1) {
    const totalValues = [];
    mergedItems.forEach(orderItem => {
      const dateMap = {};
      if (orderItem.name.includes('Order')) {
        console.log(orderItem.name, 'orderItem.name');
        // 还要判断orderItem.chart.data.values中的country数量是否大于1
        const countryList = [...new Set(orderItem.chart.data.values.map(value => value.country))];
        // console.log(countryList, "countryList=====");
        if (countryList.length <= 1) return;
        orderItem.chart.data.values.forEach(value => {
          if (!dateMap[value.time]) {
            dateMap[value.time] = new BigNumber(0);
          }

          dateMap[value.time] = dateMap[value.time].plus(new BigNumber(value.amount));
          // console.log(dateMap, "dateMap");
        });

        const totalValues = Object.keys(dateMap).map(date => ({
          amount: dateMap[date].toString(),
          type: '',
          time: date,
          country: 'total'
        }));
        orderItem.chart.data.values.push(...totalValues);
      }
    });
    // console.log(mergedItems, "mergedItems===");
  }

  // 单独处理D-AdSAPie图标 将country相同的数据加和
  mergedItems.forEach(orderItem => {
    const totalValues = [];

    if (orderItem.name === 'D-AdSAPie') {
      // console.log(orderItem, "orderItem");
      orderItem.chart.data[0].values.forEach(value => {
        // 将country相同的数据加和
        const existingValue = totalValues.find(v => v.country === value.country);
        if (existingValue) {
          existingValue.y = new BigNumber(existingValue.y).plus(new BigNumber(value.y)).toString();
          existingValue.cost = new BigNumber(existingValue.cost).plus(new BigNumber(value.cost)).toString();
        } else {
          totalValues.push({ ...value });
        }
      });

      // 计算cost_ratio
      totalValues.forEach(value => {
        value.cost_ratio = new BigNumber(value.cost).dividedBy(new BigNumber(value.y)).multipliedBy(100).toFixed(2);
        // 计算每个销售额占总销售额的比例
        value.sales_ratio = new BigNumber(value.y)
          .dividedBy(new BigNumber(totalValues.reduce((acc, val) => acc + Number.parseFloat(val.y), 0)))
          .multipliedBy(100)
          .toFixed(2);
      });

      orderItem.chart.data[0].values = totalValues;
      // console.log(totalValues, "totalValues===");
    }
    if (orderItem.name === 'D-Ad') {
      if (country.length <= 1) return;
      orderItem.chart.data.forEach(item => {
        if (item.id == 'bar') {
          // 如果等于bar的话 遍历item.values 将x和type相同的y加和
          const dateMap = {};
          item.values.forEach(value => {
            const key = `${value.x}_${value.type}`;
            if (!dateMap[key]) {
              dateMap[key] = new BigNumber(0);
            }
            dateMap[key] = dateMap[key].plus(new BigNumber(value.y));
          });
          const itemTotalValues = Object.keys(dateMap).map(key => {
            const [x, type] = key.split('_');
            return {
              x,
              y: dateMap[key].toString(),
              type,
              country: 'total'
            };
          });
          totalValues.push(...itemTotalValues);
        }
      });
      const totalACOS = [];
      // console.log(totalValues, "totalValues===");
      // 根据类型 用同日期的type为广告总花费的y除以同日期的type为广告总销售额的y 算出广告ACOS
      totalValues.forEach(value => {
        // console.log(value, "value");
        if (value.type === '广告总花费') {
          const salesValue = totalValues.find(v => v.x === value.x && v.type === '广告总销售额');
          if (salesValue) {
            const acos = new BigNumber(value.y).dividedBy(new BigNumber(salesValue.y)).multipliedBy(100).toFixed(2);
            totalACOS.push({
              x: value.x,
              y: acos,
              type: '广告ACOS',
              country: 'total'
            });
          }
        }
      });
      orderItem.chart.data[0].values.push(...totalValues);
      orderItem.chart.data[1].values.push(...totalACOS);
    }

    // 单独处理D-AdCam 将相同campaignName相同国家的数据中的clicks，impressions，orders，total_cost，total_sales加和
    // ... existing code ...
    // 单独处理D-AdCam 将相同campaignName相同国家的数据中的clicks，impressions，orders，total_cost，total_sales加和
    if (orderItem.name === 'D-AdCam') {
      const campaignMap = {};
      orderItem.chart.data[0].values.forEach(value => {
        const key = `${value.campaignName}_${value.country}`;
        const clicks = Number.parseInt(value.clicks) || 0;
        const impressions = Number.parseInt(value.impressions) || 0;
        const orders = Number.parseInt(value.orders) || 0;
        const total_cost = Number.parseFloat(value.total_cost) || 0;
        const total_sales = Number.parseFloat(value.total_sales) || 0;

        if (!campaignMap[key]) {
          campaignMap[key] = {
            ...value,
            campaignName: `${value.country} ${value.campaignName}`,
            clicks,
            impressions,
            orders,
            total_cost,
            total_sales
          };
        } else {
          campaignMap[key].clicks += clicks;
          campaignMap[key].impressions += impressions;
          campaignMap[key].orders += orders;
          campaignMap[key].total_cost += total_cost;
          campaignMap[key].total_sales += total_sales;
        }
      });

      // Calculate ACOS for each campaign
      Object.keys(campaignMap).forEach(key => {
        const campaign = campaignMap[key];
        if (campaign.total_sales > 0) {
          campaign.acos = ((campaign.total_cost / campaign.total_sales) * 100).toFixed(2);
          if (campaign.acos > 100) {
            campaign.newacos = '100';
          } else {
            campaign.newacos = campaign.acos;
          }
        } else {
          campaign.acos = '0.00'; // Handle case where total_sales is 0
        }
      });
      // console.log(campaignList,"campaignList===")
      // Update orderItem chart data values with campaignList
      orderItem.chart.data[0].values = Object.values(campaignMap);
    }
    // ... existing code ...
  });
  // console.log(mergedItems, "mergedItems===");
  return mergedItems;
};

const useChartData = selectedCountry => {
  return useMemo(() => {
    const handleChartData = (old_data, dataList, country) => {
      // console.log(old_data, "old_data=====")
      let chartData = {
        values: []
      };
      const adCosts = {
        id: 'bar',
        values: []
      };
      const adSales = {
        id: 'line',
        values: []
      };
      // console.log(dataList, "dataList=====")
      dataList.forEach(entry => {
        let parsedData = JSON.parse(entry.ShowData);
        if (entry && !entry.ShowData) {
          parsedData = entry;
        }
        switch (old_data.name) {
          case 'W-OrderS':
          case 'W-OrderA':
            chartData.values.push(
              ...parsedData.map(item => ({
                week: item[0],
                country,
                value: old_data.name === 'W-OrderS' ? Number.parseFloat(item[1]).toFixed(2) : item[1]
              }))
            );
            break;
          case 'D-Effect':
            parsedData.forEach(item => {
              adCosts.values.push(
                {
                  date: item[1],
                  Type: 'SP',
                  Cost: item[2],
                  Sales: item[3],
                  Impressions: item[4],
                  Click: item[5],
                  country,
                  CTR: Number.parseFloat(item[6]).toFixed(2),
                  ACOS: Number.parseFloat(item[7]).toFixed(2)
                },
                {
                  date: item[1],
                  Type: 'SD',
                  Cost: item[8],
                  Sales: item[9],
                  Impressions: item[10],
                  Click: item[11],
                  country,
                  CTR: Number.parseFloat(item[12]).toFixed(2),
                  ACOS: Number.parseFloat(item[13]).toFixed(2)
                },
                {
                  date: item[1],
                  Type: 'SB',
                  Cost: item[14],
                  Sales: item[15],
                  Impressions: item[16],
                  Click: item[17],
                  country,
                  CTR: Number.parseFloat(item[18]).toFixed(2),
                  ACOS: Number.parseFloat(item[19]).toFixed(2)
                }
              );
            });
            break;
          case 'W-Effect':
            parsedData.forEach(item => {
              adCosts.values.push(
                {
                  date: item[1],
                  Type: 'SP',
                  Cost: item[2],
                  Sales: item[3],
                  Impressions: item[4],
                  Click: item[5],
                  country,
                  CTR: Number.parseFloat(item[6]).toFixed(2),
                  ACOS: Number.parseFloat(item[7]).toFixed(2)
                },
                {
                  date: item[1],
                  Type: 'SD',
                  Cost: item[8],
                  Sales: item[9],
                  Impressions: item[10],
                  Click: item[11],
                  country,
                  CTR: Number.parseFloat(item[12]).toFixed(2),
                  ACOS: Number.parseFloat(item[13]).toFixed(2)
                },
                {
                  date: item[1],
                  Type: 'SB',
                  Cost: item[14],
                  Sales: item[15],
                  Impressions: item[16],
                  Click: item[17],
                  country,
                  CTR: Number.parseFloat(item[18]).toFixed(2),
                  ACOS: Number.parseFloat(item[19]).toFixed(2)
                }
              );
            });
            break;
          case 'D-Price':
            chartData.values.push(
              ...parsedData.map(item => ({
                date: item[1],
                value: Number.parseFloat(item[4]).toFixed(2),
                sales: item[2],
                country,
                orders: Number.parseInt(item[3])
              }))
            );
            break;
          case 'W-FBA':
            adCosts.values.push({
              name: country,
              children: parsedData
                .map(item => {
                  // console.log(item['预估可售天数'], "item预估可售天数===");
                  // 如果有NaN 则为0
                  const safeParseInt = value => {
                    const parsed = Number.parseInt(value);
                    return isNaN(parsed) ? 0 : parsed;
                  };
                  return {
                    name: item.parent_asins_or_asin.replace('(asin)', ''),
                    value: safeParseInt(item['总量']),
                    sales30: safeParseInt(item.sum_sales30),
                    estDays: safeParseInt(item['预估可售天数']),
                    market: item.random_market,
                    advertise: safeParseInt(item['可售数量']),
                    // 入库 在途 不可售 预留
                    storage: safeParseInt(item['入库']),
                    on_way: safeParseInt(item['在途']),
                    un_sell: safeParseInt(item['不可售']),
                    reserve: safeParseInt(item['预留'])
                  };
                  // }
                  // return null; // 返回 null 以便过滤掉不符合条件的项
                })
                .filter(child => child !== null) // 过滤掉 null 项
            });
            break;
          case 'W-KL':
            parsedData.forEach(item => {
              // console.log(item, "item");
              chartData.values.push(
                {
                  date: item.week_year,
                  type: '0-90',
                  sku: item.sku_num,
                  quantity: Number.parseInt(item.day0_90)
                },
                {
                  date: item.week_year,
                  type: '90-180',
                  sku: item.sku_num,
                  quantity: Number.parseInt(item.day91_180)
                },
                {
                  date: item.week_year,
                  type: '180-270',
                  sku: item.sku_num,
                  quantity: Number.parseInt(item.day181_270)
                },
                {
                  date: item.week_year,
                  type: '270-365',
                  sku: item.sku_num,
                  quantity: Number.parseInt(item.day271_356)
                },
                {
                  date: item.week_year,
                  type: '365plus',
                  sku: item.sku_num,
                  quantity: Number.parseInt(item.day356_more)
                }
              );
            });
            // chartData.values.push(...parsedData.flatMap(item => [
            //   {
            //     date: item[0],
            //     type: '0-90',
            //     sku: item[1],
            //     quantity: parseInt(item[2]),
            //   },
            //   {
            //     date: item[0],
            //     type: '90-180',
            //     sku: item[1],
            //     quantity: parseInt(item[3]),
            //   },
            //   {
            //     date: item[0],
            //     type: '180-270',
            //     sku: item[1],
            //     quantity: parseInt(item[4]),
            //   },
            //   {
            //     date: item[0],
            //     type: '270-365',
            //     sku: item[1],
            //     quantity: parseInt(item[5]),
            //   },
            //   {
            //     date: item[0],
            //     type: '365plus',
            //     sku: item[1],
            //     quantity: parseInt(item[6]),
            //   },
            // ]));
            break;
          case 'D-Ad':
            parsedData.forEach(item => {
              adCosts.values.push(
                {
                  x: item[1],
                  type: '广告总花费',
                  country,
                  y: Number.parseFloat(item[2])
                },
                {
                  x: item[1],
                  type: '广告总销售额',
                  country,
                  y: Number.parseFloat(item[3])
                }
              );
            });
            parsedData.forEach(item => {
              adSales.values.push({
                x: item[1],
                type: '广告ACOS',
                country,
                y: Number.parseFloat(item[4] * 100).toFixed(2)
              });
            });
            break;
          case 'D-Sales':
            parsedData.forEach(entry => {
              adCosts.values.push(
                {
                  type: '商品总销售额',
                  x: entry[1],
                  country,
                  y: Number.parseFloat(entry[2])
                },
                {
                  type: '自然销售额',
                  x: entry[1],
                  country,
                  y: Number.parseFloat(entry[6]) > 0 ? Number.parseFloat(entry[6]) : 0
                }
              );
            });
            parsedData.forEach(item => {
              adSales.values.push({
                x: item[1],
                type: '自然销售额比例',
                country,
                y: Number.parseFloat(item[6]) > 0 ? Number.parseFloat(item[7] * 100).toFixed(2) : 0
              });
            });
            break;
          case 'D-AdSABar':
            parsedData.forEach(entry => {
              adCosts.values.push(
                {
                  type: '总销售额',
                  x: entry[1],
                  y: Number.parseFloat(entry[3]),
                  cost: Number.parseFloat(entry[2]),
                  country
                }
                // {
                //   type: '广告花费',
                //   x: entry[1],
                //   y: parseFloat(entry[2]),
                //   country: country
                // }
              );
            });
            parsedData.forEach(item => {
              adSales.values.push({
                x: item[1],
                type: '广告费用占比',
                y:
                  Number.parseFloat(item[3]) && Number.parseFloat(item[2]) > 0
                    ? Number.parseFloat(item[4] * 100).toFixed(2)
                    : 0,
                country
              });
            });
            break;
          case 'D-AdSAPie':
            parsedData.forEach(entry => {
              adCosts.values.push(
                {
                  type: '总销售额',
                  x: entry[1],
                  y: Number.parseFloat(entry[3]),
                  cost: Number.parseFloat(entry[2]),
                  cost_ratio:
                    Number.parseFloat(entry[3]) && Number.parseFloat(entry[2]) > 0
                      ? Number.parseFloat(entry[4] * 100).toFixed(2)
                      : 0,
                  country
                }
                // {
                //   type: '广告花费',
                //   x: entry[1],
                //   y: parseFloat(entry[2]),
                //   country: country
                // }
              );
            });
            break;
          case 'D-OrderR':
          case 'W-OrderR':
            parsedData.forEach(item => {
              // console.log(item, "item");
              chartData.values.push({
                amount: Number.parseFloat(item[2]).toFixed(2) || 0,
                type: '销售额',
                time: item[1],
                country
              });
            });
            break;
          case 'D-OrderU':
          case 'W-OrderU':
            parsedData.forEach(item => {
              chartData.values.push({
                amount: Number.parseInt(item[3]) || 0,
                type: '销售数量',
                time: item[1],
                country
              });
            });
            break;
          case 'D-OrderO':
          case 'W-OrderO':
            parsedData.forEach(item => {
              chartData.values.push({
                amount: Number.parseInt(item[4]) || 0,
                type: '订单量',
                time: item[1],
                country
              });
            });
            break;
          case 'D-Trim':
            parsedData.forEach(entry => {
              console.log(entry, 'entry');
              chartData.values.push({
                x: entry[0],
                type: entry[1],
                country,
                y: entry[2]
              });
            });
            // 根据type类型加和所有的y
            // const typeCountrySums = parsedData.reduce((acc, entry) => {

            //   const key = `${entry[1]}_${country}`;
            //   const value = parseFloat(entry[2]);
            //   if (!acc[key]) {
            //     acc[key] = 0;
            //   }
            //   acc[key] += value;
            //   return acc;
            // }, {});
            // // console.log(typeCountrySums, "typeCountrySums");
            // Object.keys(typeCountrySums).forEach(key => {
            //   const [type, country] = key.split('_');
            //   chartData.values.push({
            //     type: type,
            //     country: country,
            //     y: typeCountrySums[key]
            //   });
            // });
            break;
          case 'D-CALL':
            // parsedData.forEach(entry => {
            adCosts.values.push({
              x: parsedData[0][0],
              country,
              type: parsedData[0][1] === '扫描广告活动' ? '量化广告分析' : parsedData[0][1],
              add: parsedData[1][2],
              y: parsedData[0][2]
            });
            // });
            break;
          case 'D-AdCam':
            adCosts.values.push(
              ...parsedData.flatMap(
                item => JSON.parse(item[2]) // Ensure to flatten the array if JSON parsing returns an array
              )
            );
            break;
        }
      });
      if (
        old_data.name === 'W-FBA' ||
        old_data.name === 'D-Ad' ||
        old_data.name === 'D-Sales' ||
        old_data.name === 'D-CALL' ||
        old_data.name === 'D-Effect' ||
        old_data.name === 'W-Effect' ||
        old_data.name === 'D-AdSABar' ||
        old_data.name === 'D-AdSAPie' ||
        old_data.name === 'D-AdCam'
      ) {
        chartData = [adCosts, adSales];
      }
      if (old_data.name === 'D-Trim') {
        // 将chartData.values按照type类型和country加和
        const typeCountrySums = chartData.values.reduce((acc, entry) => {
          const key = `${entry.type}_${entry.country}`;
          const value = Number.parseFloat(entry.y);

          if (!acc[key]) {
            acc[key] = 0;
          }
          acc[key] += value;
          return acc;
        }, {});
        const newValues = [];
        Object.keys(typeCountrySums).forEach(key => {
          const [type, country] = key.split('_');
          newValues.push({
            type,
            country,
            y: typeCountrySums[key]
          });
        });
        chartData.values = newValues;
        console.log(chartData.values, 'chartData.values');
      }
      // if (old_data.name === "W-KL") {
      //   // 根据 date 和 type  对quantity进行加和
      //   const typeDateSums = chartData.values.reduce((acc, entry) => {
      //     const key = `${entry.date}_${entry.type}`;
      //     const value = parseInt(entry.quantity);
      //     if (!acc[key]) {
      //       acc[key] = 0;
      //     }
      //     acc[key] += value;
      //     return acc;
      //   }, {});
      //   console.log(typeDateSums, "typeDateSums");
      //   Object.keys(typeDateSums).forEach(key => {
      //     const [date, type] = key.split('_');
      //     chartData.values.push({
      //       date: date,
      //       type: type,
      //       quantity: typeDateSums[key]
      //     });
      //   });
      // }
      return {
        ...old_data.chart,
        data: chartData
      };
    };
    return handleChartData;
  }, [selectedCountry]);
};

// const updateSpecWithData = (apiData, spec) => {
//   if (apiData.length === 0) {
//     return spec.map(s => ({
//       ...s,
//       value: "N/A",
//       num: "N/A",
//       type: "stable",
//       data: {
//         ...s.data,
//         data: {
//           ...s.data.data,
//           values: [] // 确保数据部分为空，但保留其他配置
//         }
//       }
//     }));
//   }
//   const newSpec = spec.map(s => ({
//     ...s,
//     data: {
//       ...s.data,
//       data: {
//         ...s.data.data,
//         values: [] // 初始化空数组
//       }
//     }
//   }));
//   // console.log(apiData[apiData.length - 1], "apiData");
//   const metricsMapping = {
//     "Impression": 2,
//     "Clicks": 3,
//     "Order": 4,
//     "ACOS": 5,
//     "CTR": 6,
//     "Conversion Rate": 7
//   };

//   // Clear existing data values and update the latest value
//   newSpec.forEach(s => {
//     s.data.data.values = [];
//     const lastData = apiData[apiData.length - 1].showTableData[0];
//     const metricIndex = metricsMapping[s.title];
//     const lastValue = parseFloat(lastData[metricIndex]);
//     // 计算指标的总和
//     const sum = apiData.reduce((acc, item) => acc + parseFloat(item.showTableData[0][metricIndex]), 0);
//     // 如果时CTR，则四舍五入保留两位小数
//     if (s.title === "CTR" || s.title === "Conversion Rate" || s.title === "ACOS") {
//       // s.value = parseFloat(lastData[metricIndex]).toFixed(2) + '%';
//       s.value = (sum / apiData.length).toFixed(2) + '%'; // 计算平均值并格式化
//     } else {
//       s.value = sum; // Update to the last value from the API data
//     }

//     // 计算所有值的平均
//     const total = apiData.reduce((acc, item) => acc + parseFloat(item.showTableData[0][metricIndex]), 0);
//     const average = total / apiData.length;

//     // 计算百分比变化
//     if (average !== 0) {
//       const change = ((lastValue - average) / average) * 100;
//       s.num = change.toFixed(2);
//       s.type = change > 0 ? 'rise' : 'decline';
//     } else {
//       s.num = 'N/A'; // 处理除以零的情况
//       s.type = 'stable'; // 如果平均值为零，则认为是稳定的
//     }
//   });

//   // Populate new data from API
//   apiData.forEach(item => {
//     const date = item.showTableData[0][0]; // Assuming the date is the first element
//     const country = item.showTableData[0][1]; // Assuming the date is the first element
//     Object.keys(metricsMapping).forEach(metric => {
//       const valueIndex = metricsMapping[metric];
//       const value = item.showTableData[0][valueIndex];
//       const specItem = newSpec.find(s => s.title === metric);
//       if (specItem) {
//         if (metric === "CTR" || metric === "Conversion Rate" || metric === "ACOS") {
//           specItem.data.data.values.push({ time: date, country: country, value: parseFloat(value).toFixed(2), });
//         } else {
//           specItem.data.data.values.push({ time: date, country: country, value: parseFloat(value) });
//         }
//       }
//     });
//   });

//   // Calculate and update Ads type data
//   const adsTypeSpec = newSpec.find(s => s.title === "Ads type");
//   console.log(adsTypeSpec, "adsTypeSpec");
//   if (adsTypeSpec) {
//     const spSales = apiData.map(item => parseFloat(item.showTableData[0][11] || 0));
//     const sdSales = apiData.map(item => parseFloat(item.showTableData[0][12] || 0));
//     const sbSales = apiData.map(item => parseFloat(item.showTableData[0][13] || 0));

//     const totalSP = spSales.reduce((acc, curr) => acc + curr, 0);
//     const totalSD = sdSales.reduce((acc, curr) => acc + curr, 0);

//     const totalSB = sbSales.reduce((acc, curr) => acc + curr, 0);
//     const total = totalSP + totalSD + totalSB;
//     console.log(totalSP, totalSD, totalSB, total, "total");
//     console.log(parseFloat((totalSP / total) * 100).toFixed(2), "totalSP");
//     console.log(parseFloat((totalSD / total) * 100).toFixed(2), "totalSD");
//     console.log(parseFloat((totalSB / total) * 100).toFixed(2), "totalSB");
//     adsTypeSpec.data.data = [
//       {
//         // id: 'id0',
//         values: [
//           { type: 'SP', value: parseFloat((totalSP / total) * 100).toFixed(2), sales: parseFloat(totalSP).toFixed(2) },
//           { type: 'SD', value: parseFloat((totalSD / total) * 100).toFixed(2), sales: parseFloat(totalSD).toFixed(2) },
//           { type: 'SB', value: parseFloat((totalSB / total) * 100).toFixed(2), sales: parseFloat(totalSB).toFixed(2) }
//         ]
//       }

//     ];
//     newSpec.forEach(s => {
//       if (s.title === "Ads type") {
//         s.data.data = adsTypeSpec.data.data;
//       }
//     });
//   }

//   return newSpec;
// }

const updateSpecWithData = (apiData, spec) => {
  if (apiData.length === 0) {
    return spec.map(s => ({
      ...s,
      value: 'N/A',
      num: 'N/A',
      type: 'stable',
      data: {
        ...s.data,
        data: {
          ...s.data.data,
          values: [] // 确保数据部分为空，但保留其他配置
        }
      }
    }));
  }
  const newSpec = spec.map(s => ({
    ...s,
    data: {
      ...s.data,
      data: {
        ...s.data.data,
        values: [] // 初始化空数组
      }
    }
  }));
  // console.log(apiData[apiData.length - 1], "apiData");
  const metricsMapping = {
    Impression: 2,
    Clicks: 3,
    Order: 4,
    ACOS: 5,
    CTR: 6,
    'Conversion Rate': 7
  };

  // Clear existing data values and update the latest value
  newSpec.forEach(s => {
    s.data.data.values = [];
    const lastData = apiData[apiData.length - 1].showTableData[0];
    const metricIndex = metricsMapping[s.title];
    const lastValue = new BigNumber(lastData[metricIndex]);
    // 计算指标的总和
    const sum = apiData.reduce(
      (acc, item) => acc.plus(new BigNumber(item.showTableData[0][metricIndex])),
      new BigNumber(0)
    );
    // 如果时CTR，则四舍五入保留两位小数
    if (s.title === 'CTR' || s.title === 'ACOS') {
      s.value = `${sum.dividedBy(apiData.length).toFixed(2)}%`; // 计算平均值并格式化
    } else if (s.title === 'Conversion Rate') {
      // 需要乘以100
      s.value = `${sum.dividedBy(apiData.length).multipliedBy(100).toFixed(2)}%`; // 计算平均值并格式化
    } else {
      s.value = sum.toString(); // Update to the last value from the API data
    }

    // 计算所有值的平均
    const total = apiData.reduce(
      (acc, item) => acc.plus(new BigNumber(item.showTableData[0][metricIndex])),
      new BigNumber(0)
    );
    const average = total.dividedBy(apiData.length);

    // 计算百分比变化
    if (!average.isZero()) {
      const change = lastValue.minus(average).dividedBy(average).multipliedBy(100);
      s.num = change.toFixed(2);
      s.type = change.isGreaterThan(0) ? 'rise' : 'decline';
    } else {
      s.num = 'N/A'; // 处理除以零的情况
      s.type = 'stable'; // 如果平均值为零，则认为是稳定的
    }
  });

  // Populate new data from API
  apiData.forEach(item => {
    const date = item.showTableData[0][0]; // Assuming the date is the first element
    const country = item.showTableData[0][1]; // Assuming the date is the first element
    Object.keys(metricsMapping).forEach(metric => {
      const valueIndex = metricsMapping[metric];
      const value = item.showTableData[0][valueIndex];
      const specItem = newSpec.find(s => s.title === metric);
      if (specItem) {
        if (metric === 'CTR' || metric === 'ACOS') {
          specItem.data.data.values.push({ time: date, country, value: new BigNumber(value).toFixed(2) });
        } else if (metric === 'Conversion Rate') {
          // 需要乘以100
          specItem.data.data.values.push({
            time: date,
            country,
            value: new BigNumber(value).multipliedBy(100).toFixed(2)
          });
        } else {
          specItem.data.data.values.push({ time: date, country, value: new BigNumber(value).toString() });
        }
      }
    });
  });

  // Calculate and update Ads type data
  const adsTypeSpec = newSpec.find(s => s.title === 'Ads type');
  console.log(adsTypeSpec, 'adsTypeSpec');
  if (adsTypeSpec) {
    const spSales = apiData.map(item => new BigNumber(item.showTableData[0][11] || 0));
    const sdSales = apiData.map(item => new BigNumber(item.showTableData[0][12] || 0));
    const sbSales = apiData.map(item => new BigNumber(item.showTableData[0][13] || 0));

    const totalSP = spSales.reduce((acc, curr) => acc.plus(curr), new BigNumber(0));
    const totalSD = sdSales.reduce((acc, curr) => acc.plus(curr), new BigNumber(0));
    const totalSB = sbSales.reduce((acc, curr) => acc.plus(curr), new BigNumber(0));
    const total = totalSP.plus(totalSD).plus(totalSB);

    adsTypeSpec.data.data = [
      {
        values: [
          { type: 'SP', value: totalSP.dividedBy(total).multipliedBy(100).toFixed(2), sales: totalSP.toFixed(2) },
          { type: 'SD', value: totalSD.dividedBy(total).multipliedBy(100).toFixed(2), sales: totalSD.toFixed(2) },
          { type: 'SB', value: totalSB.dividedBy(total).multipliedBy(100).toFixed(2), sales: totalSB.toFixed(2) }
        ]
      }
    ];
    newSpec.forEach(s => {
      if (s.title === 'Ads type') {
        s.data.data = adsTypeSpec.data.data;
      }
    });
  }

  return newSpec;
};

const ad_raw = [
  {
    key: 1,
    value: 'NA',
    label: '北美',
    children: [
      { value: 'US', label: '美国' },
      { value: 'CA', label: '加拿大' },
      { value: 'MX', label: '墨西哥' },
      { value: 'BR', label: '巴西' }
    ]
  },
  {
    key: 2,
    value: 'EU',
    label: '欧洲',
    children: [
      { value: 'UK', label: '英国' },
      { value: 'FR', label: '法国' },
      { value: 'DE', label: '德国' },
      { value: 'IT', label: '意大利' },
      { value: 'ES', label: '西班牙' },
      { value: 'NL', label: '荷兰' },
      { value: 'SE', label: '瑞典' },
      { value: 'PL', label: '波兰' },
      { value: 'BE', label: '比利时' },
      { value: 'IE', label: '爱尔兰' },
      { value: 'IN', label: '印度' },
      { value: 'TR', label: '土耳其' },
      { value: 'ZA', label: '南非' },
      { value: 'EG', label: '埃及' },
      { value: 'SA', label: '沙特阿拉伯' },
      { value: 'AE', label: '阿联酋' }
    ]
  },
  {
    key: 3,
    value: 'FE',
    label: '远东',
    children: [
      { value: 'JP', label: '日本' },
      { value: 'SG', label: '新加坡' },
      { value: 'AU', label: '澳大利亚' }
    ]
  }
];

// 根据传入的国家返回大洲以及国家中文名称
const getContinentAndCountryName = countryCode => {
  const continent = ad_raw.find(item => item.children.some(child => child.value === countryCode))?.value;
  const countryName = ad_raw
    .find(item => item.children.some(child => child.value === countryCode))
    ?.children.find(child => child.value === countryCode)?.label;
  return { continent, countryName };
};

export { useChartData, updateSpecWithData, mergeChartData, getMonth, getContinentAndCountryName };
