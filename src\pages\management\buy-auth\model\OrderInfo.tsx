import { useState, useEffect, useContext } from 'react';
import { useSearchParams } from 'react-router-dom';
import { getOrderShopinfo, createPackageOrder } from '@/service/api';
import { Table, Skeleton, Checkbox, Alert } from 'antd';
import { AppContext } from './AppContext';
import BigNumber from 'bignumber.js';
import { selectUserInfo } from '@/store/slice/auth';
// import AgreementModal from './AgreementModal';
import { removeActiveTab, removeTabByRouteName, resetTabLabel, setTabLabel } from '@/store/slice/tab';
import './order-info.css';
const AgreementModal = lazy(() => import('./AgreementModal'));
export default function OrderInfo() {
  const [searchParams] = useSearchParams();
  const [orderInfo, setOrderInfo] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [jumpUrl, setJumpUrl] = useState<string | undefined>(undefined);
  const dataParam = searchParams.get("data");
  const OneMonthKeyParam = searchParams.get("OneMonthKey");
  const { selectedPackage, selectedCoupon,setShowPayModal, parseFormattedData} = useContext(AppContext)!;
  const [agreementChecked, setAgreementChecked] = useState(false);
  const [showAgreementAlert, setShowAgreementAlert] = useState(false);
  const agreementModalRef = useRef<any>(null);
  const userInfo = useAppSelector(selectUserInfo);
  const [initialData, setInitialData] = useState<any>(null);
  const nav = useNavigate();
  const dispatch = useAppDispatch();


  const fetchOrderInfo = async () => {
    setLoading(true);
    try {
      const shopData = parseFormattedData();
      console.log(shopData,"shopData====");
      const res = await getOrderShopinfo({ ShopData: shopData });
      if (res && res.data) {
        setInitialData(res.data); 
        const formattedData = formatOrderData(res.data);
        setOrderInfo(formattedData);
      }
    } catch (error) {
      console.error('Failed to fetch order info:', error);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  const formatOrderData = (data: any) => {
    const serviceDuration = `${selectedPackage?.ServiceMonth}个月 ${selectedPackage?.ServiceAddDay ? `(${selectedPackage?.ServiceMonth *30}天赠送${selectedPackage?.ServiceAddDay}天)` : ''}`;
    const fee = new BigNumber(selectedPackage?.Fee || 0);

    return Object.values(data).map((shop: any) => {
      const payorder = parseFormattedData()[shop.ID] || '';
      const countryCount = payorder.split(',').length;
      // 计算总费用 最小为0 不能是负数
      const totalFee = fee.multipliedBy(countryCount).gt(0) ? fee.multipliedBy(countryCount) : new BigNumber(0);
      // console.log(totalFee,"totalFee====");
      return {
        key: shop.ID,
        shopName: shop.ShopName,
        countries: payorder,
        serviceDuration,
        estimatedPrice: totalFee
      };
    });
  };

  const handleConfirm = async () => {
    if (!agreementChecked) {
        setShowAgreementAlert(true);
        return;
      }
      setShowAgreementAlert(false);
    if(userInfo.Balance < finalPrice.toNumber()){
        window.$message?.error('账户余额不足');
        setShowPayModal(true);
        return;
    }
    const requestData = {
      ServerPackageID: selectedPackage?.ID,
      ShopData: parseFormattedData(),
      ...selectedCoupon && { CouponID: selectedCoupon?.ID },
      ...OneMonthKeyParam && { OneMonthKey: OneMonthKeyParam }
    };
    const res = await createPackageOrder(requestData);
    if (res && res.data) {
      setJumpUrl(res.data.JumpUrl);
      setModalVisible(true);
     
      if (!res.data.JumpUrl) {
        setTimeout(() => {
          setModalVisible(false);
          dispatch(removeActiveTab());
          // window.location.href = '/management/auth?new='+ dataParam;
          nav('/management/auth?new='+ dataParam);
        }, 1000);
      }else{
        window.open(res.data.JumpUrl, '_blank');
      }
    }
  };

    // 更新订单信息的函数（不调用API）
    const updateOrderInfo = () => {
      if (initialData) {
        const formattedData = formatOrderData(initialData);
        setOrderInfo(formattedData);
      }
    };

    useEffect(() => {
      // 只在第一次有 selectedPackage 时获取数据
      if (selectedPackage && !initialData) {
        fetchOrderInfo();
      } else if (initialData) {
        // 后续更新时只重新计算
        updateOrderInfo();
      }
    }, [selectedPackage, selectedCoupon]);

  const totalEstimatedPrice = orderInfo.reduce((acc, item) => acc.plus(item.estimatedPrice), new BigNumber(0));
  const couponDiscount = new BigNumber(selectedCoupon?.CouponAmount || 0);
  const finalPrice = totalEstimatedPrice.minus(couponDiscount);

  const columns = [
    {
      title: '店铺',
      dataIndex: 'shopName',
      key: 'shopName',
    //   align: 'center',
    },
    {
      title: '激活国家',
      dataIndex: 'countries',
      key: 'countries',
    //   align: 'center',
    },
    {
      title: '服务时长',
      dataIndex: 'serviceDuration',
      key: 'serviceDuration',
    //   align: 'center',
    },
    {
      title: '预计费用',
      dataIndex: 'estimatedPrice',
      key: 'estimatedPrice',
    //   align: 'center',
      render: (text: BigNumber) =>{
        return <AStatistic 
        value={text.toNumber()}
        precision={2}
        prefix="¥"
        valueStyle={{
        //   color: '#E52E2E',
          fontSize: '16px',
        //   fontWeight: 500,
        //   display: 'inline-block'
          }}
        />
      } 
    },
  ];

  return (
    <div className="mb-6 order-info">
        {/* 协议弹窗 */}
        <AgreementModal ref={agreementModalRef} onAgree={() =>{
             setAgreementChecked(true)
             setShowAgreementAlert(false)
             agreementModalRef.current?.close()
        }} />
      <h3 className="text-base font-500 text-lg flex items-center mb-4">
        <SvgIcon localIcon="order-info" className="w-6 h-6 mr-2" />
        订单信息
      </h3>
      {loading ? (
        <Skeleton active paragraph={{ rows: 4 }} />
      ) : (
        <>
          <Table
            columns={columns}
            dataSource={orderInfo}
            pagination={false}
            bordered
            summary={() => (
                <Table.Summary.Row className='text-lg font-500'>
                  <Table.Summary.Cell colSpan={3}>合计</Table.Summary.Cell>
                  <Table.Summary.Cell>
                    {couponDiscount.isZero() ? (
                      <AStatistic 
                        value={totalEstimatedPrice.toNumber()}
                        precision={2}
                        prefix="¥"
                        valueStyle={{
                          color: '#E52E2E',
                          fontSize: '20px',
                          fontWeight: 500,
                          display: 'inline-block'
                        }}
                      />
                    ) : (
                      <span className='flex items-center'>
                        <AStatistic 
                          value={totalEstimatedPrice.toNumber()}
                          precision={2}
                          prefix="¥"
                          valueStyle={{
                            // color: '#E52E2E',
                            fontSize: '18px',
                            fontWeight: 500,
                            display: 'inline-block'
                          }}
                        />
                        {' - '}
                        <AStatistic 
                          value={couponDiscount.toNumber()}
                          precision={2}
                          prefix="¥"
                          valueStyle={{
                            // color: '#E52E2E',
                            fontSize: '18px',
                            fontWeight: 500,
                            display: 'inline-block'
                          }}
                        />
                       <span className='mx-1'> {' = '}</span>
                        <AStatistic 
                          value={finalPrice.toNumber()}
                          precision={2}
                          prefix="¥"
                          valueStyle={{
                            color: '#E52E2E',
                            fontSize: '20px',
                            fontWeight: 500,
                            display: 'inline-block'
                          }}
                        />
                      </span>
                    )}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
          />

        <div className="mt-4 text-gray-600 text-sm flex items-center justify-end pl-2">
            <Checkbox
              checked={agreementChecked}
              onChange={(e) => {
                setAgreementChecked(e.target.checked)
                if(e.target.checked){
                  setShowAgreementAlert(false)
                }
              }}
            >
              同意   <span className='text-primary' onClick={(e) => {
                  e.preventDefault(); // 阻止默认行为
                  e.stopPropagation(); // 阻止事件冒泡
                agreementModalRef.current?.open()
              }}>
                    ATLAS 产品服务协议
                    </span>
            </Checkbox>
          </div>
          {showAgreementAlert && (
            <Alert
              message={
                <p>请先同意并勾选
                    <span className='text-primary ml-1' onClick={() => agreementModalRef.current?.open()}>
                    ATLAS 产品服务协议
                    </span>
                </p>
              }
              type="error"
              showIcon
              className="mt-2 w-70 ml-auto"
            />
          )}
          <div className="mt-4 text-gray-600 text-sm flex items-center justify-between pl-2">
            站点费用按使用计算，根据选择的站点数量和服务时长，系统会自动显示费用总计。
            <AButton type="primary" onClick={handleConfirm}>确认激活</AButton>
          </div>
        </>
      )}
      <PaymentModal
        visible={modalVisible}
        onClose={() => {
            setModalVisible(false)
            // window.location.href = '/management/auth';
            dispatch(removeActiveTab());
            nav('/management/auth');
        }}
        jumpUrl={jumpUrl}
        redirectToUrl={() => {
          setModalVisible(false);
          dispatch(removeActiveTab());
          // window.location.href = '/management/auth';
          nav('/management/auth');
        }}
      />
    </div>
  );
}