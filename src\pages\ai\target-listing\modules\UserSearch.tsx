import { Col, Form, Input, Row, Select, DatePicker } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect } from 'react';
import dayjs from 'dayjs';
import { selectUserInfo } from '@/store/slice/auth';

interface Props {
  localSearch: (params: any, isLocalSearch: boolean) => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ search, localSearch, form, loading }) => {
  const defaultValues = { manageState: 1, date: dayjs().subtract(1, 'day') };
  const [defaultDate, setDefaultDate] = useState(dayjs().subtract(1, 'day'));
  const userInfo = useAppSelector(selectUserInfo);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      UID: userInfo.active_shop_id,
      targeting: values.targeting,
      state: values.state,
      Date: values.date ? values.date.format('YYYY-MM-DD') : undefined,
    };
    // 清除其他form表单中的内容
    // form.resetFields();/*  */
    localSearch(params, true);
  };

  useEffect(() => {
    form.setFieldsValue(defaultValues);
    search({});
  }, []);


  const handleDateChange = (value: any) => {

    // 检查选择的日期是否与当前日期相同
    if (value && dayjs(value).isSame(defaultDate, 'day')) {
      return; // 如果选择的日期与当前相同，则不更新
    }

    setDefaultDate(value);
    search({});
  };

  return (
    <Form form={form} disabled={loading} initialValues={defaultValues}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={12} lg={4}>
          <Form.Item
            className="m-0"
            name="targeting"
            label="名称"
          >
            <Input
              placeholder="请输入名称"
              onChange={handleSearch}
              allowClear
            />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item
            className="m-0"
            name="state"
            label="状态"
          >
            <Select
              placeholder="请选择状态"
              allowClear
              options={[
                { label: '开启', value: 'enabled' },
                { label: '暂停', value: 'paused' },
              ]}
              onChange={handleSearch}
            />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item
            className="m-0"
            name="date"
            label="日期"
          >
            <DatePicker
              allowClear={false}
              format="YYYY-MM-DD"
              disabledDate={(current: any) => {
                return current && current > dayjs().endOf('day');
              }}
              onChange={handleDateChange}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;