import './coupons.css'
interface CouponCardProps {
  valid?: boolean;
  data: {
    id: number;
    couponDescription: string;
    validDate: string;
    number: number;
    amount: number;
    unit: string;
    CommonDenominator: number;
    Used: number;
    couponInfo: string;
    couponLeftTimes: number;
  };
  onSelect: () => void;
}

export default function CouponCard({ valid = false, data, onSelect }: CouponCardProps) {
  const {
    couponDescription = '折扣卷',
    validDate = '有效期未知',
    CommonDenominator = 0,
    amount = 0,
    couponInfo = '',
    couponLeftTimes = 0,
  } = data || {};
  return (
    <div className={`parentContainer relative ${valid ? 'selected' : ''} ${`used${data.Used ? 1 : 0}`}`} onClick={onSelect}>
        {data.couponLeftTimes > 0 && (
          <div className="absolute -right-1 sm:-right-2 -top-1 sm:-top-2 
            bg-gradient-to-r from-[#fff7e6] to-[#ffd591]
            text-red-500 px-2 sm:px-3 py-0.5 sm:py-1 
            rounded-full text-xs sm:text-sm font-medium 
            transform rotate-12 shadow-lg z-10 ">
            剩余{data.couponLeftTimes}张
          </div>
        )}
      <div className="container">
    
        <div className="left" />
        <div className="right">
          <div className="title">{couponDescription}</div>
          {/* <div className="desc">总额¥{CommonDenominator}</div> */}
          <div className="validDate">有效期至{validDate}</div>
          {/* CouponInfo */}
          <div className="couponInfo mt-2">
            <ATag color="#ffd591">
              <span className="text-sm text-[#d48806]">
                {couponInfo}
              </span>
            </ATag>
          </div>
        </div>
        <div className="amountContainer">
          <span className="amount flex items-center"><span className='text-sm mr-1'></span>¥{amount.toFixed(2)}</span>
          <button className="useButton">{!data.Used ? "立即使用" : data.Used == 1 ? '已使用' : data.Used == 0 ? '立即使用' : '已过期'} </button>
        </div>
        <div className="topSemicircle" />
        <div className="bottomSemicircle" />
      </div>
    </div>
  );
}