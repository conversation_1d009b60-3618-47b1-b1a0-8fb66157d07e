import { request } from '../request';

// 充值-现有的套餐
//   POST /user/rechage_package
export const rechagePackage = (data: any) => request({ url: "/user/rechage_package", method: "post", data })

// 充值-仅充值
//   POST /user/create_recharge_order
export const createRechargeOrder = (data: any) => request({ url: "/user/create_recharge_order", method: "post", data })


// 购买服务1-站点信息
//   POST /user/get_order_shopinfo
export const getOrderShopinfo = (data: any) => request({ url: "/user/get_order_shopinfo", method: "post", data })

// 购买服务2-拥有的优惠券
//   POST /user/have_coupon
export const haveCoupon = (data: any) => request({ url: "/user/have_coupon", method: "post", data })

// 购买服务2-系统存在的服务套餐
//   POST /user/service_package
export const servicePackage = (data: any) => request({ url: "/user/service_package", method: "post", data })


// 购买服务2-Action 包括余额足够/不足充值
//   POST /user/create_package_order
export const createPackageOrder = (data: any) => request({ url: "/user/create_package_order", method: "post", data })


// 充值消费记录
//   POST /user/rechage_blance_list
export const rechageBlanceList = (data: any) => request({ url: "/user/rechage_blance_list", method: "post", data })


// 服务购买记录 
//   POST /user/service_buy_list
export const serviceBuyList = (data: any) => request({ url: "/user/service_buy_list", method: "post", data })


// 继续支付
//   POST /user/continue_pay
export const continuePay = (data: any) => request({ url: "/user/continue_pay", method: "post", data })
