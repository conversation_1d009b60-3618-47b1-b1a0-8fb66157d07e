const ServicePrice: React.FC = () => {
  return (
    <div
      className="relative cursor-pointer"
      onClick={() => {
        window.open('https://www.deepbi.cn/charge');
      }}
    >
      <div className="absolute left-[-85px] top-[-30px]">
        <p className="whitespace-nowrap bg-[#154EC1] px-2 py-1 text-sm text-white">服务价格</p>

        {/* <p className="border-l-2  border-white h-1"></p> */}
        <div className="h-0 w-0 border-l-[36px] border-r-[36px] border-t-[5px] border-l-transparent border-r-transparent border-t-[#154EC1]"></div>
      </div>
    </div>
  );
};

export default memo(ServicePrice);
