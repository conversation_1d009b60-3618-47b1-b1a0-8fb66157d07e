.parentContainer {
  position: relative;
  margin: 10px;
  /* overflow: hidden; */
  /* transition: background-color 0.3s, border 0.3s; */
  transition: all 0.3s ease;
  transform: translateY(0);
}
.parentContainer:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 选中状态动画 */
.parentContainer.selected .container {
  animation: pulse 1s;
}


.container {
  display: flex;
  border-radius: 8px;
  min-width: 350px;
  height: 110px;
  overflow: hidden;
  background-color: #fff7e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* .left {
  width: 12px;
  background-image: radial-gradient(circle at center, transparent 6px, #ffd591 4px);
  background-size: 20px 15px;
} */

.right {
  flex: 1;
  padding: 15px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color:rgb(152, 85, 18);
}

.desc {
  font-size: 12px;
  color: #d48806;
  margin-top: 6px;
}

.validDate {
  font-size: 12px;
  color: #d48806;
  margin-top: 6px;
}

.amountContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  padding: 15px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  color: #ff4d4f;
}

.useButton {
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  margin-top: 10px;
  cursor: pointer;
}

.topSemicircle,
.bottomSemicircle {
  width: 20px;
  height: 20px;
  border: 1px solid #ffd591;
  border-radius: 10px;
  position: absolute;
  background-color: #fff;
}

.topSemicircle {
  top: -10px;
  left: 98px;
}

.bottomSemicircle {
  bottom: -10px;
  left: 98px;
}

.selected {
  transform: scale(1.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  /* border: 2px solid #ffa940; */
}

.used1 {
  opacity: 0.5;
  cursor: not-allowed;
}
.used1 .container {
  background-color: #f0f0f0 !important;
}
.used1 .amount {
  color: #4e4d4d !important;
}

.used1 .useButton {
  background-color: #999 !important;
}

.coupons-container .ant-collapse-header {
  padding: 0 !important;
  display: flex;
  align-items: center;
  /* 翻转 */
  flex-direction: row-reverse;
}




/* 选中状态动画 */
.parentContainer.selected .container {
  animation: pulse 1s;
}

/* 使用按钮悬浮效果 */
.useButton {
  transition: all 0.3s ease;
  transform: scale(1);
}

.useButton:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(255, 169, 64, 0.3);
}

/* 金额文字效果 */
.amount {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 选中状态的脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 169, 64, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 169, 64, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 169, 64, 0);
  }
}

/* 已使用状态的过渡效果 */
.parentContainer.used1 .container {
  transition: all 0.3s ease;
}