import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Modal, Input, Select, Button, Table, Switch, message } from 'antd';
import { SearchOutlined, CloseCircleFilled } from '@ant-design/icons';
import { getParentSonAsinList, GetSonAsinAdStatus, ControlSonAsin } from '@/service/api';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';

interface AsinStatusSetterProps {
  countryCode: string;
  parentAsin: string;
  disabled: boolean;
}

interface AsinStatus {
  ID?: number;
  Asin: string;
  AdCloseFlag: boolean;
  ProcessState: number;
  Title?: string;
}

// 添加节流函数
function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  let lastExec = 0;

  return function(this: any, ...args: Parameters<T>) {
    const context = this;
    const now = Date.now();
    const remaining = wait - (now - lastExec);

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      lastExec = now;
      func.apply(context, args);
    } else if (!timeout) {
      timeout = setTimeout(() => {
        lastExec = Date.now();
        timeout = null;
        func.apply(context, args);
      }, remaining);
    }
  };
}

const AsinStatusSetter: React.FC<AsinStatusSetterProps> = ({
  countryCode,
  parentAsin,
  disabled
}) => {
  const { t } = useTranslation();
  const {Search} = Input
  const [loading, setLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState('全部');
  const [asinList, setAsinList] = useState<string[]>([]);
  const [asinStatusMap, setAsinStatusMap] = useState<Record<string, AsinStatus>>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filteredAsins, setFilteredAsins] = useState<any[]>([]);
  // 添加一个记录处理中ASIN的集合
  const [processingAsins, setProcessingAsins] = useState<Set<string>>(new Set());

  // 在组件中添加分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
  });

  // 获取子ASIN列表
  const fetchAsinList = async () => {
    if (!countryCode || !parentAsin) return;
    
    setLoading(true);
    try {
      const res = await getParentSonAsinList({
        CountryCode: countryCode,
        ParentAsin: parentAsin
      });
      console.log(res,"res====");
      console.log(res.data.data[parentAsin].length,"res.data.data[parentAsin]====");
      if (res.data.data[parentAsin]) {
        setAsinList(res.data.data[parentAsin]);
        fetchAsinStatus(res.data.data[parentAsin]);
      } else {
        setAsinList([]);
      }
    } catch (error) {
      console.error('获取ASIN列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取ASIN状态
  const fetchAsinStatus = async (asins: string[]) => {
    if (!countryCode || !parentAsin || asins.length === 0) return;
    
    try {
      const res = await GetSonAsinAdStatus({
        CountryCode: countryCode,
        ParentAsin: parentAsin
      });
      console.log(res,"res====");
      const statusMap: Record<string, AsinStatus> = {};
        
      // 为所有ASIN设置默认状态（开启）
      asins.forEach(asin => {
        statusMap[asin] = {
          Asin: asin,
          AdCloseFlag: false,
          ProcessState: 1
        };
      });
      // 判断对象长度大于0
     if (res?.data && Object.keys(res.data).length > 0) {
     console.log(Object.keys(res.data).length,"len====");
     
        
        // 获取响应数据，处理不同的响应结构
        const responseData = res?.data || (res as any).data || {};
        
        // 更新从API获取的状态
        Object.keys(responseData).forEach(asin => {
          statusMap[asin] = {
            ...responseData[asin],
            Asin: asin
          };
        });
        
       
      }
      setAsinStatusMap(statusMap);
    } catch (error) {
      console.error('获取ASIN状态失败:', error);
    }
  };

  // 原始的toggleAsinStatus函数
  const toggleAsinStatusOriginal = async (asin: string, newStatus: boolean) => {
    // 如果ASIN已经在处理中，则不执行操作
    if (processingAsins.has(asin)) {
      message.warning(t('page.ailisting.asinStatusSetter.operationInProgress', { asin }));
      return;
    }
    
    try {
      // 添加到处理中集合
      setProcessingAsins(prev => new Set(prev).add(asin));
      
      // 更新UI状态为处理中
      setAsinStatusMap(prev => ({
        ...prev,
        [asin]: {
          ...prev[asin],
          ProcessState: 0
        }
      }));
      
      const res = await ControlSonAsin({
        CountryCode: countryCode,
        ParentAsin: parentAsin,
        Asin: asin,
        OpType: newStatus ? 'open' : 'close'
      });
      
      if (res?.data) {
        setAsinStatusMap(prev => ({
          ...prev,
          [asin]: {
            ...prev[asin],
            AdCloseFlag: !newStatus,
            ProcessState: 0
          }
        }));
        window.$message?.success(t('page.ailisting.asinStatusSetter.statusSubmitted', { asin }));
      } else {
        setAsinStatusMap(prev => ({
          ...prev,
          [asin]: {
            ...prev[asin],
            ProcessState: 1
          }
        }));
        // message.error(`操作失败，请重试`);
      }
    } catch (error) {
      console.error('切换状态失败:', error);
      // message.error('切换状态失败');
      
      setAsinStatusMap(prev => ({
        ...prev,
        [asin]: {
          ...prev[asin],
          ProcessState: 1
        }
      }));
    } finally {
      // 从处理中集合移除
      setProcessingAsins(prev => {
        const newSet = new Set(prev);
        newSet.delete(asin);
        return newSet;
      });
    }
  };
  
  // 使用useCallback和throttle创建节流版本的toggleAsinStatus
  const toggleAsinStatus = useCallback(
    throttle((asin: string, newStatus: boolean) => {
      toggleAsinStatusOriginal(asin, newStatus);
    }, 1000), // 1秒内只能执行一次
    [countryCode, parentAsin, processingAsins, asinStatusMap]
  );

  // 批量设置状态
  const batchSetStatus = async (newStatus: boolean) => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('page.ailisting.asinStatusSetter.selectAsin'));
      return;
    }
    
    // 先将所有选中的ASIN状态设置为处理中
    const newStatusMap = { ...asinStatusMap };
    selectedRowKeys.forEach(key => {
      const asin = String(key);
      if (newStatusMap[asin]) {
        newStatusMap[asin].ProcessState = 0; // 设置为处理中状态
      }
    });
    setAsinStatusMap(newStatusMap);
    
    const promises = selectedRowKeys.map(key => {
      const asin = String(key);
      return ControlSonAsin({
        CountryCode: countryCode,
        ParentAsin: parentAsin,
        Asin: asin,
        OpType: newStatus ? 'open' : 'close'
      });
    });
    
    try {
      setLoading(true);
      const results = await Promise.all(promises);
      
      // 检查是否所有操作都成功
      const allSuccess = results.every(res => res?.data);
      
      // 更新最终状态
      const finalStatusMap = { ...asinStatusMap };
      selectedRowKeys.forEach(key => {
        const asin = String(key);
        if (finalStatusMap[asin]) {
          finalStatusMap[asin].ProcessState = 1; // 恢复为正常状态
          if (allSuccess) {
            finalStatusMap[asin].AdCloseFlag = !newStatus;
          }
        }
      });
      
      setAsinStatusMap(finalStatusMap);
      
      if (allSuccess) {
        message.success(newStatus ? t('page.ailisting.asinStatusSetter.batchEnabled') : t('page.ailisting.asinStatusSetter.batchDisabled'));
      } else {
        message.warning(t('page.ailisting.asinStatusSetter.batchOperationPartialFailed'));
      }
      
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('批量设置状态失败:', error);
      message.error(t('page.ailisting.asinStatusSetter.batchOperationFailed'));
      
      // 发生错误时，恢复所有ASIN为正常状态
      const recoveryMap = { ...asinStatusMap };
      selectedRowKeys.forEach(key => {
        const asin = String(key);
        if (recoveryMap[asin]) {
          recoveryMap[asin].ProcessState = 1; // 恢复为正常状态
        }
      });
      setAsinStatusMap(recoveryMap);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (isModalVisible) {
      fetchAsinList();
    }
  }, [isModalVisible, countryCode, parentAsin]);

  // 取消所有选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
  };

  // 过滤和搜索ASIN
  const getFilteredAsins = (search?: string) => {
    if (!isModalVisible) return []
    // 如果提供了搜索值，更新状态
    let currentSearchValue = search !== undefined ? search : searchValue;
    
    // 如果提供了新的搜索值，更新状态
    if (search !== undefined) {
      setSearchValue(search);
    }
    // 去除currentSearchValue的空格
    currentSearchValue = currentSearchValue?.trim();
    // console.log(search,"search====")

    // console.log(asinList,"asinList====");
    // console.log(asinStatusMap,"asinStatusMap====");
    
    const result_data =  asinList
      .filter(item => {
        // 搜索过滤
        if (currentSearchValue && !item.asin.toLowerCase().includes(currentSearchValue.toLowerCase())) {
          return false;
        }
        
        // 状态过滤
        if (statusFilter !== '全部') {
          const status = asinStatusMap[item.asin];
          if (!status) return false;
          
          if (statusFilter === '开启' && (status.ProcessState !== 1 || status.AdCloseFlag)) {
            return false;
          }
          
          if (statusFilter === '关闭' && (status.ProcessState !== 1 || !status.AdCloseFlag)) {
            return false;
          }
          
          if (statusFilter === '处理中' && status.ProcessState !== 0) {
            return false;
          }
        }
        
        return true;
      })
      .map(item => ({
        key: item.asin,
        ...item,
        status: asinStatusMap[item.asin] || { Asin:  item.asin, AdCloseFlag: false, ProcessState: 1 }
      }));
      setFilteredAsins(result_data);
      console.log(result_data,"result_data====");
      // return result_data
  };

  // 添加一个状态变化的副作用
  useEffect(() => {
    console.log("dsadadasdasd ")
    // 当状态过滤器变化时，重新过滤数据
    
    getFilteredAsins();
  }, [statusFilter, asinStatusMap]);

  // 处理分页变化的函数
  const handleTableChange = (newPagination: any) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  const columns = [
    {
      title: t('page.ailisting.asinStatusSetter.columns.asin'),
      dataIndex: 'asin',
      key: 'asin',
      align: 'center',
      width: 450,
      render: (_, record) => {
        // 定义asin 颜色状态
        return (
            <div className='flex items-stretch h-full gap-2 cursor-pointer'>
            
                <LazyImage
                    src={record.first_image}
                    alt={record.item_name || "--"}
                    className='max-h-[100px] object-contain min-w-[80px]'
                    width={100}
                />
                <div className='flex flex-col justify-between w-full'>
                        <div className='line-clamp-2 text-left' >
                            {record.item_name || "--"}
                        </div>
                    <div className='text-start flex flex-nowrap'>
                        <span className='text-gray-400 mr-1 whitespace-nowrap'>{t('page.ailisting.asinStatusSetter.price')}:</span>
                        <div className='flex flex-nowrap'>
                            {record.min_price === record.max_price ?

                                <CurrencySymbol countryCode={countryCode} value={record.min_price} />
                                :
                                <>
                                    <CurrencySymbol countryCode={countryCode} value={record.min_price} />~<CurrencySymbol countryCode={countryCode} value={record.max_price} />
                                </>
                            }
                        </div>
                        <span className='mx-1'> | </span>
                        <span className='whitespace-nowrap'>
                            <span className='text-gray-400 mr-1'>{t('page.ailisting.asinStatusSetter.inventory')}:</span>
                            {record.quantity || "--"}
                        </span>
                    </div>
                    <div className='text-start flex items-center justify-between'>
                        <p className='flex items-center'>
                            ASIN:
                            <a
                                className='text-primary'
                                target="_blank"
                                rel="noopener noreferrer"
                                onClick={(event) => {
                                    event.stopPropagation();
                                    if (record.asin && record.asin !== '-') {
                                        let domain = 'amazon.com';
                                        // 根据国家代码简单设置域名
                                        if (countryCode === 'UK' || countryCode === 'GB') domain = 'amazon.co.uk';
                                        else if (countryCode === 'DE') domain = 'amazon.de';
                                        else if (countryCode === 'FR') domain = 'amazon.fr';
                                        else if (countryCode === 'IT') domain = 'amazon.it';
                                        else if (countryCode === 'ES') domain = 'amazon.es';
                                        else if (countryCode === 'JP') domain = 'amazon.co.jp';
                                        else if (countryCode === 'CA') domain = 'amazon.ca';
                                        window.open(`https://www.${domain}/dp/${record.asin}`, '_blank');
                                    }
                                }}
                            >
                                {record.asin || "-"}
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        );
    }
    },
    {
      title: t('page.ailisting.asinStatusSetter.columns.adDeliveryStatus'),
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: AsinStatus) => {
        if (status.ProcessState === 0) {
          return <span className="text-gray-500">{t('page.ailisting.asinStatusSetter.processing')}</span>;
        }
        return status.AdCloseFlag ? t('page.ailisting.asinStatusSetter.statusOptions.disabled') : t('page.ailisting.asinStatusSetter.statusOptions.enabled');
      }
    },
    {
      title: t('page.ailisting.asinStatusSetter.columns.operation'),
      key: 'operation',
      align: 'center',
      render: (_: any, record: { asin: string, status: AsinStatus }) => {
        const { status } = record;
        const isProcessing = status.ProcessState === 0 || processingAsins.has(record.asin);
        
        if (isProcessing) {
          return <span className="text-gray-400">[{t('page.ailisting.asinStatusSetter.processing')}]</span>;
        }
        
        return (
          <Switch
            checked={!status.AdCloseFlag}
            onChange={(checked) => toggleAsinStatus(record.asin, checked)}
            className={!status.AdCloseFlag ? 'bg-#154EC1' : ''}
            disabled={isProcessing || disabled}
          />
        );
      }
    }
  ];

  // const filteredData = getFilteredAsins();

  // modalTitle: {
  //   display: 'flex',
  //   alignItems: 'center',
  //   gap: '8px',
  //   fontSize: '16px',
  //   color: '#1a1a1a'
  // },
  // aiIcon: {
  //   color: '#154EC1',
  //   fontSize: '20px',
  //   display: 'flex',
  //   alignItems: 'center'
  // }
  // );

  const modalTitle = (
    <div className='flex items-center gap-2 text-primary'>
      <Icon icon="tdesign:setting" style={{ fontSize: '20px' }} />
      <span className='text-#1a1a1a'>{parentAsin} {t('page.ailisting.asinStatusSetter.title')}</span>
    </div>
  );

  return (
    <>
      {/* 主按钮 */}
      <AButton 
        type="link"
        onClick={() => setIsModalVisible(true)}
        size="small"
       
      >
         <div className='flex items-center text-sm'>
         <Icon icon="hugeicons:file-management" className='mr-1' />
        {t('page.ailisting.asinStatusSetter.childAsinManagement')}
        </div>
      </AButton>
     

      <Modal
        title={modalTitle}
        open={isModalVisible}
        onCancel={() => {
          if (!loading) {
            setIsModalVisible(false);
          } else {
            message.warning('数据加载中，请稍候...');
          }
        }}
        maskClosable={!loading}
        width={1000}
        footer={null}
        className="asin-status-setter"
        closable={!loading}
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <span className="mr-2">{t('page.ailisting.asinStatusSetter.columns.asin')}</span>
            <Input
              placeholder={t('page.ailisting.asinStatusSetter.inputAsin')}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onPressEnter={() => getFilteredAsins(searchValue)}
              style={{ width: 150 }}
              className="mr-4"
              allowClear
              suffix={
                <p onClick={() => getFilteredAsins(searchValue)}>
                  <Icon icon="mdi:magnify"  />
                </p>
              }
              onClear={() => {
                setSearchValue('');
                getFilteredAsins('');
              }}
            />
          
            <span className="mr-2">{t('page.ailisting.asinStatusSetter.adStatus')}</span>
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 120 }}
              options={[
                { value: '全部', label: t('page.ailisting.asinStatusSetter.statusOptions.all') },
                { value: '开启', label: t('page.ailisting.asinStatusSetter.statusOptions.enabled') },
                { value: '关闭', label: t('page.ailisting.asinStatusSetter.statusOptions.disabled') },
                { value: '处理中', label: t('page.ailisting.asinStatusSetter.statusOptions.processing') }
              ]}
            />
          </div>
        </div>
        
        <Table
          columns={columns}
          dataSource={filteredAsins}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            pageSizeOptions: ['20', '50','100'],
            total: filteredAsins.length,
            showTotal: (total) => t('page.ailisting.asinStatusSetter.totalItems', { total }),
          }}
          onChange={handleTableChange}
          size="small"
          rowKey="asin"
          scroll={{ y: 500 }}
          className="asin-status-table"
        />
      </Modal>
    </>
  );
};

export default AsinStatusSetter;