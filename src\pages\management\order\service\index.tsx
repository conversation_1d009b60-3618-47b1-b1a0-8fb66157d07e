import { Tooltip } from 'antd';
import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
// import UserSearch from './modules/UserSearch';
import { serviceBuyList, continuePay } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};
const balanceTypeMap: Record<string, string> = {
    INCO: '充值',
    CONS: '消费',
    RETU: '退款',
};

const payTypeMap: Record<string, string> = {
    ALIP: '支付宝',
    WECH: '微信支付',
    PUBA: '对公转账',
    FREE: '赠送',
};

export function Component() {
    const { t } = useTranslation();

    const userInfo = useAppSelector(selectUserInfo);

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const [loading, setLoading] = useState(true);

    const nav = useNavigate();
    // 批量操作弹窗
    const [open, setOpen] = useState(false);

    const [tableData, setTableData] = useState<any[]>([]);

    const [modalVisible, setModalVisible] = useState(false);
    const [jumpUrl, setJumpUrl] = useState('');


    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: serviceBuyList,
            apiParams: {
                page: 1,
                pagesize: 20,
                where: {}
            },
            immediate: false,
            columns: () => [
                {
                    key: 'ID',
                    dataIndex: 'ID',
                    title: '订单ID',
                    align: 'center',
                    hidden: true, // 如果不需要显示，可以保持隐藏
                },
                {
                    key: 'OrderNum',
                    dataIndex: 'OrderNum',
                    title: '订单编号',
                    align: 'center',
                    checked: true,
                    width: 170,
                    // ellipsis: {
                    //     showTitle: false,
                    // },
                    // 用tooltip显示
                    // render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                },
                {
                    key: 'ServiceName',
                    dataIndex: 'ServiceName',
                    title: '服务名称',
                    align: 'center',
                    checked: true,
                    width: 100,
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                },

                {
                    key: 'Content',
                    dataIndex: 'Content',
                    title: '服务内容',
                    align: 'center',
                    checked: true,
                    width: 110,
                    ellipsis: {
                        showTitle: false,
                    },
                    render: (text: string) => {
                        const contentArray = JSON.parse(text);
                        
                        // Group by ShopName
                        const groupedData = contentArray.reduce((acc: any, curr: any) => {
                            if (!acc[curr.ShopName]) {
                                acc[curr.ShopName] = [];
                            }
                            acc[curr.ShopName].push(curr.CountryCode);
                            return acc;
                        }, {});
                
                        // Create content for display
                        const content = Object.keys(groupedData).join(', ');
                        
                        // Create table content for popover
                        const popoverContent = (
                            <ATable 
                                size="small"
                                pagination={false}
                                dataSource={Object.entries(groupedData).map(([shop, countries]) => ({
                                    key: shop,
                                    shop,
                                    countries: (countries as string[]).join(', ')
                                }))}
                                columns={[
                                    {
                                        title: '店铺名称',
                                        dataIndex: 'shop',
                                        key: 'shop',
                                    },
                                    {
                                        title: '国家',
                                        dataIndex: 'countries',
                                        key: 'countries',
                                    }
                                ]}
                            />
                        );
                
                        return (
                            <APopover 
                                content={popoverContent}
                                title="服务详情"
                                trigger="hover"
                            >
                                {/* 下划线 */}
                                <span className="cursor-pointer text-primary underline">{content}</span>
                            </APopover>
                        );
                    },
                },
                // {
                //     key: 'PackageMonths',
                //     dataIndex: 'PackageMonths',
                //     title: '服务周期(月)',
                //     align: 'center',
                //     width: 120,
                //     checked: true,
                //     sorter: (a: any, b: any) => parseFloat(a.PackageMonths) - parseFloat(b.PackageMonths),
                // },
                // {
                //     key: 'PackageAddDay',
                //     dataIndex: 'PackageAddDay',
                //     title: '套餐附加时长(天)',
                //     align: 'center',
                //     checked: true,
                //     width: 150,
                //     sorter: (a: any, b: any) => parseFloat(a.PackageAddDay) - parseFloat(b.PackageAddDay),
                // },
                // {
                //     key: 'FactServiceDay',
                //     dataIndex: 'FactServiceDay',
                //     title: '实际服务周期(天)',
                //     align: 'center',
                //     checked: true,
                //     width: 150,
                //     sorter: (a: any, b: any) => parseFloat(a.FactServiceDay) - parseFloat(b.FactServiceDay),
                // },
                {
                    key: 'ServicePeriod',
                    dataIndex: 'FactServiceDay',
                    title: '服务周期',
                    align: 'center',
                    width: 170,
                    checked: true,
                    sorter: (a: any, b: any) => parseFloat(a.FactServiceDay) - parseFloat(b.FactServiceDay),
                    render: (_, record: any) => {
                        const months = record.PackageMonths ? `${record.PackageMonths}个月` : '';
                        const days  = record.PackageMonths ? `${record.PackageMonths * 30}天` : '';
                        const addDays = record.PackageAddDay ? `赠送${record.PackageAddDay}天` : '';
                        // 如果months和addDays都有值，则用+号连接，否则用空字符串
                        // const total = `${record.FactServiceDay}天`;
                        
                        return (
                            <Tooltip title={`${months}（${days}${addDays}）`}>
                                {months}（{days}{addDays}）
                            </Tooltip>
                        );
                    }
                },
                {
                    key: 'Pay',
                    dataIndex: 'Pay',
                    title: '订单金额',
                    align: 'center',
                    checked: true,
                    width: 100,
                    sorter: (a: any, b: any) => parseFloat(a.Pay) - parseFloat(b.Pay),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },

                // {
                //     key: 'CouponID',
                //     dataIndex: 'CouponID',
                //     title: '优惠券ID',
                //     align: 'center',
                //     checked: true,
                // },
                {
                    key: 'CouponMoney',
                    dataIndex: 'CouponMoney',
                    title: '优惠券金额',
                    align: 'center',
                    checked: true,
                    width: 110,
                    sorter: (a: any, b: any) => parseFloat(a.CouponMoney) - parseFloat(b.CouponMoney),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                // {
                //     key: 'CouponName',
                //     dataIndex: 'CouponName',
                //     title: '优惠券',
                //     align: 'center',
                //     checked: true,
                //     width: 100,
                //     ellipsis: {
                //         showTitle: false,
                //     },
                //     render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                // },
                {
                    key: 'BalancePay',
                    dataIndex: 'BalancePay',
                    title: '应付金额',
                    align: 'center',
                    checked: true,
                    width: 100,
                    sorter: (a: any, b: any) => parseFloat(a.BalancePay) - parseFloat(b.BalancePay),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                // {
                //     key: 'GiftBalancePay',
                //     dataIndex: 'GiftBalancePay',
                //     title: '赠送支付',
                //     align: 'center',
                //     width: 100,
                //     checked: true,
                //     sorter: (a: any, b: any) => parseFloat(a.GiftBalancePay) - parseFloat(b.GiftBalancePay),
                //     render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                // },
                // {
                //     key: 'OlinePay',
                //     dataIndex: 'OlinePay',
                //     title: '在线支付',
                //     align: 'center',
                //     checked: true,
                //     width: 100,
                //     sorter: (a: any, b: any) => parseFloat(a.OlinePay) - parseFloat(b.OlinePay),
                //     render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                // },
                // {
                //     key: 'UpdateShopStatus',
                //     dataIndex: 'UpdateShopStatus',
                //     title: '店铺更新状态',
                //     align: 'center',
                //     checked: true,
                //     render: (status: number) => (
                //         <ATag color={status === 1 ? 'green' : status === 0 ? 'blue' : 'red'}>
                //             {status === 1 ? '更新完毕' : status === 0 ? '没有更新' : '更新失败'}
                //         </ATag>
                //     ),
                // },
                {
                    key: 'AddDatetime',
                    dataIndex: 'AddDatetime',
                    title: '创建时间',
                    align: 'center',
                    checked: true,
                    width: 150,
                    ellipsis: {
                        showTitle: false,
                    },
                    sorter: (a: any, b: any) => new Date(a.AddDatetime).getTime() - new Date(b.AddDatetime).getTime(),
                    render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                },
                {
                    key: 'UOOther',
                    dataIndex: 'UOOther',
                    title: "操作账户",
                    align: 'center',
                    width: 100,
                    checked: true,
                },
                {
                    key: 'PayState',
                    dataIndex: 'PayState',
                    title: '支付状态',
                    align: 'center',
                    width: 100,
                    fixed: 'right',
                    checked: true,
                    sorter: (a: any, b: any) => a.PayState - b.PayState,
                    render: (state: number,record:any) => (
                        <ATag className='cursor-pointer' onClick={()=>handlePay(state,record)} color={state === -1 ? 'red' : state === 2 ? 'green' : 'blue'}>
                            {state === -1 ? '支付失败' : state === 2 ? '支付成功' : '待支付'}
                        </ATag>
                    ),
                },
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);


    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async () => {
        console.log(tableProps, "tableProps====")
        // console.log(params, "params====")
        // return
        // run(params);
        const formValues = form.getFieldsValue();
        console.log(formValues, "formValues====")
        const searchParams = {
            where: {
                ...(formValues?.PayState ? { PayState: `${formValues.PayState}` } : {})
            },
        };
        handleRun({
            page: 1,
            pagesize: 20,
            ...searchParams,
            CountryCode: "CN"
        });
    }

    // const filteredData = () => {
    //     return tableProps.dataSource?.map((item: any, index: number) => ({
    //         ...item,
    //         ID: index
    //     }));
    // }


    // 二次支付
    const handlePay = async (state: number, record: any) => {
        console.log(state, record, "state,record====")
        if (state === 0 || state === 1) {
            console.log(state, record, "state,record====")
            const res = await continuePay({
                OrderID: record.ID
            })
            if (res && res.data) {

                console.log(res.data, "res.data====")
                if (res.data.JumpUrl) {
                    setJumpUrl(res.data.JumpUrl);
                    setModalVisible(true);
                    window.open(res.data.JumpUrl, '_blank');
                }
            }
            // console.log(res, "res====")
        }
    }

    // useEffect(() => {
    //     setLoading(true);
    //     if (tableProps.dataSource.length >= 0) {
    //     const fetchData = async () => {
    //         const newData = await filteredData();
    //         // console.log(newData, "newData====");
    //         setTableData(newData);
    //         // setListingHistoryLoading(false);
    //     };
    //     fetchData();
    //     }

    // }, [tableProps.dataSource]);
    useEffect(() => {
        getTableData();
    }, []);
    return (
        <div className="h-full min-h-500px  flex-col-stretch gap-16px overflow-auto">

            <PaymentModal
                visible={modalVisible}
                onClose={() => {
                    setModalVisible(false)
                    reset()
                }}
                jumpUrl={jumpUrl}
                redirectToUrl={() => {
                    setModalVisible(false);
                    reset()
                }}
            />
            {/* <ACard> */}
{/* 
                <UserSearch
                    search={getTableData}
                    reset={reset}
                    form={form}
                    loading={tableProps.loading}
                /> */}
            {/* </ACard> */}

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={reset}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <div>
                        订单记录
                    </div>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={scrollConfig}
                    // rowSelection={rowSelection}
                    size="small"
                    {...tableProps}
                    // dataSource={filteredData()}
                // loading={loading}
                // tableLayout="auto"
                //   dataSource={formattedData}
                />
            </ACard>
        </div>
    );
}
