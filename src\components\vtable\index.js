
import wxkf from "@/assets/svg-icon/activity.svg";
// import {VTable} from "@visactor/react-vtable";
const table_config = {
    "columns": [
        {
            "field": "国家",
            "title": "国家",
        
        },
        {
            "field": "总销售日期",
            "title": "日期",
            "sort": true
        },
        {
            "field": "广告总销售额",
            "title": "广告总销售额",
            "description": "托管listing所有广告销售额",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "广告总花费",
            "title": "广告总花费",
            // "headerIcon": [
            //     {
            //       name: 'question',
            //       type: 'svg',
            //       marginLeft: 10,
            //       positionType:"inlineEnd",
            //       width: 20,
            //       height: 20,
            //       svg: wxkf,
            //       tooltip: {
            //         // style: { arrowMark: true },
            //         // 气泡框，按钮的的解释信息
            //         // title: 'this is product name',
            //         // placement: VTable.TYPES.Placement.right
            //       }
            //     }
            // ],
            "description": "托管listing所有广告花费",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "广告总ACOS",
            "title": "广告总ACOS",
            "description": " 广告总花费（托管listing） /   广告总销售额（托管listing）",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "DeepBI广告销售额",
            "title": "AI广告销售额",
            "description": "AI所有SP广告销售额",
            headerStyle: {
                color: '#9400D3',
            },
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "DeepBI广告花费",
            "title": "AI广告花费",
            "description": "AI所有SP广告花费",
            headerStyle: {
                color: '#9400D3',
            },
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        
        {
            "field": "DeepBI广告总ACOS",
            "title": "AI广告总ACOS",
            "description": "AI广告花费 / AI广告销售额",
            headerStyle: {
                color: '#9400D3',
            },
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "原计划广告销售额",
            "title": "原计划广告销售额",
            "description": "广告总销售额（托管listing） - AI广告销售额",
            headerStyle: {
                color: 'red',
            },
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "原计划广告花费",
            "title": "原计划广告花费",
            "description": "广告总花费（托管listing） -  AI广告花费",
            headerStyle: {
                color: 'red',
            },
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "原计划广告总ACOS",
            "title": "原计划广告总ACOS",
            "description": "原计划广告花费  /   原计划广告销售额",
            headerStyle: {
                color: 'red',
            },
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "总销售额",
            "title": "总销售额",
            "description": "托管listing订单销售额 （广告和非广告部分）",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        // {
        //     "field": "14",
        //     "title": "广告销售额",
        //     
        //     style: {
        //         color(args) {
        //             // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
        //             if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
        //                 return '#FF7F00';
        //             }
        //             if (parseFloat(args.dataValue) > 0) {
        //                 return 'black';
        //             } else if (parseFloat(args.dataValue) < 0) {
        //                 return 'red';
        //             } else {
        //                 return 'black';
        //             }
        //         }
        //     }
        // },
        {
            "field": "自然销售额",
            "title": "自然销售额",
            "description": "总销售额（托管listing） - 广告总销售额（托管listing）",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "自然比例",
            "title": "自然比例",
            "description": "自然销售额（托管listing） / 总销售额（托管listing）",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        },
        {
            "field": "TACOS",
            "title": "TACOS",
            "description": "广告总花费（托管listing）/  总销售额（托管listing）",
            style: {
                color(args) {
                    // 判断parseFloat(args.dataValue)等于0或者0.00 或者0.00%
                    if (args.dataValue == '0.00%' || args.dataValue == '0.00' || args.dataValue == '0' || args.dataValue == '0%' || args.dataValue == '0.0%' || args.dataValue == '0.0') {
                        return '#FF7F00';
                    }
                    if (parseFloat(args.dataValue) > 0) {
                        return 'black';
                    } else if (parseFloat(args.dataValue) < 0) {
                        return 'red';
                    } else {
                        return 'black';
                    }
                }
            }
        }
    ],
    "records": [],
    "widthMode": "autoWidth",
    "autoWrapText": true,
    "limitMaxAutoWidth": 140,
    "heightMode": "autoHeight",
    keyboardOptions: {
        copySelected: true,
        selectAllOnCtrlA: true
      },
    "tooltip": {
        "isShowOverflowTextTooltip": true
    },
    "hover": {
        "highlightMode": "row"
    },
    "emptyTip": {
        "text": "No Data"
    },
    "allowFrozenColCount": 2,
    "frozenColCount": 2,
    "showFrozenIcon": true,
    "dragHeaderMode": 'column',
    // "animationAppear": {
    //   "duration": 300,
    //   "delay": 100,
    //   "type": 'one-by-one', // all
    //   "direction": 'row' // colunm
    // },
    // theme: VTable.themes.DEFAULT,
    theme: {
        underlayBackgroundColor: '#FFF',
        // selectionBgColor: '#CCE0FF',
        defaultStyle: {
            borderColor: '#E1E4E8',
            color: '#000',
            bgColor: '#ECF1F5'
        },
        headerStyle: {
            fontSize: 14,
            fontWeight: 'bold',
            bgColor: '#ECF1F5',
            hover: {
                //   cellBorderColor: "#003fff",
                cellBgColor: '#CCE0FF',
                inlineRowBgColor: '#F3F8FF',
                inlineColumnBgColor: '#F3F8FF'
            }
            // click: {
            //   cellBgColor: '#82b2f5',
            //   // inlineColumnBgColor: "#82b2f5",
            //   cellBorderColor: '#0000ff',
            //   cellBorderLineWidth: 2, // [0, 1, 3, 1],
            // },
        },
        rowHeaderStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            bgColor: '#ECF1F5',
            // click: {
            //   cellBgColor: '#82b2f5',
            //   // inlineColumnBgColor: "#82b2f5",
            //   cellBorderColor: '#0000ff',
            //   cellBorderLineWidth: 2, // [0, 1, 3, 1],
            // },
            hover: {
                //   cellBorderColor: "#003fff",
                cellBgColor: '#CCE0FF',
                inlineRowBgColor: '#F3F8FF',
                inlineColumnBgColor: '#F3F8FF'
            }
        },
        cornerHeaderStyle: {
            fontSize: 16,
            fontWeight: 'bold'
        },
        bodyStyle: {
            fontSize: 14,
            bgColor: getBackgroundColor,
            hover: {
                // cellBorderColor: "#003fff",
                cellBgColor: '#CCE0FF',
                inlineRowBgColor: '#F3F8FF',
                inlineColumnBgColor: '#F3F8FF'
                // cellBorderLineWidth:2
            }
            // click: {
            //   cellBgColor: 'rgba(0, 0, 255,0.1)',
            //   cellBorderLineWidth: 2,
            //   inlineColumnBgColor: '#CCE0FF',
            //   inlineRowBgColor: '#CCE0FF',
            //   cellBorderColor: '#0000ff',
            // },
        },
        frameStyle: {
            borderColor: '#E1E4E8',
            borderLineWidth: 1,
            borderLineDash: [],
            cornerRadius: 0,
            shadowBlur: 0,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowColor: 'black'
        },
        columnResize: {
            lineWidth: 1,
            lineColor: '#416EFF',
            bgColor: '#D9E2FF',
            width: 3
        },
        frozenColumnLine: {
            shadow: {
                width: 3,
                startColor: 'rgba(225, 228, 232, 0.6)',
                endColor: 'rgba(225, 228, 232, 0.6)'
            }
        },
        // menuStyle: {
        //   color: '#000',
        //   highlightColor: '#2E68CF',
        //   fontSize: 12,
        //   fontFamily: 'Arial,sans-serif',
        //   highlightFontSize: 12,
        //   highlightFontFamily: 'Arial,sans-serif',
        //   hoverBgColor: '#EEE'
        // },
        selectionStyle: {
            cellBgColor: 'rgba(0, 0, 255,0.1)',
            cellBorderLineWidth: 2,
            cellBorderColor: '#0000ff'
        },
        tooltipStyle: {
            bgColor: '#FFF',
            color: '#000',
            fontSize: 12,
            fontFamily: 'Arial,sans-serif'
        },
        scrollStyle: {
            visible: 'always',
            scrollSliderColor: '#ccc',
            scrollRailColor: '#fff',
            //   hoverOn: false,
            //   barToSide: true
        }
    }

}

function getBackgroundColor(args) {
    const { row, table } = args;
    // if (row < table.frozenRowCount) {
    //   return "#FFF";
    // }
    const index = row - table.frozenRowCount;
    if (!(index & 1)) {
        return '#FAF9FB';
    }
    return '#FDFDFD';
}

export { table_config };

