import React, { useState, useEffect, useCallback, useImperativeHandle, forwardRef } from 'react';
import { Form, Row, Col, Input } from 'antd';
import { getCaptcha } from '@/service/api';

interface CaptchaInputWithQRCodeProps {
  onCaptchaChange: (captcha: string, captchaKey: string) => void;
  label?: boolean;
}
const CaptchaQRCode = forwardRef(({ onCaptchaChange, label }: CaptchaInputWithQRCodeProps, ref) => {
  const { t } = useTranslation();
  const [captcha, setCaptcha] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [captchaKey, setCaptchaKey] = useState('');
  const {
    formRules: { code }
  } = useFormRules();
  const fetchCaptcha = useCallback(async () => {
    try {
      const res = await getCaptcha();
      console.log(res, 'res');
      if (res.data) {
        setQrCodeUrl('data:image/jpeg;base64,' + res.data.img_code);
        setCaptchaKey(res.data.key);
      }

    } catch (error) {
      console.error('Error fetching captcha:', error);
    }
  }, []);

  useEffect(() => {
    fetchCaptcha();
  }, []);

  useImperativeHandle(ref, () => ({
    refreshCaptcha: fetchCaptcha,
  }));

  const handleChange = (value: any) => {
    setCaptcha(value);
    if (onCaptchaChange) {
      onCaptchaChange(value, captchaKey);
    }
  };

  return (
    <div className={`flex  justify-between gap-4px ${label ? 'items-center' : ''}`}>
      <Form.Item name="captchaValue" className='flex-1 mb-4'
        label={label ? t('page.login.common.captcha') : ''}
        rules={code}>
        <Input
          className="form-input-custom"
          value={captcha}
          size="large"
          onChange={handleChange}
          placeholder={t('page.login.common.captchaPlaceholder')}
         // Adjust marginRight as needed

        />
      </Form.Item>
      <img
        src={qrCodeUrl}
        alt="QR Code"
        className={`h-full ${label ? 'mb-[-15px]' : ''}`}
        style={{ maxHeight: '36px', minWidth: 116, maxWidth: '140px', cursor: 'pointer' }} // Adjust maxWidth as needed
        onClick={fetchCaptcha}
      />
    </div>
  );
});

export default CaptchaQRCode;