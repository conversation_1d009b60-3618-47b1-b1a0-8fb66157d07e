import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { UserAddOutlined, UserDeleteOutlined, EditOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { sonIndex, sonAdd, sonChange, sonUpdate, sonDelete } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
import AddSubAccounts from './modules/AddSubAccounts';
import EditSubAccounts from './modules/EditSubAccounts';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {
    const { t } = useTranslation();

    const userInfo = useAppSelector(selectUserInfo);

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    // 批量操作弹窗
    const [open, setOpen] = useState(false);


    // 添加子账号弹窗
    const AddSubaccountsRef = useRef<{ showModal: () => void, hideModal: () => void }>(null);
    // 修改子账号弹窗
    const EditSubaccountsRef = useRef<{ showModal: () => void, hideModal: () => void }>(null);
    // 
    const [subAccountData, setSubAccountData] = useState<any>(null);
    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: sonIndex,
            apiParams: {
                page: 1,
                pagesize: 20,
                where: {
                    // "BalanceType": "INCO",
                }
            },
            immediate: false,
            columns: () => [
                {
                    key: 'ID',
                    dataIndex: 'ID',
                    title: '子账号ID',
                    align: 'center',
                    checked: true,
                    // hidden: true,
                },
                {
                    key: 'NickName',
                    dataIndex: 'NickName',
                    title: '子账号名称',
                    align: 'center',
                    // width: 150,
                    checked: true,
                    render: (type: string) => (
                        <ATag color="blue">{type}</ATag>
                    ),
                },

                // {
                //     key: 'CompanyName',
                //     dataIndex: 'CompanyName',
                //     title: '公司名称',
                //     align: 'center',
                //     //   width: 120,
                //     checked: true,
                //     // render: (text: string) => {
                //     //     const InvoiceInfo = JSON.parse(text);
                //     //     return InvoiceInfo?.CompanyName || '-';
                //     // }
                // },
                {
                    key: 'Email',
                    dataIndex: 'Email',
                    title: '邮箱',
                    align: 'center',
                    //   width: 120,
                    checked: true,
                },
                {
                    key: 'Phone',
                    dataIndex: 'Phone',
                    title: '手机号',
                    align: 'center',
                    //   width: 120,
                    checked: true,
                },
//                 {
//                     key: 'SonRight',
//                     dataIndex: 'SonRight',
//                     title: '管理店铺',
//                     align: 'center',
//                     width: 180,
//                     checked: true,
//                     render: (sonRight: any[]) => {
//                         if (!sonRight?.length) return '-';

//                         const validShops = sonRight.filter(item => item && item.ShopName);
//                         if (!validShops.length) return '-';
//                         const firstShop = validShops[0];
//                         const remainingCount = validShops.length - 1;

//                         return (
//                             <div className="flex items-center justify-center gap-1">
//                                 <ATag color="blue" className="max-w-[110px]">
//                                     <Tooltip title={firstShop?.ShopName}>
//                                         <span className="block truncate">
//                                             {firstShop?.ShopName || '-'}
//                                         </span>
//                                     </Tooltip>
//                                 </ATag>
//                                 {remainingCount > 0 && (
//     <APopover
//         placement="right"
//         content={
//             <div className="w-[300px] max-h-[400px] overflow-y-auto">
//                 <div className="py-2 px-3 text-sm text-gray-600 bg-gray-100 border-b">
//                     其他授权店铺 ({remainingCount}):
//                 </div>
//                 {validShops.slice(1).map((shop: any) => (
//                     <div 
//                         key={shop.ID} 
//                         className="py-2 px-3 hover:bg-gray-50 border-b last:border-b-0 transition duration-200"
//                     >
//                         <div className="text-gray-800 font-medium">
//                             {shop.ShopName}
//                         </div>
//                         <div className="text-xs text-gray-500 mt-1">
//                             ID: {shop.ID}
//                         </div>
//                     </div>
//                 ))}
//             </div>
//         }
//     >
//         <ATag className="cursor-pointer">
//             +{remainingCount}家店铺
//         </ATag>
//     </APopover>
// )}
//                             </div>
//                         );
//                     }
//                 },
                {
                    key: 'Oother',
                    dataIndex: 'Oother',
                    title: '备注',
                    align: 'center',
                    width: 150,
                    ellipsis: true,
                    checked: true,
                },
                {
                    key: 'operate',
                    dataIndex: 'operate',
                    title: '操作',
                    align: 'center',
                    // width: 150,
                    checked: true,
                    render: (text: string, record: any) => {
                        return (
                            <div className='flex items-center justify-center gap-2px'>
                                <AButton type='link' icon={<EditOutlined />} onClick={() => ChangeInfo(record)}>编辑</AButton>
                                |
                                <AButton type='link' icon={<UserDeleteOutlined />} danger onClick={() => DeleteInfo(record)}>删除</AButton>
                            </div>
                        )
                    }
                }
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);


    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }
    const getTableData = async () => {
        console.log(tableProps, "tableProps====")
        // console.log(params, "params====")
        // return
        // run(params);
        const formValues = form.getFieldsValue();
        console.log(formValues, "formValues====")
        const searchParams = {
            where: {
                ...(formValues.NickName ? { NickName: formValues.NickName } : {}),
                ...(formValues.Email ? { Email: formValues.Email } : {}),
                ...(formValues.Phone ? { Phone: formValues.Phone } : {}),
            },
        };
        handleRun({
            page: 1,
            pagesize: 20,
            ...searchParams,
            CountryCode: "CN"
        });
    }

    // 修改用户信息
    const ChangeInfo = (record: any) => {
        console.log(record, "record====")
        setSubAccountData({
            ...record,
            SonRight: record?.SonRight?.filter(Boolean)?.map((item: any) => String(item.ID)) || []
        })
        EditSubaccountsRef.current?.showModal()
    }

    // 删除子账号
    const DeleteInfo = (record: any) => {
        console.log(record, "record====")
        // onDeleted();
        AModal.confirm({
            title: <div >确定要删除<span className='text-red-500'>{record.NickName}</span>吗？</div>,
            content: "删除后将无法恢复",
            onOk: async () => {
                const res = await sonDelete({ ID: record.ID })
                if (res && res.data) {
                    window?.$message?.success('删除成功')
                    reset()
                }
            }
        })
    }




    // 头部操作插槽
    const prefix = () => {
        return (
            <div className='flex items-center'>

                {/* 添加子账号 */}
                <AButton
                    // icon={}
                    size="small"
                    ghost
                    type="primary"
                    className=''
                    // disabled={checkedRowKeys.length === 0}
                    onClick={() => AddSubaccountsRef.current?.showModal()}
                >
                    <div className='flex items-center'>
                        <UserAddOutlined className="text-icon mr-1" /> 添加子账号
                    </div>
                </AButton>



            </div>
        )
    }
    const SubAccountDescription = () => {
        const content = (
            <div className="p-2">
                {/* 标题 */}
                <div className="text-lg font-bold text-blue-600 mb-4 border-b pb-2">
                    子账号管理说明
                </div>

                {/* 授权店铺管理部分 */}
                <div className="mb-4 bg-gray-50 p-3 rounded-lg">
                    <div className="text-base font-semibold text-gray-800 mb-2 flex items-center">
                        <span className="w-1 h-4 bg-#154EC1 mr-2 rounded-full"></span>
                        授权店铺管理
                    </div>
                    <div className="text-gray-700 mb-2 pl-3">
                        子账号可以管理并操作其被授权的店铺内的所有活动。
                    </div>
                    <div className="bg-white p-2 rounded-md">
                        <div className="text-gray-700 font-medium mb-1">包括：</div>
                        <ul className="list-disc pl-6 text-gray-600 space-y-1">
                            <li>店铺站点激活与延长</li>
                            <li>店铺内Listing的托管与取消</li>
                        </ul>
                    </div>
                </div>

                {/* 权限限制部分 */}
                <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="text-base font-semibold text-gray-800 mb-2 flex items-center">
                        <span className="w-1 h-4 bg-#154EC1 mr-2 rounded-full"></span>
                        权限限制
                    </div>
                    <ul className="list-disc pl-6 text-gray-600 space-y-1">
                        <li>子账号只能查看和操作自己有权限授权的店铺</li>
                        <li>对于没有授权的店铺，子账号无法查看或进行任何操作</li>
                    </ul>
                </div>
            </div>
        );
        return content;
    }
    return (
        <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
            <AddSubAccounts ref={AddSubaccountsRef} onInvoiceApplied={() => {
                reset()
            }} />

            <EditSubAccounts ref={EditSubaccountsRef} subAccountData={subAccountData} onUpdate={() => {
                reset()
            }} />

            <ACard>

                <UserSearch
                    search={getTableData}
                    reset={reset}
                    form={form}
                    loading={tableProps.loading}
                />
            </ACard>

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={reset}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                        prefix={prefix()}
                    />
                }
                title={
                    <div>
                        子账号管理
                        <APopover
                            content={SubAccountDescription}
                            title={null}
                            placement="bottom"
                            overlayClassName="min-w-sm"
                        >
                            <QuestionCircleOutlined className="text-sm ml-1 cursor-pointer text-gray-400 hover:text-gray-600" />
                        </APopover>
                    </div>
                }
                className=" flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    rowSelection={rowSelection}
                    scroll={scrollConfig}
                    size="small"
                    {...tableProps}
                // loading={loading}
                // tableLayout="auto"
                //   dataSource={formattedData}
                />
            </ACard>
        </div>
    );
}
