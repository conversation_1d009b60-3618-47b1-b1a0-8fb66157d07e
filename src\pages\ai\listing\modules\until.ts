// 目标ACOS选项
export const asinOptions = [
  { value: 16, label: 'ACOS ≤ 16% | 极度保守' },
  { value: 20, label: 'ACOS ≤ 20% | 保守策略' },
  { value: 24, label: 'ACOS ≤ 24% | 适度保守' },
  { value: 28, label: 'ACOS ≤ 28% | 平衡策略' },
  { value: 32, label: 'ACOS ≤ 32% | 激进策略' },
  { value: 50, label: 'ACOS ≤ 50% | 极度激进' }
];

// 限制整数
export const limitDecimalsP = (value: any) => {
  const reg = /^(\d+).*$/;
  if (reg.test(value)) {
    return String(value).replace(reg, '$1');
  }
  return '';
};

// 获取ACOS策略标签
export const getStrategyLabel = (acosValue: number, asinOptions: any, selectedAcos: any, showacos: boolean = true) => {
  // console.log(acosValue,asinOptions,selectedAcos)
  // 首先判断是否为自定义ACOS  selectedAcos是否在asinOptions中
  const NotCustom = asinOptions.find((option: any) => option.value === selectedAcos);
  if (NotCustom || !acosValue) {
    return '';
  }
  const label = '';
  // if (acosValue <= 16) {
  //     label = `| 极度保守`;
  // } else if (acosValue <= 20) {
  //     label = `| 保守策略`;
  // } else if (acosValue <= 24) {
  //     label = `| 适度保守`;
  // } else if (acosValue <= 28) {
  //     label = `| 平衡策略`;
  // } else if (acosValue <= 32) {
  //     label = `| 激进策略`;
  // } else if (acosValue <= 50) {
  //     label = `| 极度激进`;
  // }
  if (showacos) {
    return `${acosValue}%${label}`;
  }
  return label;
};
