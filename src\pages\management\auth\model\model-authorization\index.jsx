import React, { useEffect, useState } from "react";
import { Form, Modal, Button, Image, Select, Checkbox } from 'antd';
import auth from '@/assets/imgs/auth.jpg';

export default function ModelAuthorization({ visible, onCancel, onOk,record, defaultRegion = 'NA' }) {
    const { t } = useTranslation();
    const [region, setRegion] = useState(defaultRegion);
    const [CheckInitValue, setCheckInitValue] = useState([]);
    const regionData = [
        {
            label: t('page.setting.country.NA'),
            value: 'NA',
            children: [
                { value: 'US', label: t('page.setting.country.US') },
                { value: 'CA', label: t('page.setting.country.CA') },
                { value: 'MX', label: t('page.setting.country.MX') },
                { value: 'BR', label: t('page.setting.country.BR') }
            ],
        },
        {
            label: t('page.setting.country.EU'),
            value: 'EU',
            children: [
                { value: 'UK', label: t('page.setting.country.UK') },
                { value: 'FR', label: t('page.setting.country.FR') },
                { value: 'DE', label: t('page.setting.country.DE') },
                { value: 'IT', label: t('page.setting.country.IT') },
                { value: 'ES', label: t('page.setting.country.ES') },
                { value: 'NL', label: t('page.setting.country.NL') },
                { value: 'SE', label: t('page.setting.country.SE') },
                { value: 'PL', label: t('page.setting.country.PL') },
                { value: 'BE', label: t('page.setting.country.BE') },
                { value: 'IN', label: t('page.setting.country.IN') },
                { value: 'TR', label: t('page.setting.country.TR') },
            ],
        },
        {
            label: t('page.setting.country.JP'),
            value: 'JP',
            children: [
                { value: 'JP', label: t('page.setting.country.JP') },
            ],
        },
        {
            label: t('page.setting.country.SG'),
            value: 'SG',
            children: [
                { value: 'SG', label: t('page.setting.country.SG') }
            ],
        },
        {
            label: t('page.setting.country.AU'),
            value: 'AU',
            children: [
                { value: 'AU', label: t('page.setting.country.AU') }
            ],
        },
        {
            label: t('page.setting.country.SA'),
            value: 'SA',
            children: [
                { value: 'SA', label: t('page.setting.country.SA') }
            ],
        },
        {
            label: t('page.setting.country.AE'),
            value: 'AE',
            children: [
                { value: 'AE', label: t('page.setting.country.AE') }
            ],
        },
    ];
    const [options, setOptions] = useState([]);

    const handleCancel = () => {
        setOptions([]);
        onCancel();
    };

    const handleSubmit = (values) => {
        const regionName = regionData.find(item => item.value === values.region)?.label;
        // 加入一个二次确认
        Modal.confirm({
            title: t('page.setting.authmodel.spauthconfirm'),
            okText: t('page.login.common.confirm'),
            cancelText: t('page.login.common.back'),
            content: (
                <div className="py-4">
                    <p className="flex items-center mb-3">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">{t('page.setting.auth.shop')}:</span>
                        <span className="text-primary font-medium text-lg">{record.parent_shop || record.shop}</span>
                    </p>
                    <p className="flex items-center">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">{t('page.setting.authmodel.region')}:</span>
                        <span className="text-primary font-medium text-lg">{regionName}</span>
                    </p>
                </div>
            ),
            onOk: () => {
                onOk(values.region, CheckInitValue,"SP",record);
            }
        });
    };

    const handleSelectChange = (value) => {
        console.log(value, "value===");
        setRegion(value);
        const children = regionData.find(item => item.value === value)?.children;
        console.log(children, "children===");
        setOptions(children);
        
        setCheckInitValue(children.map(item => item.value));
    };

    useEffect(() => {
        // console.log(defaultRegion, "defaultRegion===");
        handleSelectChange(defaultRegion);
    }, [defaultRegion, visible]);
    // useEffect(() => {
    //     console.log(record, "record===");
    // }, [record]);
    return (
        <Modal
            title={<div className="flex items-center pb-2">
                <span className="font-500 text-primary mr-1">{t('page.setting.auth.shop')}:{record.parent_shop || record.shop} </span>
                {t('page.setting.authmodel.title')}
                </div>}
            open={visible}
            onCancel={handleCancel}
            footer={null}
            width={1200}
            centered
        >
            <div className="flex flex-row">
                <div className="model-left pr-10">
                    <Form
                        layout="vertical"
                        onFinish={handleSubmit}
                        key={defaultRegion}
                        initialValues={{ region: defaultRegion }}
                    >
                        <Form.Item
                            label={t('page.setting.authmodel.regiontitle')}
                            name="region"
                            rules={[{ required: true, message: t('page.setting.authmodel.formrule') }]}
                        >
                            <Select
                                style={{ width: 280 }}
                                onChange={handleSelectChange}
                                placeholder={t('common.chooseText')}
                            >
                                {regionData.map((group, index) => (
                                    <Select.Option value={group.value} key={`${index}-${group.label}`}>
                                        {group.label}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>

                        {options.length > 0 && CheckInitValue.length > 0 && region && (
                            <Form.Item
                                label={t('page.setting.authmodel.notifyText')}
                                name="notify"
                            >
                               <div>
                               <p className="hidden"> {JSON.stringify(options)}</p>
                                <Checkbox.Group
                                    options={options}
                                    value={CheckInitValue}
                                    disabled
                                />
                               </div>
                            </Form.Item>
                        )}

                        <Form.Item>
                            <Button type="primary" htmlType="submit" className="mt-4 p-4 bg-#154EC1">
                                {t('page.setting.authmodel.auth')}
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
                <div className="model-right">
                    <div className="model-right-title">
                        <span>{t('page.setting.authmodel.step')}：</span>
                        <span className="text-gray-500 text-sm ml-2"></span>
                    </div>
                    <div className="model-right-content">
                        <div className="model-right-content-item flex items-center">
                            {/* 用圆圈包裹 */}
                            <span className="span flex items-center justify-center w-5 h-5 rounded-full bg-#154EC1 text-white mr-1">1</span>
                            <span className="flex-1">{t('page.setting.authmodel.step1')}</span>
                        </div>
                        <div className="model-right-content-item flex items-center">
                            {/* 用圆圈包裹 */}
                            <span className="span flex items-center justify-center w-5 h-5 rounded-full bg-#154EC1 text-white mr-1">2</span>
                            <span className="flex-1">{t('page.setting.authmodel.step2')}<a href="https://deepthought.feishu.cn/docx/TxfqdoMeLoH0s4xDoqhcybBJn1N#Etf4d7ostoUaV1xs92vcxKB5nrh" className="text-primary">{t('page.setting.authmodel.howtysetup')}</a></span>
                        </div>
                        <div className="model-right-content-item flex items-center">
                        <span className="span flex items-center justify-center w-5 h-5 rounded-full bg-#154EC1 text-white mr-1">3</span>
                            <span className="flex-1">{t('page.setting.authmodel.step2')}</span>
                        </div>
                        <Image src={auth} alt="" />
                    </div>
                </div>
            </div>
        </Modal>
    );
}