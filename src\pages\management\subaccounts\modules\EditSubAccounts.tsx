import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Modal, Select } from 'antd';
import { sonUpdate,OuthList } from '@/service/api'; // Assume this is your API function

interface EditSubAccountsProps {
    onUpdate: () => void;
    subAccountData: {
        ID: string;
        Email: string;
        Phone: string;
        NickName: string;
        SonRight: string[];
        Oother: string;
    };
}

const EditSubAccounts = forwardRef((props: EditSubAccountsProps, ref) => {
    const { onUpdate, subAccountData } = props;
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const { formRules, createConfirmPwdRule } = useFormRules();
    const [outhList, setOuthList] = useState([]);

    useImperativeHandle(ref, () => ({
        showModal: () => setVisible(true),
        hideModal: () => setVisible(false),
    }));

    useEffect(() => {
        const getPermissions = async () => {
            try {
                const res = await OuthList();
                if (res && res.data) {
                    // 便利res.data 
                    const options = Object.keys(res.data).map(key => ({
                        value: key,
                        label: res.data[key].ShopName
                    }));
                    console.log(options, 'options')
                    setOuthList(options);
                }
            } catch (error) {
                console.error('Failed to fetch permissions:', error);
            }
        };
        if (visible) {
            form.setFieldsValue(subAccountData);
            getPermissions()
        }else{
            form.resetFields()
        }
    }, [visible, form, subAccountData]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            const { Password, RePassword, ...otherValues } = values;
            // console.log(otherValues, "otherValues====")
            // return
            // Only include password fields if they are filled
            const updateData = {
                ...otherValues,
                SonRight: otherValues.SonRight.join(','),
                ...(Password ? { Password } : {}),
                ...(RePassword ? { RePassword } : {}),
                ID: subAccountData.ID

            };

            setLoading(true);
            const res = await sonUpdate(updateData);
            if (res && res.data) {
                window?.$message?.success('子账号更新成功');
                onUpdate();
                setVisible(false);
            }
        } catch (error) {
            console.error('Failed to update subaccount:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
          <style>
        {`
          @media (max-width: 1440px) {
            .responsive-form {
              max-height: 520px;
            }
          }
          @media (min-width: 1441px) and (max-width: 1800px) {
            .responsive-form {
              max-height: 800px;
            }
          }
        `}
      </style>
       
        <Modal
            title="编辑子账号"
            open={visible}
            onOk={handleOk}
            onCancel={() => setVisible(false)}
            confirmLoading={loading}
            centered
            maskClosable={false}
        >
            <Form form={form} layout="vertical" initialValues={subAccountData}
                className="responsive-form overflow-auto"
                style={{ overflowY: 'scroll' }}
            >
                <Form.Item
                    name="Email"
                    label="邮箱"
                    rules={formRules.email}
                >
                    <Input placeholder="请输入邮箱" />
                </Form.Item>
                <Form.Item
                    name="Phone"
                    label="手机号"
                    rules={[formRules.phone[1]]} // 只使用正则验证规则，不使用必填规则
                >
                    <Input placeholder={t('page.login.common.phonePlaceholder')}></Input>
                </Form.Item>
                <Form.Item
                    name="NickName"
                    label="用户名称"
                    rules={[{ required: true, message: '请输入用户名称' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="Password"
                    label="密码（可选）"
                    rules={[formRules.pwd[1]]}
                >
                    <Input.Password
                        autoComplete="Password"
                        placeholder={t('请输入最短8位，包含大小写字母、数字的密码')}
                    ></Input.Password>
                </Form.Item>
                <Form.Item
                    name="RePassword"
                    label="重复密码（可选）"
                    dependencies={['Password']}
                    rules={[
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('Password') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error('两次输入的密码不一致'));
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="请再次输入新密码" />
                </Form.Item>
                <Form.Item
                    name="SonRight"
                    label="权限列表"
                    rules={[{ required: true, message: '请选择权限列表' }]}
                >
                    <Select key={subAccountData?.ID} mode="multiple" placeholder="请选择权限" options={outhList} />
                </Form.Item>
                <Form.Item
                    name="Oother"
                    label="备注"
                >
                    <Input.TextArea rows={4} />
                </Form.Item>
            </Form>
        </Modal>
        </>
    );
});

export default EditSubAccounts;