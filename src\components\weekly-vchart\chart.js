import { Icon } from '@iconify/react';
// import { useLanguage } from "..../../store/settingsStore";
// const language = useLanguage();
function getCountryName(countryCode) {
  const countryNames = {
    CA: '加拿大',
    US: '美国',
    MX: '墨西哥',
    BR: '巴西',
    ES: '西班牙',
    UK: '英国',
    GB: '英国',
    FR: '法国',
    BE: '比利时',
    NL: '荷兰',
    DE: '德国',
    IT: '意大利',
    SE: '瑞典',
    IE: '爱尔兰',
    PL: '波兰',
    EG: '埃及',
    TR: '土耳其',
    SA: '沙特阿拉伯',
    AE: '阿联酋',
    IN: '印度',
    SG: '新加坡',
    AU: '澳大利亚',
    JP: '日本'
  };

  return countryNames[countryCode] || '';
}

function getContinentsName(countryCode) {
  // 'NA': 'emojione:globe-showing-americas',
  // 'EU': 'emojione:globe-showing-europe-africa',
  // 'JP': 'emojione:globe-showing-asia-australia',
  // 'SG': 'emojione:globe-showing-asia-australia',
  // 'AU': 'emojione:globe-showing-asia-australia',
  // 'SA': 'emojione:globe-showing-europe-africa',
  // 'AE': 'emojione:globe-showing-europe-africa',
  const shopNameMap = {
    NA: {
      name: '北美',
      icon: 'emojione:globe-showing-americas'
    },
    EU: {
      name: '欧洲',
      icon: 'emojione:globe-showing-europe-africa'
    },
    JP: {
      name: '日本',
      icon: 'emojione:globe-showing-asia-australia'
    },
    SG: {
      name: '新加坡',
      icon: 'emojione:globe-showing-asia-australia'
    },
    AU: {
      name: '澳大利亚',
      icon: 'emojione:globe-showing-asia-australia'
    },
    SA: {
      name: '沙特阿拉伯',
      icon: 'emojione:globe-showing-europe-africa'
    },
    AE: {
      name: '阿联酋',
      icon: 'emojione:globe-showing-europe-africa'
    }
  };
  return shopNameMap[countryCode] || countryCode;
}

function getCurrency(countryCode) {
  const Currencies = {
    SE: 'SEK',
    PL: 'PLN',
    TR: 'TRY',
    EG: 'EGP',
    AE: 'AED',
    SA: 'SAR',
    GB: 'GBP',
    UK: 'GBP',
    US: 'USD',
    MX: 'MXN',
    BR: 'BRL',
    CA: 'CAD',
    AU: 'AUD',
    IN: 'INR',
    JP: 'JPY',
    SG: 'SGD'
  };

  // Set multiple countries to the same currency
  const euroCountries = ['EU', 'ES', 'DE', 'FR', 'IT', 'NL', 'BE', 'IE'];
  euroCountries.forEach(code => {
    Currencies[code] = 'EUR';
  });
  return Currencies[countryCode] || '';
}

function getCurrencySymbol(countryCode) {
  const CurrencySymbols = {
    EUR: '€',
    SEK: 'kr',
    PLN: 'zł',
    TRY: '₺',
    EGP: '£',
    AED: 'د.إ',
    SAR: '﷼',
    GBP: '£',
    USD: '$',
    MXN: '$',
    BRL: 'R$',
    CAD: '$',
    AUD: '$',
    INR: '₹',
    JPY: '¥',
    SGD: '$'
  };
  return CurrencySymbols[countryCode] || '';
}

function getSalesChannel(countryCode) {
  const AmazonMarketplaces = {
    CA: 'Amazon.ca',
    US: 'Amazon.com',
    MX: 'Amazon.com.mx',
    BR: 'Amazon.com.br',
    ES: 'Amazon.es',
    UK: 'Amazon.co.uk',
    GB: 'Amazon.co.uk',
    FR: 'Amazon.fr',
    BE: 'Amazon.com.be',
    NL: 'Amazon.nl',
    DE: 'Amazon.de',
    IE: 'Amazon.ie',
    IT: 'Amazon.it',
    SE: 'Amazon.se',
    PL: 'Amazon.pl',
    EG: 'Amazon.eg',
    TR: 'Amazon.com.tr',
    SA: 'Amazon.sa',
    AE: 'Amazon.ae',
    IN: 'Amazon.in',
    SG: 'Amazon.sg',
    AU: 'Amazon.com.au',
    JP: 'Amazon.co.jp'
  };
  // if (countryCode == "FR" || countryCode == "DE" || countryCode == "IT" || countryCode == "ES" || countryCode == "NL" || countryCode == "PL" || countryCode == "BE" || countryCode == "SE") {
  //   return 'Amazon.de';
  // }

  return AmazonMarketplaces[countryCode] || '';
}

// FBA 库存和周转率（按产品）
function createFBAInventory(IsDistributor = false) {
  return {
    type: 'treemap',
    data: [
      {
        id: 'data',
        values: [
          {
            name: '',
            children: [
              // { name: 'B08T85NTX5', value: 2813,sales30:3216,estDays:26.24 },
            ]
          }
        ]
      }
    ],
    nonLeaf: {
      visible: true
    },
    nonLeafLabel: {
      visible: true,
      // non-leaf label will show at the top of the rect
      // 非叶子节点的标签位置显示在矩形上方
      position: 'top',
      // The padding for non-leaf node, we can use this space to display a label
      // This padding will only works when the node has enough space
      // 非叶子结点预留的空间大小，通常用于配合文字大小配置，本例中为多行文本预留 30px 高度
      padding: 30,
      style: {
        x: data => data.labelRect?.x0 + 4,
        textAlign: 'left',
        text: data => {
          console.log(data.value, 'data');
          // const advertise = data.children.reduce((acc, child) => acc + Number(child.datum[1].advertise), 0);
          // const storage = data.children.reduce((acc, child) => acc + Number.parseInt(child.datum[1].storage), 0);
          // const on_way = data.children.reduce((acc, child) => acc + Number.parseInt(child.datum[1].on_way), 0);
          // const un_sell = data.children.reduce((acc, child) => acc + Number.parseInt(child.datum[1].un_sell), 0);
          // const reserve = data.children.reduce((acc, child) => acc + Number.parseInt(child.datum[1].reserve), 0);
          let totalValue = 0;
          let totalAdvertise = 0;
          let totalReserve = 0;

          // 遍历所有子节点计算总量和可售量
          data.children.forEach(child => {
            if (child.datum && child.datum[1]) {
              totalValue += Number(child.datum[1].value || 0);
              totalAdvertise += Number(child.datum[1].advertise || 0);
              totalReserve += Number(child.datum[1].reserve || 0);
            }
          });
          if (IsDistributor) {
            return `${data.name}    总量: ${totalValue}`;
          }
          // return `${data.name}    总量: ${data.value}  可售: ${advertise}    入库: ${storage}  在途: ${on_way}    不可售: ${un_sell}  预留: ${reserve}`;
          return `${data.name}    总量: ${totalValue}  可售: ${totalAdvertise}  预留: ${totalReserve}`;
        },
        fill: '#333',
        fontSize: 12,
        lineHeight: 16
      }
    },
    tooltip: {
      mark: {
        title: {
          value: datum => {
            // console.log(datum)
            return datum?.datum?.map(data => data.name).join('/');
          }
        },
        content: [
          { key: '总量', value: datum => datum.value },
          {
            key: '可售',
            value: datum => {
              if (datum && datum.datum && datum.datum.length > 1) {
                return datum.datum[1].advertise;
              }
              return 0;
            }
          },
          // { key: "入库", value: datum => {
          //   if (datum && datum.datum && datum.datum.length > 1) {
          //     return datum.datum[1].storage
          //   }
          //   return 0
          // } },
          // { key: "在途", value: datum => {
          //   if (datum && datum.datum && datum.datum.length > 1) {
          //     return datum.datum[1].on_way
          //   }
          //   return 0
          // } },
          // { key: "不可售", value: datum => {
          //   if (datum && datum.datum && datum.datum.length > 1) {
          //     return datum.datum[1].un_sell
          //   }
          //   return 0
          // } },
          !IsDistributor && {
            key: '预留',
            value: datum => {
              if (datum && datum.datum && datum.datum.length > 1) {
                return datum.datum[1].reserve;
              }
              return 0;
            }
          },
          {
            key: '30天销量',
            value: datum => {
              if (datum && datum.datum && datum.datum.length > 1) {
                return datum.datum[1].sales30;
              }
              return 0;
            }
          },
          {
            key: '预计可售天数',
            value: datum => {
              // console.log(datum, "datum");
              if (datum && datum.datum && datum.datum.length > 1) {
                return datum.datum[1].estDays;
              }
              return 0;
            }
          }
        ],
        updateContent: (prev, data, params) => {
          // // 判断name 等于 EU NA UK 或者是JP
          if (params.datum.name.length === 2) {
            return [prev[0]];
          }
          // if (params.datum.name === 'EU' || params?.datum?.name === 'NA' || params?.datum?.name === 'UK' || params?.datum?.name === 'JP') {
          //   return [prev[0]]
          // }
          // if
          // return [prev[0]]
        }
      },
      enterable: true,

      updateElement: (el, actualTooltip, params) => {
        // console.log(el, actualTooltip, params, "params");

        // console.log(actualTooltip, "actualTooltip.activeType");
        if (actualTooltip.activeType === 'mark') {
          const { changePositionOnly, datum } = params;
          // console.log(datum)
          // console.log(el.getElementsByClassName('value-box'), "changePositionOnly, dimensionInfo");
          if (changePositionOnly) {
            return;
          }
          // 判断name
          if (datum?.datum?.length > 1) {
            el.style.width = 'auto';
            el.style.height = 'auto';
            el.style.minHeight = 'auto';
            el.getElementsByClassName('value-box')[0].style.flex = '1';
            for (const valueLabel of el.getElementsByClassName('value')) {
              valueLabel.style.maxWidth = 'none';
            }

            if (el.lastElementChild?.id === 'button-container') {
              el.lastElementChild.remove();
            }
            const div = document.createElement('div');
            div.id = 'button-container';
            div.style.margin = '10px -10px 0px';
            div.style.padding = '10px 0px 0px';
            div.style.borderTop = '1px solid #cccccc';
            div.style.textAlign = 'center';
            div.innerHTML = `<a
              href="https://www.${getSalesChannel(datum.datum[1].market)}/dp/${datum.datum[1].name}"
              style="text-decoration: none"
              target="_blank"
            >Go to <b>Amazon</b></a>`;
            el.appendChild(div);
          } else if (el.lastElementChild?.id === 'button-container') {
            el.lastElementChild.remove();
          }
        } else if (el.lastElementChild?.id === 'button-container') {
          el.lastElementChild.remove();
        }
      }
    },
    // categoryField: 'sales30',
    // color:datum=>{
    //   console.log(datum, "datum");
    //   return datum.value > 0 ? ['#000'] : ['#fff']
    // },
    color: ['#fff'],
    leaf: {
      style: {
        fill: datum => {
          // console.log(datum.datum[1].estDays, "datum");
          if (datum && datum.datum && datum.datum.length > 1 && datum.datum[1].estDays) {
            if (datum.datum[1].estDays >= 360) {
              return '#F44336';
            } else if (datum.datum[1].estDays >= 270) {
              return '#FF7043';
            } else if (datum.datum[1].estDays >= 180) {
              return '#FFA726';
            } else if (datum.datum[1].estDays >= 90) {
              return '#1664ff';
            } else if (datum.datum[1].estDays > 0) {
              return '#66BB6A';
            }
            return '#333';
          }
          return '#333';

          // else if(datum.sales30 >= 90 && datum.sales30 < 180){
          //   return '#1ac6ff'
          // }else if(datum.sales30 >= 0 && datum.sales30 < 90){
          //   return '#1664ff'
          // }
        }
      }
    },
    categoryField: 'name',
    valueField: 'value',
    // roam:true,
    label: {
      visible: true,
      style: {
        fontSize: 12,
        color: '#000'
      }
    },
    title: {
      visible: true,
      text: IsDistributor ? '实时库存' : 'FBA 库存'
    }
  };
}

// 仓库龄数据：
function createInventoryAge() {
  // if (selectedContinentCode == 'EU' && selectedCountry == 'UK') {
  //   selectedContinentCode = 'UK';
  // }
  return {
    type: 'bar',
    title: {
      visible: true,
      text: `库龄数据`
    },
    data: [
      {
        id: 'barData',
        values: [
          // {
          //   date: '202420',
          //   type: '0-90',
          //   quantity: 19104,
          // },
          // {
          //   date: '202420',
          //   type: '90-180',
          //   quantity: 20149,
          // },
          // {
          //   date: '202420',
          //   type: '180-270',
          //   quantity: 5800,
          // },
          // {
          //   date: '202420',
          //   type: '270-365',
          //   quantity: 2201,
          // },
          // {
          //   date: '202420',
          //   type: '365plus',
          //   quantity: 7212,
          // },
        ]
      }
    ],
    color: ['#66BB6A', '#1664ff', '#FFD700', '#FFA726', '#F44336'],
    xField: 'date',
    yField: 'quantity',
    seriesField: 'type',
    barWidth: 90,
    stack: true,
    legends: [
      {
        item: {
          focus: true // enable focus
        },
        visible: true,
        position: 'middle',
        orient: 'right'
      }
    ],
    label: {
      visible: false,
      style: {
        text: obj => {
          return `${obj.quantity}`;
        },
        fill: '#333',
        fontWeight: 'bold',
        fontSize: 10
      }
    },
    tooltip: {
      mark: {
        title: {
          value: datum => {
            return datum.type;
          }
        },
        content: [
          { key: 'quantity', value: datum => datum.quantity }
          // { key: "sku", value: datum => datum.sku },
        ]
      },
      dimension: {
        // content: [
        //   { key: "0-90", value: datum => datum.quantity },
        //   { key: "90-180", value: datum => datum.quantity },
        //   { key: "180-270", value: datum => datum.quantity },
        //   { key: "270-365", value: datum => datum.quantity },
        //   { key: "365plus", value: datum => datum.quantity },
        // ],
        updateContent: data => {
          const arr = data;
          const sum = data.reduce((acc, datum) => {
            return acc + Number.parseInt(datum.value);
          }, 0); // 初始化累加器为 0
          arr.push({
            key: '库存合计',
            value: sum
          });
          // console.log(arr, "arr")
          return arr;
        }
      }
    },
    axes: [
      {
        orient: 'left',
        label: {
          formatMethod: val => {
            return `${val > 1000 ? `${val / 1000}k` : val}`;
          }
        }
      }
    ],
    bar: {
      // The state style of bar
      state: {
        hover: {
          stroke: '#000',
          lineWidth: 1
        }
      }
    }
  };
}

// 各国 销售 & 广告费用占比
function createListingDaily() {
  return {
    type: 'common',
    padding: {
      left: 0
    },
    data: [],
    tooltip: {
      renderMode: 'canvas',
      confine: true
    },
    axes: [
      { orient: 'left', seriesIndex: [0] },
      {
        orient: 'right',
        seriesIndex: [1],
        grid: { visible: true },
        label: {
          formatMethod: value => {
            return `${value}%`;
          }
        }
      },
      { orient: 'bottom', label: { visible: true }, type: 'band' }
    ],

    legends: {
      visible: true,
      orient: 'bottom',
      item: {
        focus: true // enable focus
      },
      allowAllCanceled: true // allow all canceled
    }
  };
}

export {
  getCountryName,
  getContinentsName,
  getCurrencySymbol,
  getCurrency,
  getSalesChannel,
  // createAdSummary,
  createInventoryAge,
  createListingDaily,
  createFBAInventory
};
