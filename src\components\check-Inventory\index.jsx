import { getAsinStoreCountry } from "@/service/api";
const CheckInventory = ({ onCountryChange, loading = false, page = "" }) => {
  const defaultTreeData = [
    {
      label: '欧盟',
      value: 'EU',
      key: '0',
    },
    {
      label: '英国',
      value: 'UK',
      key: '1',
    },
    {
      label: '美国',
      value: 'US',
      key: '2',
    },
    {
      label: '加拿大',
      value: 'CA',
      key: '3',
    },
    {
      label: '墨西哥',
      value: 'MX',
      key: '4',
    },
    {
      label: '巴西',
      value: 'BR',
      key: '5',
    },
    {
      label: '土耳其',
      value: 'TR',
      key: '6',
    },
    {
      label: '印度',
      value: 'IN',
      key: '7',
    },
    {
      label: '沙特阿拉伯',
      value: 'SA',
      key: '8',
    },
    {
      label: '阿联酋',
      value: 'AE',
      key: '9',
    },
    {
      label: '日本',
      value: 'JP',
      key: '10',
    },
    {
      label: '澳大利亚',
      value: 'AU',
      key: '11',
    },
    {
      label: '新加坡',
      value: 'SG',
      key: '12',
    },
  ];
  const [defaultValue, setDefaultValue] = useState([]);
  const [selectedDay, setSelectedDay] = useState(7);
  const [selectedWeekOrMonth, setSelectedWeekOrMonth] = useState("WEEK");
  const [countries, setCountries] = useState([
  ]);
  const [treeData, setTreeData] = useState([]);

  // 获取支持的仓库
  const getSupportWarehouse = async () => {
    const res = await getAsinStoreCountry({});
    // console.log(res, "res")
    if (res && res.data.data.length > 0) {
      // 初始化一个 Set 来存储唯一的仓库
      const warehouseSet = new Set();

      // 遍历所有数据项，合并 warehouse_list
      res.data.data.forEach(dataItem => {
          warehouseSet.add(dataItem);
      });

      // 将 Set 转换为数组，并查找对应的国家信息
      const newTreeData = Array.from(warehouseSet).map(warehouse => {
        return defaultTreeData.find(country => country.value === warehouse);
      }).filter(country => country !== undefined); // 过滤掉未找到的项

      // console.log(newTreeData, "newTreeData");
      newTreeData.sort((a, b) => {
        const order = ['US', 'EU', 'UK', 'JP']; // Added 'CN' and 'IN' as examples
        const indexA = order.indexOf(a.value);
        const indexB = order.indexOf(b.value);

        // If both countries are in the order list, sort by their index
        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB;
        }
        // If only one country is in the order list, prioritize it
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;
        // If neither country is in the order list, sort alphabetically
        return a.value.localeCompare(b.value);
      });
      setTreeData(newTreeData);
      if (newTreeData.length > 0) {
        setDefaultValue([newTreeData[0].value]); // 设置默认值为加载的第一个国家
        onCountryChange([newTreeData[0].value]);
      }
    }
  }


  useEffect(() => {
    getSupportWarehouse();
  }, []);
  const handleCountryChange = (value) => {
    console.log(value, "value")
    // const country = countries.find(country => country.value === value);
    const country = value.map(item => treeData.find(country => country.value === item));
    onCountryChange(value, country.ContinentCode);
  };


  return (
    <div className="automaticpricing-card">
      <ACard shadows={"always"}>
        <ASelect
          style={{ maxWidth: "415px", minWidth: "220px", borderRadius: "5px" }}
          onChange={handleCountryChange}
          disabled={loading}
          defaultValue={defaultValue}
          key={defaultValue}
          maxTagCount={4}
          mode={"multiple"}
        >
          {treeData && treeData.map((country) => (
            <ASelect.Option
              key={country.key}
              value={country.value}
            >
              {country.label}
            </ASelect.Option>
          ))}
        </ASelect>
      </ACard >

    </div>
  );
};

export default CheckInventory;