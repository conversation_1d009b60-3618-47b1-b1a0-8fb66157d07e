import { Col, Form, Row, Select, Input } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect } from 'react';

interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading }) => {
  useEffect(() => {
    handleSearch();
  }, []);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      ...(values.NickName ? { NickName: values.NickName } : {}),
      ...(values.Email ? { Email: values.Email } : {}),
      ...(values.Phone ? { Phone: values.Phone } : {}),
    };
    console.log(params, "params====")
    // return
    search(params);
  };

  return (
    <Form disabled={loading} form={form}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="NickName" label="名称">
            <Input allowClear  placeholder="请输入名称"  onChange={handleSearch} />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="Email" label="邮箱">
            <Input allowClear  placeholder="请输入邮箱"  onChange={handleSearch} />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="Phone" label="手机号">
            <Input allowClear  placeholder="请输入手机号"  onChange={handleSearch} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;