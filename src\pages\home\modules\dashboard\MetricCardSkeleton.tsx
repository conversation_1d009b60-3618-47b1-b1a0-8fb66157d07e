import React from 'react';
import { Card, Skeleton } from 'antd';

const MetricCardSkeleton: React.FC = () => {
  return (
    <Card className="h-full shadow-sm">
      <div className="flex flex-col h-full">
        <Skeleton.Input style={{ width: '60%' }} size="small" active />
        <div className="mt-3">
          <Skeleton.Input style={{ width: '80%' }} size="large" active />
        </div>
        <div className="mt-2">
          <Skeleton.Input style={{ width: '40%' }} size="small" active />
        </div>
      </div>
    </Card>
  );
};

export default MetricCardSkeleton; 