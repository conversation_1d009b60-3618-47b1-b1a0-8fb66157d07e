import spn from '@/assets/imgs/spn.png'
import SystemLogo from '@/components/stateless/common/SystemLogo';
interface LogoProps {
    showSubtitle?: boolean; // 可选的prop，用于控制子标题的显示
  }
  
  export default function Logo({ showSubtitle = false }: LogoProps) {
    return (
      <div className="flex flex-col items-start absolute top-[20px] left-0 z-50 px-4">
        
        <div className="flex items-center space-x-1 text-xl font-bold">
        <SystemLogo className="text-36px text-primary lt-sm:text-36px" />
          <span className="text-primary font-bold">DeepBI</span>
          <span className="text-black font-bold">
            AT<span className="text-red-600">L</span>AS
          </span>
          <img src={spn} onClick={() => { window.open('https://spn.globalsellingcommunity.cn/searchService?keyword=%E6%99%BA%E5%BE%AE%E8%A7%81&queryTypeKey=QUERY_BY_SERVICE_PROVIDER', '_blank') }} alt="spn" className="w-25 pl-2 mt-2 cursor-pointer" />
        </div>
       
        {showSubtitle && (
          <span className="text-gray-600">Next Generation AI Sales System</span>
        )}
      </div>
    );
  }