import { Button, Col, Form, Input, Row, Select } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { localStg } from "@/utils/storage";

interface Props {
  reset: () => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ reset, search, form, loading }) => {
  useEffect(() => {
    handleSearch();
  }, []);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      PayState: values.PayState,
      // PayType: values.PayType,
    };
    search(params);
  };

  return (
    <Form disabled={loading} form={form}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="PayState" label="支付状态">
            <Select
              placeholder="请选择支付状态"
              allowClear
              onChange={handleSearch}
              options={[
                { label: '待支付', value: '0' },
                { label: '成功成功', value: '2' },
                { label: '支付失败', value: '-1' },
              
                // { label: '混合支付', value: '1' },
               
              ]}
            />
          </Form.Item>
        </Col>

        {/* <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="PayType" label="支付方式">
            <Select
              placeholder="请选择支付方式"
              allowClear
              onChange={handleSearch}
              options={[
                { label: '支付宝', value: 'ALIP' },
                { label: '微信支付', value: 'WECH' },
                { label: '对公转账', value: 'PUBA' },
                { label: '赠送', value: 'FREE' },
              ]}
            />
          </Form.Item>
        </Col> */}
      </Row>
    </Form>
  );
});

export default UserSearch;