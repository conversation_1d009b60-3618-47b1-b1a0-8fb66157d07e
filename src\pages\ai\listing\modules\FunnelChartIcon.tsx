import React from 'react';
import { Icon } from '@iconify/react';
import { Popover, Card, Table, Tooltip } from 'antd';
import { VChart } from "@visactor/react-vchart";
import { QuestionCircleOutlined } from '@ant-design/icons';
// import { useMemo } from 'react';
import BigNumber from 'bignumber.js';
interface FunnelChartIconProps {
  countryCode: string;
  data: {
    manual: {
      exposure: number;
      clicks: number;
      orders: number;
      amount: number;
      cost: number;
      previousData?: {
        exposure: number;
        clicks: number;
        orders: number;
        amount: number;
        cost: number;
      };
    };
    auto: {
      exposure: number;
      clicks: number;
      orders: number;
      amount: number;
      cost: number;
      previousData?: {
        exposure: number;
        clicks: number;
        orders: number;
        amount: number;
        cost: number;
      };
    };
    asin: {
      exposure: number;
      clicks: number;
      orders: number;
      amount: number;
      cost: number;
      previousData?: {
        exposure: number;
        clicks: number;
        orders: number;
        amount: number;
        cost: number;
      };
    };
  };
}

const FunnelChartIcon: React.FC<FunnelChartIconProps> = ({ data, countryCode }) => {
  // 对数转换函数
  // const logTransform = (value: number) => {
  //   return value;
  //   // 避免log(0)的情况
  //   if (value <= 0) return 0;
  //   return Math.log10(value);
  // };
  const parseValue = (value: any): number => {
    if (value === "None" || value === null || value === undefined) {
      return 0;
    }
    const num = Number(value);
    return isNaN(num) ? 0 : num;
  };

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}w`;
    }
    return num.toString();
  };


  const calculateRateChange = (name: string, type: string) => {
    // 获取当前数据和历史数据
    const currentData = {
      exposure: parseValue(data[type].exposure),
      clicks: parseValue(data[type].clicks),
      orders: parseValue(data[type].orders)
    };
    const previousData = data[type].previousData ? {
      exposure: parseValue(data[type].previousData.exposure),
      clicks: parseValue(data[type].previousData.clicks),
      orders: parseValue(data[type].previousData.orders)
    } : null;
    
    // 如果没有历史数据，返回默认值
    if (!previousData) {
      return {
        value: 0,
        isRise: false
      };
    }
    
    // 计算当前转化率
    let currentRate = 0;
    // 计算历史转化率
    let previousRate = 0;
    
    if (name === '曝光数') {
      // 点击率 = 点击数 / 曝光数
      currentRate = currentData.clicks / currentData.exposure;
      previousRate = previousData.clicks / previousData.exposure;
    } else if (name === '点击数') {
      // 转化率 = 成单量 / 点击数
      currentRate = currentData.orders / currentData.clicks;
      previousRate = previousData.orders / previousData.clicks;
    }
    
    // 防止除以零的情况
    if (isNaN(currentRate) || isNaN(previousRate) || previousRate === 0) {
      return {
        value: 0,
        isRise: false
      };
    }
    
    // 计算环比变化率
    const change = ((currentRate - previousRate) / previousRate) * 100;
    
    return {
      value: Math.abs(change).toFixed(1),
      isRise: change > 0
    };
  };
  


  const calculatePercents = (data: { exposure: number; clicks: number; orders: number }) => {
    // 使用对数转换后的值来计算图表显示比例
    const logExposure = parseValue(data.exposure);
    const logClicks = parseValue(data.clicks);
    const logOrders = parseValue(data.orders);
     // 计算转化率，并确保不会出现NaN
    const clickRate = data.exposure && data.exposure > 0 ? (parseValue(data.clicks) / parseValue(data.exposure)) : 0;
    const orderRate = data.clicks && data.clicks > 0 ? (parseValue(data.orders) / parseValue(data.clicks)) : 0;
    
    // 计算历史转化率，并确保不会出现NaN
    const oldClickRate = data.previousData && parseValue(data.previousData.exposure) > 0 ? 
      (parseValue(data.previousData.clicks) / parseValue(data.previousData.exposure)) : 0;
    const oldOrderRate = data.previousData && parseValue(data.previousData.clicks) > 0 ? 
      (parseValue(data.previousData.orders) / parseValue(data.previousData.clicks)) : 0;
    
    return [
      { 
        value: logExposure,
        displayValue: data.exposure,
        name: '曝光数',
        percent: clickRate,
        oldPercent: oldClickRate
      },
      { 
        value: logClicks,
        displayValue: data.clicks,
        name: '点击数',
        percent: orderRate,
        oldPercent: oldOrderRate
      },
      { 
        value: logOrders,
        displayValue: data.orders,
        name: '成单量',
      }
    ];
  };

  const createSpec = (data: { exposure: number; clicks: number; orders: number }, title: string,type:string) => ({
    type: 'common',
    width: 330,
    height: 280,
    padding: { top: 0, right: 100, bottom: 0, left: 20 }, 
    color: {
      type: 'ordinal',
      range: ['#00328E', '#4E91FF', '#AEE2FF']
    },
    data: [
      {
        id: type,
        values: calculatePercents(data)
      }
    ],
    series: [
      {
        type: 'funnel',
        maxSize: '80%',
        minSize: '15%',
        isTransform: true,
        shape: 'rect',
          funnel: {
          style: {
            cornerRadius: 4,
            stroke: 'white',
            lineWidth: 2
          },
          state: {
            hover: {
              stroke: '#4e83fd',
              lineWidth: 2
            }
          }
        },
        transform: {
          style: {
            stroke: 'white',
            lineWidth: 2
          },
          state: {
            hover: {
              stroke: '#4e83fd',
              lineWidth: 2
            }
          }
        },
        label: {
          visible: true,
          smartInvert: true,
          style: {
            fontSize: 12,
            lineHeight: 16,
            text: datum => [
              `${datum.name}`,
              `${formatNumber(datum.displayValue)}`,
              // `${(datum.percent * 100).toFixed(1)}%`
            ]
          }
        },
        transformLabel: {
          visible: true,
          style: {
            fill: '#666',
            fontSize: 12
          }
        },
        extensionMark: [
          {
            type: 'polygon',
            dataId: 'funnel',
            style: {
              points: (datum, ctx, params, dataView) => {
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return;
                }
                const nextDatum = data[curIndex + 1];
                const firstDatum = data[0];
  
                const points = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(datum);
                const nextPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(nextDatum);
  
                const firstPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(firstDatum);
  
                const tr = points[1];
                const tb = points[2];
  
                const next_tr = nextPoints[1];
  
                const first_tr = firstPoints[1];
  
                const result = [
                  { x: tr.x + 5, y: (tr.y + tb.y) / 2 },
                  { x: first_tr.x + 20, y: (tr.y + tb.y) / 2 },
                  {
                    x: first_tr.x + 20,
                    y: (tr.y + tb.y) / 2 + (next_tr.y - tr.y) - 10
                  },
                  {
                    x: next_tr.x + 5,
                    y: (tr.y + tb.y) / 2 + (next_tr.y - tr.y) - 10
                  }
                ];
                return result;
              },
              cornerRadius: 5,
              stroke: 'rgb(200,200,200)',
              strokeOpacity: 0.5,
              lineWidth: 2,
              closePath: false
            }
          },
          {
            type: 'symbol',
            dataId: 'funnel',
  
            style: {
              visible: (datum, ctx, params, dataView) => {
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return false;
                }
                return true;
              },
              x: (datum, ctx, params, dataView) => {
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return;
                }
                const nextDatum = data[curIndex + 1];
  
                const nextPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(nextDatum);
  
                const next_tr = nextPoints[1];
  
                return next_tr.x + 5;
              },
              y: (datum, ctx, params, dataView) => {
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return;
                }
                const nextDatum = data[curIndex + 1];
                const points = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(datum);
                const nextPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(nextDatum);
  
                const tr = points[1];
                const tb = points[2];
  
                const next_tr = nextPoints[1];
  
                return (tr.y + tb.y) / 2 + (next_tr.y - tr.y) - 10;
              },
              size: 8,
              scaleX: 0.8,
              symbolType: 'triangleLeft',
              cornerRadius: 2,
              fill: 'rgb(200,200,200)'
            }
          },
          {
            type: 'text',
            dataId: 'funnel',
            style: {
              text: datum => `转化率 ${(datum.percent * 100).toFixed(1)}%`,
              textAlign: 'left',
              textBaseline: 'middle',
              visible: (datum, ctx, params, dataView) => {
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return false;
                }
                return true;
              },
              x: (datum, ctx, params, dataView) => {
                // 与原来相同的x坐标计算
                const data = dataView.latestData;
                if (!data) return;
                const firstDatum = data[0];
                const firstPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(firstDatum);
                const tr = firstPoints[1];
                return tr.x + 20 + 10;
              },
              y: (datum, ctx, params, dataView) => {
                // 向上偏移一点
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return;
                }
                const nextDatum = data[curIndex + 1];
                const points = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(datum);
                const nextPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(nextDatum);
                const tr = points[1];
                const tb = points[2];
                const next_tr = nextPoints[1];
                
                // 向上偏移5个像素
                return ((tr.y + tb.y) / 2 + (next_tr.y - tr.y) - 10 + (tr.y + tb.y) / 2) / 2 - 8;
              },
              fontSize: 13,
              fill: 'black'
            }
          },
          // 第二个文本元素显示变化率
          {
            type: 'text',
            dataId: 'funnel',
            style: {
              text: datum => {
                const rateChange = calculateRateChange(datum.name, type);
                const arrow = rateChange.isRise ? '↑' : '↓';
                return rateChange.value ? `${arrow} ${rateChange.value}%` : '';
              },
              textAlign: 'left',
              textBaseline: 'middle',
              visible: (datum, ctx, params, dataView) => {
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return false;
                }
                return true;
              },
              x: (datum, ctx, params, dataView) => {
                // 与原来相同的x坐标计算
                const data = dataView.latestData;
                if (!data) return;
                const firstDatum = data[0];
                const firstPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(firstDatum);
                const tr = firstPoints[1];
                return tr.x + 20 + 20;
              },
              y: (datum, ctx, params, dataView) => {
                // 向下偏移一点
                const data = dataView.latestData;
                if (!data) return;
                const curIndex = data.findIndex(d => d.name === datum.name);
                if (curIndex === data.length - 1) {
                  return;
                }
                const nextDatum = data[curIndex + 1];
                const points = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(datum);
                const nextPoints = ctx.vchart.getChart().getSeriesInIndex(0)[0].getPoints(nextDatum);
                const tr = points[1];
                const tb = points[2];
                const next_tr = nextPoints[1];
                
                // 向下偏移5个像素
                return ((tr.y + tb.y) / 2 + (next_tr.y - tr.y) - 10 + (tr.y + tb.y) / 2) / 2 + 8;
              },
              fontSize: 13,
              fill: datum => {
                const rateChange = calculateRateChange(datum.name, type);
                return rateChange.isRise ? '#154EC1' : '#FF4C4C';
              }
            }
          }
        ],
        categoryField: 'name',
        valueField: 'value'
      }
    ],
    title: {
      visible: true,
      text: title,
      align: 'center',
      style: {
        fontSize: 14,
        fontWeight: 'bold',
        fill: '#333',
        // textAlign: 'center'
      },
    }
  });


   // 计算 CPC
   const calculateCPC = (spend: number, clicks: number) => {
    if (!clicks) return new BigNumber(0);
    
    return new BigNumber(spend || 0)
      .dividedBy(new BigNumber(clicks))
      .decimalPlaces(2); // 保留2位小数
  };
  

  const columns = [
    {
      title: (
        <span>
          指标
          <Tooltip title="广告类型">
            <QuestionCircleOutlined className="ml-1" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'metric',
      key: 'metric',
      align: 'center',
      render: (text: string) => {
        return <ATag color="blue">{text}</ATag>
      }
    },
    {
      title: (
        <span>
          曝光数
          <Tooltip title="广告展示次数">
            <QuestionCircleOutlined className="ml-1" />
          </Tooltip>
        </span>
      ),
      align: 'center',
      dataIndex: 'exposure',
      key: 'exposure',
      render: (text: string, record: any) => {
        console.log(record)
        // countryCode={form.getFieldValue('country')}
        const exposure = data[record.key].exposure || 0
        const old_exposure = data[record.key].previousData.exposure ?? 0
        return <CurrencySymbol  value={exposure} oldValue={old_exposure} isInteger={true} />;
        // return <span>{text}</span>
      }
    },
    {
      title: (
        <span>
          点击数
          <Tooltip title="用户点击广告的次数">
            <QuestionCircleOutlined className="ml-1" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'clicks',
      key: 'clicks',
      align: 'center',
      render: (text: string, record: any) => {
        const clicks = data[record.key].clicks || 0
        const old_clicks = data[record.key].previousData.clicks ?? 0
        return <CurrencySymbol  value={clicks} oldValue={old_clicks} isInteger={true} />;
      }
    },
    {
      title: (
        <span>
          CPC价格
          <Tooltip title="花费/点击数">
            <QuestionCircleOutlined className="ml-1" />
          </Tooltip>
        </span>
      ),
      align: 'center',
      dataIndex: 'cpc',
      key: 'cpc',
      render: (text: string, record: any) => {
        // 计算当前CPC
        const clicks = data[record.key].clicks || 0;
        const currentCPC = calculateCPC(data[record.key].cost, clicks);
        
        // 计算历史CPC
        const oldClicks = data[record.key].previousData?.clicks || 0;
        const oldCPC = calculateCPC(
          data[record.key].previousData?.cost || 0, 
          oldClicks
        );
    
        return (
          <CurrencySymbol 
          
            countryCode={countryCode} 
            value={currentCPC.toString()} 
            oldValue={oldCPC.toString()}
          />
        );
      }
    },
    {
      title: (
        <span>
          成交金额
          <Tooltip title="广告产生的总销售额">
            <QuestionCircleOutlined className="ml-1" />
          </Tooltip>
        </span>
      ),
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      render: (text: any, record: any) => {
        const amount = data[record.key].amount || 0
        const old_amount = data[record.key].previousData.amount || 0
        return <CurrencySymbol  countryCode={countryCode} value={amount} oldValue={old_amount} />;
      }
    },
    {
      title: (
        <span>
          成单量
          <Tooltip title="通过广告产生的订单数量">
            <QuestionCircleOutlined className="ml-1" />
          </Tooltip>
        </span>
      ),
      align: 'center',
      dataIndex: 'orders',
      key: 'orders',
      render: (text: string, record: any) => {
        const orders = data[record.key].orders || 0
        const old_orders = data[record.key].previousData.orders || 0
        return <CurrencySymbol  isInteger={true} value={orders} oldValue={old_orders} />;
      }
    }
  ];

  const tableData = [
    {
      key: 'manual',
      metric: 'SP-Manual',
      exposure: formatNumber(data.manual.exposure),
      clicks: formatNumber(data.manual.clicks),
      cpc: calculateCPC(data.manual.cost, data.manual.clicks),
      amount: formatNumber(data.manual.amount),
      orders: formatNumber(data.manual.orders),
    },
    {
      key: 'auto',
      metric: 'SP-Auto',
      exposure: formatNumber(data.auto.exposure),
      clicks: formatNumber(data.auto.clicks),
      cpc: calculateCPC(data.auto.cost, data.auto.clicks),
      amount: formatNumber(data.auto.amount),
      orders: formatNumber(data.auto.orders),
    },
    {
      key: 'asin',
      metric: 'SP-Asin',
      exposure: formatNumber(data.asin.exposure),
      clicks: formatNumber(data.asin.clicks),
      cpc: calculateCPC(data.asin.cost, data.asin.clicks),
      amount: formatNumber(data.asin.amount),
      orders: formatNumber(data.asin.orders),
    },
  ];

  const content = (
    <div className="w-[1000px] bg-white rounded-lg p-4">
      <div className="grid grid-cols-3 gap-4">
        <div>
          <VChart 
            spec={createSpec(data.manual, 'SP-Manual 转化','manual')}
            option={{ mode: "desktop-browser" }}
          />
        </div>
        <div>
          <VChart 
            spec={createSpec(data.auto, 'SP-Auto 转化','auto')}
            option={{ mode: "desktop-browser" }}
          />
        </div>
        <div>
          <VChart 
            spec={createSpec(data.asin, 'SP-Asin 转化','asin')}
            option={{ mode: "desktop-browser" }}
          />
        </div>
      </div>
      
      <div className="mt-4">
        <Table 
          columns={columns} 
          dataSource={tableData} 
          size="small"
          pagination={false}
        />
      </div>
{/* 
      <div className="mt-4 text-xs text-gray-500">
        <div>说明：</div>
        <ul className="list-disc pl-4 space-y-1">
          <li>曝光数：广告展示次数</li>
          <li>点击数：用户点击广告的次数</li>
          <li>成单量：通过广告产生的订单数量</li>
          <li>百分比表示相邻两层之间的转化率</li>
        </ul>
      </div> */}
    </div>
  );

  return (
    <Popover 
      content={content}
      title={null}
      trigger="click"
      placement="right"
      overlayClassName="funnel-chart-popover"
      overlayStyle={{ minWidth: '1000px' }}
    >
      <Icon 
        icon="flowbite:chart-outline" 
        className='text-primary text-lg cursor-pointer hover:text-#1890ff' 
      />
    </Popover>
  );
};

export default React.memo(FunnelChartIcon);