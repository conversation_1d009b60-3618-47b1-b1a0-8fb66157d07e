/* src/components/GlobalFloat/style.css */
.float-wrapper {
  position: fixed;
  right: 0;
  bottom: 15%;
  transform: translateY(-50%);
  z-index: 999;
  display: none;
}
.show {
  display: block;
}

.float-wrapper .ant-float-btn-group {
  transform: translateX(40px);
  transition: transform 0.7s ease;
}

.float-wrapper:hover .ant-float-btn-group {
  transform: translateX(10px);
}
.show .ant-float-btn-group {
  transform: translateX(10px);
}

/* 微信二维码容器 */
.wechat-container {
  position: relative;
}

.qrcode-popup.hidden {
  display: none !important;
}

.qrcode-popup.visible {
  display: block !important;
}

/* 优化关闭按钮的悬停效果 */
.qrcode-popup .anticon-close {
  font-size: 16px;
  transition: all 0.3s;
}

.qrcode-popup .anticon-close:hover {
  transform: scale(1.1);
}

@keyframes breathe {
  0% {
    box-shadow: 0 2px 12px rgba(47, 84, 235, 0.2);
    transform: translateY(-50%) scale(1);
  }
  50% {
    box-shadow: 0 2px 20px rgba(47, 84, 235, 0.4);
    transform: translateY(-50%) scale(1.02);
  }
  100% {
    box-shadow: 0 2px 12px rgba(47, 84, 235, 0.2);
    transform: translateY(-50%) scale(1);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-50%);
  }
  50% {
    transform: translateY(-52%);
  }
}



/* 二维码弹出层 */
.qrcode-popup {
  position: absolute;
  right: 60px; /* 调整距离按钮的距离 */
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  display: none;
  min-width: 220px; /* 根据实际二维码大小调整 */
}

.show .qrcode-popup {
  display: block;  /* 默认显示 */
  animation: breathe 1.5s infinite ease-in-out;
}

/* 二维码图片样式 */
.qrcode-popup img {
  width: 100%;
  height: auto;
  padding: 5px;
  display: block;
}
/* 
/* hover 时显示二维码 */
.wechat-container:hover .qrcode-popup {
  display: block;
}

/* 添加小三角箭头 */
.qrcode-popup::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-left: 6px solid white;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  filter: drop-shadow(2px 0 1px rgba(0, 0, 0, 0.1));
}

/* 可选：添加淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-10px, -50%);
  }
  to {
    opacity: 1;
    transform: translate(0, -50%);
  }
}

.wechat-container:hover .qrcode-popup {
  display: block;
  animation: fadeIn 0.3s ease;
}

.show .wechat-container:hover .qrcode-popup {
  animation: none;
}

.qrcode-popup-content {
  text-align: center;
}

.qrcode-header {
  background-color: #154EC1;
  /* background: linear-gradient(180deg, #6282F3 0%, #6A8AF4 100%); */
  color: white;
  padding: 8px 0;
  font-size: 18px;
  font-weight: bold;
  border-radius: 8px 8px 0 0;
  /* animation: bounce 2s infinite ease-in-out; */
}
@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
.scan-tip {
  color: #f5222d;
  font-size: 14px;
  /* margin: 8px 0; */
  /* animation: blink 1s infinite; */
}