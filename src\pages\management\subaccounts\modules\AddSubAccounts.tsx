import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Modal } from 'antd';
import { OuthList, sonAdd } from '@/service/api';
// import { sonIndex,,sonChange,sonUpdate,sonDelete } from '@/service/api';
interface AddSubAccountsProps {
    onInvoiceApplied: () => void;
}

const AddSubAccounts: React.FC<AddSubAccountsProps> = forwardRef((props, ref) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    const { formRules, createConfirmPwdRule } = useFormRules();
    const [outhList, setOuthList] = useState([]);
    const [loading, setLoading] = useState(false);
    const { t } = useTranslation();
    useImperativeHandle(ref, () => ({
        showModal: () => setVisible(true),
        hideModal: () => setVisible(false),
    }));

    useEffect(() => {
        const getPermissions = async () => {
            try {
                const res = await OuthList();
                if (res && res.data) {
                    // 便利res.data 
                    const options = Object.keys(res.data).map(key => ({
                        value: key,
                        label: res.data[key].ShopName
                    }));
                    console.log(options, 'options')
                    setOuthList(options);
                }
            } catch (error) {
                console.error('Failed to fetch permissions:', error);
            }
        };
        if (visible) {
            setLoading(true);
            getPermissions();
            setLoading(false);
        }else{
            form.resetFields()
        }
    }, [visible]);

    const handleOk = async () => {
        form.validateFields()
            .then(async values => {
                setLoading(true);
                const requestParams = {
                    ...values,
                    SonRight: values.SonRight.join(',')
                }
                // console.log(requestParams,'requestParams')
                // return
                const res = await sonAdd(requestParams);
                console.log(res);
                if (res && res.data) {
                    window?.$message?.success('子账号添加成功');
                    setVisible(false);
                    props.onInvoiceApplied();
                }
                setLoading(false);
            })
            .catch(info => {
                console.log('Validate Failed:', info);
            });
    };

    const handleCancel = () => {
        setVisible(false);
    };

    // const options = ()=>{
    //     console.log(outhList,'outhList')
    //     return []
    // }

    return (
        <>
         <style>
        {`
          @media (max-width: 1440px) {
            .responsive-form {
              max-height: 520px;
            }
          }
          @media (min-width: 1441px) and (max-width: 1800px) {
            .responsive-form {
              max-height: 800px;
            }
          }
        `}
      </style>
        <Modal

            title="新增子账号"
            open={visible}
            width={600}
            // height={500}

            onOk={handleOk}
            onCancel={handleCancel}
            confirmLoading={loading}
            okButtonProps={{ loading }}
            centered
            maskClosable={false}
        >

            <Form form={form} layout="vertical"  className="responsive-form overflow-auto"
          style={{ overflowY: 'scroll' }}>
                <Form.Item
                    name="Email"
                    label="邮箱"
                    rules={formRules.email}
                >
                    <Input placeholder="请输入邮箱" />
                </Form.Item>
                <Form.Item
                    name="Phone"
                    label="手机号"
                    rules={[formRules.phone[1]]} // 只使用正则验证规则，不使用必填规则
                >
                    <Input placeholder={t('page.login.common.phonePlaceholder')}></Input>
                </Form.Item>

                <Form.Item
                    name="NickName"
                    label="用户名称"
                    rules={[{ required: true, message: '请输入用户名称' }]}
                >
                    <Input maxLength={120} />
                </Form.Item>
                <Form.Item
                    name="Password"
                    label="密码"
                    rules={formRules.pwd}
                >
                    <Input.Password
                        autoComplete="Password"
                        placeholder={t('请输入最短8位，包含大小写字母、数字的密码')}
                    ></Input.Password>
                </Form.Item>
                <Form.Item
                    label="确认密码"
                    name="RePassword"
                    rules={createConfirmPwdRule(form, 'Password')}
                >
                    <Input.Password
                        autoComplete="RePassword"
                        placeholder={t('page.login.common.confirmPasswordPlaceholder')}
                    ></Input.Password>
                </Form.Item>
                <Form.Item
                    name="SonRight"
                    label="权限列表"
                    rules={[{ required: true, message: '请选择权限列表' }]}
                >
                    <ASelect mode="multiple" placeholder="请选择权限" options={outhList} />
                </Form.Item>
                <Form.Item
                    name="Oother"
                    label="备注"
                    // rules={[{ required: true, message: '请输入备注' }]}
                >
                    <Input.TextArea rows={4} />
                </Form.Item>
            </Form>
        </Modal>
        </>
    );
});

export default AddSubAccounts;