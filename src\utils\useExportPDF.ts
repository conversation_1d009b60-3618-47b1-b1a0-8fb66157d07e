import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

/**
 * 导出PDF
 * @param {导出后的文件名} title 
 * @param {要导出的dom节点：react使用ref} ele 
 */
export const useExportPDF = () => {
  let [loading, setLoading] = useState(false);

  const exportPDF = async (title:string, element:any) => {
    if (element) {
      setLoading(true);
      let width = element.offsetWidth / 2;
      let height = element.offsetHeight / 2;
      let limit = 14400;
      if (height > limit) {
        let contentScale = limit / height;
        height = limit;
        width *= contentScale;
      }
      console.log(width ,height)
      html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: false,
        ignoreElements: (element) => {
          if (element.id === 'ignoreBtnElement') return true;
          return false;
        }
      }).then(canvas => {
        let context = canvas.getContext('2d');
        let orientation;
        if (context) {
          context.mozImageSmoothingEnabled = false;
          context.webkitImageSmoothingEnabled = false;
          context.msImageSmoothingEnabled = false;
          context.imageSmoothingEnabled = false;
        }
        // let pageData = canvas.toDataURL('image/jpg', 1.0);
        let pageData = canvas.toDataURL('image/webp', 0.7);
        let img = new Image();
        img.src = pageData;
        img.onload = function () {
          console.log('Image dimensions before scaling:', img.width, img.height);
          const maxDimension = 2000; // 尝试更小的最大尺寸
          let scale = Math.min(maxDimension / img.width, maxDimension / img.height);
          img.width *= scale;
          img.height *= scale;
          console.log('Scaled image dimensions:', img.width, img.height);

          // 确保尺寸在合理范围内
          const maxA4Width = 210; // A4纸张最大宽度（毫米）
          const maxA4Height = 297; // A4纸张最大高度（毫米）
          scale = Math.min(maxA4Width / img.width, maxA4Height / img.height);
          img.width *= scale;
          img.height *= scale;
          console.log('Adjusted image dimensions for A4:', img.width, img.height);

          let pdf;
          orientation = width > height ? 'l' : 'p';
          let pdfWidth = img.width * 0.264583; // 1 pixel = 0.264583 mm
          let pdfHeight = img.height * 0.264583;
          pdf = new jsPDF(orientation, 'mm', [pdfWidth, pdfHeight]);
          let pageData = canvas.toDataURL('image/jpeg', 0.7); // 改为使用 JPEG 格式
          pdf.addImage(pageData, 'JPEG', 0, 0, pdfWidth, pdfHeight);
          pdf.save(`${title}.pdf`);
          setLoading(false);
        };
      });
    }
  }

  return { loading, exportPDF };
}
