import { Stati<PERSON>, Tooltip } from 'antd';
import { SyncOutlined } from '@ant-design/icons';
import { useState } from 'react';
// import { useAppSelector } from '@/store/slice/auth';
import { useLogin } from '@/hooks/common/login';
import { selectUserInfo } from '@/store/slice/auth';
import CountUp from 'react-countup';
import { AppContext } from './AppContext';
export default function AccountOverview() {
  const userInfo = useAppSelector(selectUserInfo);
  const [refreshing, setRefreshing] = useState(false);
  // const [showPayModal, setShowPayModal] = useState(false);
  const { toGroupLogin } = useLogin();
  const { showPayModal, setShowPayModal } = useContext(AppContext)!;

      // 添加格式化函数
  const formatter: StatisticProps['formatter'] = (value) => (
        <CountUp
            end={value as number}
            separator=","
            decimals={2}
            duration={1} // 动画持续时间
            preserveValue
        />
  );


  const handleRefreshBalance = async () => {
    if (refreshing) return;
    setRefreshing(true);
    setTimeout(() => {
      try {
        toGroupLogin(false, true);
      } finally {
        setRefreshing(false);
      }
    }, 1000);
  };

  const handleClosePayModal = () => {
    setShowPayModal(false);
    handleRefreshBalance();
  };
  useEffect(() => {
    handleRefreshBalance();
  }, []);

  return (
    <div className="mb-6">
       {showPayModal && <PayModel open={showPayModal} onCancel={handleClosePayModal} />}
      <h3 className="text-base mb-4 font-500 text-lg flex items-center">
        <SvgIcon localIcon="walletsmall" className="w-6 h-6 mr-2" />
        账户概况
      </h3>
      <div className="bg-gray-50 p-4 rounded-md">
        <div className="flex items-center">
          <SvgIcon localIcon="wallet" className="w-20 h-20 mr-4" />
          <div>
            <div className="text-gray-500">
            公司账户余额
              <Tooltip title="刷新余额">
                <SyncOutlined
                  className={`ml-2 cursor-pointer hover:text-primary ${refreshing ? 'animate-spin' : ''}`}
                  onClick={handleRefreshBalance}
                />
              </Tooltip>
            </div>
            {/* <Statistic
              prefix="¥"
              value={userInfo.SumBalance ?? 0}
              precision={2}
              valueStyle={{
                color: '#e1ab51',
                fontSize: '26px',
                fontWeight: 500
              }}
            /> */}
            <ASpace align="center">
              <Statistic
                value={userInfo.SumBalance ?? 0}
                precision={2}
                formatter={formatter}
                valueStyle={{
                  color: '#e1ab51',
                  fontSize: '26px',
                  fontWeight: 500
                }}
                prefix="¥"
              />
              <div>
                <AButton
                  type="link"
                  size="small"
                  onClick={() => setShowPayModal(true)}
                  className="ml-2"
                >
                  充值
                </AButton>
              </div>
            </ASpace>
          </div>
        </div>
      </div>
    </div>
  );
}