import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Modal, Form, Input, Button } from 'antd';

interface ChangeNameModalProps {
  onSubmit: (values: { company_name: string; nick_name: string }) => void;
  initialValues: { company_name: string; nick_name: string };
}

const ChangeNameModal = forwardRef(({ onSubmit, initialValues }: ChangeNameModalProps, ref) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false),
  }));
  useEffect(() => {
    if (visible) {
      form.setFieldsValue(initialValues);
    }
  }, [visible, initialValues]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
      // setVisible(false);
    } catch (error) {
      console.error('Validation Failed:', error);
    }
  };

  return (
    <Modal
      centered
      maskClosable={false}
      title="修改名称"
      open={visible}
      onCancel={() => setVisible(false)}
      footer={[
        <Button key="back" onClick={() => setVisible(false)}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          确认
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" initialValues={initialValues}>
        <Form.Item
          name="company_name"
          label="公司名称"
          rules={[{ required: true, message: '请输入公司名称' }]}
        >
          <Input placeholder="请输入公司名称" />
        </Form.Item>
        <Form.Item
          name="nick_name"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default ChangeNameModal;