import React, { useEffect, useState } from "react";
import { Form, Modal, Button } from '@douyinfe/semi-ui';
import { useTranslation } from "react-i18next";
import api from "@/api";
import { Icon } from '@iconify/react';
import { useLanguage } from "@/store/settingsStore";
import { useSettingsStore } from "@/store/settingsStore.ts";
export default function ModelAuthorization() {
    const settingStore = useSettingsStore();
    const { t } = useTranslation();
    const language = useLanguage();
    const [countries, setCountries] = useState([]);
    const regionData = [
        {
            label: '北美',
            children: [
                { value: 'US', label: '美国' },
                { value: 'CA', label: '加拿大' },
                { value: 'MX', label: '墨西哥' },
                { value: 'BR', label: '巴西' }
            ],
        },
        {
            label: '欧洲',
            children: [
                { value: 'GB', label: '英国' },
                { value: 'FR', label: '法国' },
                { value: 'DE', label: '德国' },
                { value: 'IT', label: '意大利' },
                { value: 'ES', label: '西班牙' },
                { value: 'NL', label: '荷兰' },
                { value: 'SE', label: '瑞典' },
                { value: 'PL', label: '波兰' }
            ],
        },
        {
            label: '远东',
            children: [
                { value: 'JP', label: '日本' },
                { value: 'SG', label: '新加坡' }
            ],
        },
    ];

    const handleOk = () => {
        console.log('ok')
    }
    const handleCancel = () => {
        console.log('cancel')
    }
    const handleAfterClose = () => {
        console.log('afterClose')
    }
    const handleSubmit = (values) => {
        console.log(values)
    }
    return (
        <Modal
            title={t('sys.authmodel.title')}
            visible={settingStore.settings.isAuthorization === '1'}
            onOk={handleOk}
            // afterClose={handleAfterClose} //>=1.16.0
            onCancel={handleCancel}
            closeOnEsc={true}
        >
            <Form 
            // layout='horizontal' 
            onSubmit={values => handleSubmit(values)}
            labelPosition='top'
            >
             <Form.InputGroup label={{ text: t('sys.authmodel.regiontitle') , required: true}} labelPosition='top'>
                <Form.Select field="region" 
                style={{ width: 280 }}
                placeholder={t('sys.authmodel.accountnameplaceholder')} 
                rules={[{ required: true }]} 
                showClear
                filter>
                    {regionData.map((group, index) => (
                        <Form.Select.OptGroup className="auth-form-optgroup" label={group.label} key={`${index}-${group.label}`}>
                            {group.children.map((option, index2) => (
                                <Form.Select.Option value={option.value} key={`${index2}-${group.label}`}>
                                    <Icon
                                        className="mr-2"
                                        icon={`circle-flags:${option.value.toLowerCase()}`}
                                        width={22}
                                        height={22}
                                    />
                                    {option.label}
                                </Form.Select.Option>
                            ))}
                        </Form.Select.OptGroup>
                    ))}
                </Form.Select>
                <Form.Input
                label={t('sys.authmodel.accountname')}
                style={{ width: 280 }}
                field='name' trigger='blur'  rules={[
                        { required: true },
                    ]} initValue='Semi'></Form.Input>
                <Button htmlType='submit'>提交</Button>
            </Form.InputGroup>
            </Form>
        </Modal>
    );
}
