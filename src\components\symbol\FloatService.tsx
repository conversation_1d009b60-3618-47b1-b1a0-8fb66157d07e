import { Badge, Dropdown } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import { useLocation } from 'react-router-dom';

// 用于ButtonIcon的类型定义
interface ButtonIconProps {
  children: React.ReactNode;
  tooltipContent?: string;
  onClick?: () => void;
  className?: string;
}

// const ButtonIcon: React.FC<ButtonIconProps> = ({ children, tooltipContent, onClick, className }) => {
//   return (
//     <div
//       className={`px-12px cursor-pointer flex-center h-full hover:bg-#f5f5f5 dark:hover:bg-#333 transition-all duration-300 ${className || ''}`}
//       onClick={onClick}
//       title={tooltipContent}
//     >
//       {children}
//     </div>
//   );
// };

const FloatService: React.FC = () => {
  const location = useLocation();
  //   const isLoginPage = location.pathname.includes('/login');
  const [showQRCode, setShowQRCode] = useState(false);

  //   // 当路由变化时更新二维码显示状态
  //   useEffect(() => {
  //     // 登录页时默认显示二维码
  //     if (isLoginPage) {
  //       setShowQRCode(true);
  //     } else {
  //       setShowQRCode(false);
  //     }
  //   }, [isLoginPage]);

  const handleToggleQRCode = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowQRCode(!showQRCode);
  };

  const handleCloseQRCode = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowQRCode(false);
  };

  const qrCodeContent = (
    <div className="qrcode-popup-content relative w-250px rounded-lg bg-white pb-2 shadow-lg">
      <CloseOutlined
        className="absolute right-2 top-2 cursor-pointer text-white hover:text-gray-800"
        onClick={handleCloseQRCode}
      />
      <p className="qrcode-header mb-3 text-center font-bold">扫码添加产品顾问</p>
      <div className="flex justify-center">
        <img
          src="https://www.deepbi.cn/kefu.png"
          alt="微信客服"
          className="h-180px w-180px"
        />
      </div>
      <p className="my-2 text-center text-sm text-gray-600">👆 立即扫码</p>
      <p className="flex items-center justify-center pb-2 text-base text-base text-#f5222d font-medium">
        <SvgIcon
          localIcon="couponnew"
          className="mr-1 h-5 w-5"
        />
        领取2490元优惠券
      </p>
    </div>
  );

  return (
    <ButtonIcon
      className="relative"
      tooltipContent=""
      onClick={handleToggleQRCode}
      // 移入触发
      // onMouseEnter={handleToggleQRCode}
      // onMouseLeave={handleCloseQRCode}
    >
      <Icon
        icon="tdesign:service"
        className="text-20px"
      />
      {/* 弹出的二维码 */}
      {showQRCode && <div className="absolute right-0 top-full z-50 mt-2">{qrCodeContent}</div>}
    </ButtonIcon>
  );
};

export default FloatService;
