import { combineSlices, configureStore } from "@reduxjs/toolkit";
import type { Action, ThunkAction } from "@reduxjs/toolkit";
import { appSlice } from "./slice/app/index";
import { themeSlice } from "./slice/theme/index";
import { authSlice } from "./slice/auth/index";
import { chartCacheSlice } from "./slice/chart/index";
import { routeSlice } from "./slice/route";
import { tabSlice } from "./slice/tab";
import { messageSlice } from "./slice/message/index";

// `combineSlices` automatically combines the reducers using
// their `reducerPath`s, therefore we no longer need to call `combineReducers`.
const rootReducer = combineSlices(
  routeSlice,
  appSlice,
  authSlice,
  themeSlice,
  tabSlice,
  chartCacheSlice,
  messageSlice,
);

// Infer the `RootState` type from the root reducer
export type RootState = ReturnType<typeof rootReducer>;

export const store = configureStore({
  reducer: rootReducer,
});

// Infer the type of `store`
export type AppStore = typeof store;
// Infer the `AppDispatch` type from the store itself
export type AppDispatch = AppStore["dispatch"];
// eslint-disable-next-line @typescript-eslint/no-invalid-void-type
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action
>;
