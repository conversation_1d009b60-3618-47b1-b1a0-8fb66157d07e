import { useState, useEffect } from "react";
import { servicePackage } from "@/service/api";
import { AppContext } from "./AppContext";
import { useSearchParams } from 'react-router-dom';
interface PricingPackagesProps {
    onPackageSelect: (packageId: number) => void;
}
const PackageSkeleton = () => (
  <div className="flex flex-nowrap justify-around space-x-4">
      {[1].map((item) => (
          <div
              key={item}
              className="p-6 relative rounded-lg text-center w-full md:w-80 bg-white animate-pulse shadow-lg"
          >
              <div className="h-7 bg-purple-200 rounded mb-4"></div>
              <div className="h-5 bg-purple-200 rounded w-3/4 mx-auto mb-4"></div>
              <div className="h-8 bg-purple-200 rounded w-1/2 mx-auto mb-4"></div>
              <div className="h-10 bg-purple-200 rounded-full w-2/3 mx-auto mb-4"></div>
              <div className="space-y-2">
                  {[1, 2, 3, 4, 5, 6].map((line) => (
                      <div key={line} className="flex items-center justify-center">
                          <div className="w-6 h-6 bg-purple-200 rounded-full mr-2"></div>
                          <div className="h-4 bg-purple-200 rounded w-[200px]"></div>
                      </div>
                  ))}
              </div>
          </div>
      ))}
  </div>
);


const GRADIENTS = {
    basic: "bg-gradient-to-br from-[#2e2e4e] to-[#1f1f3a]",
    selected: "bg-gradient-to-br from-[#242538] to-[#1a1a2e]",
    premium: "bg-gradient-to-br from-[#2d2d4d] to-[#1f1f3a]"
 };

export const PricingPackages: React.FC<PricingPackagesProps> = () => {
    const { selectedPackage, setSelectedPackage, parseFormattedData } = useContext(AppContext)!;
    const [searchParams] = useSearchParams();
    
    const [loading, setLoading] = useState(true);
    const [packageList, setPackageList] = useState([]);
    const [selectedPackageId, setSelectedPackageId] = useState(1);

    const features = [
      "AI自动探索同类ASIN",
      "AI自动探索重点词和长尾词",
      "Al量化调价智能逼近最优ACOS",
      "AI诊断 Listing （托管2周后）",
      "AI自动调预算控成本",
      "AI自动否定低产出关键词",
      "每站点限50个Listing（特例请联系客服）"
        // "AI自动探索重点词和长尾词",
        // "AI自动探索同类ASIN",
        // "AI量化调价智能逼近目标ACOS",
        // "AI广告诊断",
        // "AI智能扩大销量",
        // "AI自动否定低产出关键词"
    ];

    const getPackageList = async () => {
        setLoading(true);
        const OneMonthKeyParam = searchParams.get("OneMonthKey");
        const shopData = parseFormattedData();
        const firstKey = Object.keys(shopData)[0];
        const firstValue = shopData[firstKey];
        try {
            const res = await servicePackage({
                ShopID:firstKey,
                CountryCode:firstValue,
                ...OneMonthKeyParam && { OneMonthKey: OneMonthKeyParam }
            });
            if (res && res.data) {
                setPackageList(res.data);
                // onPackageSelect(res.data[1].ID);
                // 
                setSelectedPackage(res.data[res.data.length - 1]);
                setSelectedPackageId(res.data.length - 1);
            }
        } catch (error) {
            console.error('Failed to fetch packages:', error);
        }
        setTimeout(() => {
            setLoading(false);
        }, 1000);
    };

    useEffect(() => {
        const data = searchParams.get("data");
        console.log(data,"data")
        // 
        getPackageList();
    }, []);

    const handlePackageSelect = (index: number, packageId: number) => {
        setSelectedPackageId(index);
        // onPackageSelect(packageId);
        setSelectedPackage(packageList[index]);
    };

    if (loading) {
        return (
            <div className="text-white p-2">
                <PackageSkeleton />
            </div>
        );
    }

    return (
      <div className="section__padding bg-gradient-to-b from-white to-[#F8F9FF]" id="home">
        <div className="max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 flex justify-center
          gap-4 sm:gap-6 lg:gap-8 2xl:gap-10 py-4">
          {packageList.map((pkg, index) => {
            const isSelected = selectedPackageId === index;
            const isLastPlan = index === packageList.length - 1;
            
            // Calculate total months including additional days
            const totalMonths = pkg.ServiceMonth + (pkg.ServiceAddDay / 30);
            const monthlyFee = (pkg.Fee / totalMonths).toFixed(2); // Calculate monthly fee
    
            return (
              <div 
                onClick={() => handlePackageSelect(index, pkg.ID)}
                className={`px-2 relative bg-white rounded-2xl sm:rounded-3xl cursor-pointer
                  ${isSelected 
                    ? 'ring-2 sm:ring-4 ring-purple-400 shadow-xl sm:shadow-2xl shadow-purple-200 scale-102 sm:scale-105' 
                    : 'shadow-lg sm:shadow-xl hover:shadow-xl sm:hover:shadow-2xl hover:-translate-y-1 sm:hover:-translate-y-2'
                  } transition-all duration-300 transform`}
                key={pkg.ID}
              >
                <div className="p-6 text-center">
                
                  
                  {pkg.OnlyFirstBuy && (
                    <div className="absolute -right-1 sm:-right-2 -top-1 sm:-top-2 
                      bg-gradient-to-r from-orange-500 to-pink-500 
                      text-white px-2 sm:px-3 py-0.5 sm:py-1 
                      rounded-full text-xs sm:text-sm font-medium 
                      transform rotate-12 shadow-lg">
                      尝鲜价
                    </div>
                  )}
    
                  {isLastPlan && (
                    <div className="absolute -right-1 sm:-right-2 -top-1 sm:-top-2 
                      bg-gradient-to-r from-blue-500 to-purple-500 
                      text-white px-2 sm:px-3 py-0.5 sm:py-1 
                      rounded-full text-xs sm:text-sm font-medium 
                      transform rotate-12 shadow-lg">
                      最佳推荐
                    </div>
                  )}
    
                  <div className="mb-2">
                  <span className="text-sm text-gray-600">仅需</span>
                    <div className="flex items-end justify-center">
                   
                      <span className="text-base font-medium text-gray-600">¥</span>
                      <span className="text-3xl font-bold text-purple-600">{monthlyFee}</span> {/* Highlight monthly fee */}
                      <span className="text-base text-gray-600">/月</span>
                    </div>
                    <button 
                      className={`w-full flex items-center justify-center py-4 my-4 px-4 rounded-lg sm:rounded-xl 
                        text-base font-bold transition-all duration-200 ${
                        isSelected 
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:shadow-lg hover:shadow-purple-200' 
                          : 'bg-gradient-to-r from-purple-500/90 to-pink-500/90 text-white hover:shadow-lg hover:opacity-90'
                      }`} 
                    >
                        {/* <div className="text-2xl font-bold tracking-tight ">
                  <span className="text-xl font-medium text-gray-600">¥</span>
                    {pkg.Fee}
                  </div> */}
                      获取{pkg.ServiceContent} （¥{pkg.Fee}）
                    </button>
                  </div>
    
                  <div className="space-y-2 2xl:space-y-4 text-left">
                    {features.map((feature, idx) => (
                      <div key={idx} className="flex items-start gap-2 sm:gap-3">
                        <svg 
                          className={`w-4 h-4 sm:w-5 sm:h-5 mt-1 flex-shrink-0 ${
                            isSelected ? 'text-purple-500' : 'text-green-500'
                          }`} 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span className={`text-sm 2xl:text-base leading-relaxed ${
                          isSelected ? 'text-gray-800' : 'text-gray-600'
                        }`}>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
    };