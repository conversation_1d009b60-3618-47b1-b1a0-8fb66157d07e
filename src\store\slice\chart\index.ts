import { createSlice, PayloadAction } from '@reduxjs/toolkit';

type ChartCacheItem = {
  data: any;
  timestamp: number;
};

type ChartCacheState = {
  cache: Record<string, ChartCacheItem>;
};

const initialState: ChartCacheState = {
  cache: {}
};

export const chartCacheSlice = createSlice({
  name: 'chartCache',
  initialState,
  reducers: {
    setChartCache: (state, action: PayloadAction<{ key: string; data: any }>) => {
      const { key, data } = action.payload;
      console.log(key, "key");
      state.cache[key] = { data, timestamp: Date.now() };
      // console.log(state.cache, "state.cache");
    },
    clearChartCache: (state) => {
      state.cache = {};
    }
  },
  selectors: {
    selectChartCache: chartCache => chartCache.cache,
  }
});

export const { setChartCache, clearChartCache } = chartCacheSlice.actions;
export const {
  selectChartCache
} = chartCacheSlice.selectors;
// export const selectChartCache = (state: { chartCache: ChartCacheState }) => state.chartCache.cache;

// export default chartCacheSlice.reducer;