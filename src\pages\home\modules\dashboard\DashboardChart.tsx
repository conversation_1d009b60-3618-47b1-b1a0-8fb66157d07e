import React, { useEffect, useState } from 'react';
import { Card, Empty, Spin } from 'antd';
import { VChart } from '@visactor/react-vchart';
import { useTranslation } from 'react-i18next';
import type { ChartData } from '@/types/dashboard';

interface DashboardChartProps {
  data: ChartData;
}

const DashboardChart: React.FC<DashboardChartProps> = ({ data }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const { title, data: chartData, type } = data;

  // 检查数据有效性
  const isValidData = () => {
    if (!chartData || !Array.isArray(chartData)) {
      return false;
    }
    if (chartData.length === 0) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    // 数据无效时直接关闭 loading
    setLoading(false);
  }, [chartData]);

  // 根据图表类型生成不同的配置
  const getChartSpec = () => {
    const noDataText = '-';

    // 创建默认数据，用于没有数据时显示空图表
    const emptyData =
      type === 'pie'
        ? [{ category: noDataText, value: 100, percentage: 100 }]
        : type === 'bar'
          ? [{ category: noDataText, value: 0, type: noDataText }]
          : [{ date: noDataText, value: 0, category: noDataText }];

    // 使用实际数据或空数据
    const values = isValidData() ? chartData : emptyData;

    // 预处理数据，翻译分类字段
    const processedValues = values.map(item => {
      const newItem = { ...item };

      // 如果存在category字段且以page.开头，对其进行翻译
      if (newItem.category && typeof newItem.category === 'string' && newItem.category.startsWith('page.')) {
        newItem.category = t(newItem.category);
      }

      // 如果存在type字段且以page.开头，对其进行翻译
      if (newItem.type && typeof newItem.type === 'string' && newItem.type.startsWith('page.')) {
        newItem.type = t(newItem.type);
      }

      return newItem;
    });

    const baseConfig = {
      type,
      data: {
        values: processedValues
      },
      title: {
        visible: true,
        text: title && title.startsWith('page.') ? t(title) : title,
        style: {
          fontSize: 16,
          fill: '#333',
          fontWeight: 500
        }
      },
      animation: {
        appear: {
          duration: 600,
          easing: 'cubicOut'
        }
      },
      theme: {
        type: 'light',
        color: ['#1890FF', '#2FC25B', '#FACC14', '#223273', '#8543E0', '#13C2C2']
      }
    };

    // 如果没有数据，添加空状态提示
    if (!isValidData()) {
      baseConfig.annotations = [
        {
          type: 'text',
          position: ['50%', '50%'],
          content: noDataText,
          style: {
            fontSize: 14,
            fill: '#999',
            textAlign: 'center'
          }
        }
      ];
    }

    switch (type) {
      case 'line':
        return {
          ...baseConfig,
          xField: 'date',
          yField: 'value',
          seriesField: 'category',
          point: {
            style: {
              visible: true
            }
          },
          line: {
            style: {
              curveType: 'monotone'
            }
          },
          legends: [
            {
              visible: true,
              position: 'middle',
              orient: 'bottom'
            }
          ]
        };

      case 'bar':
        return {
          ...baseConfig,
          xField: 'category',
          yField: 'value',
          seriesField: 'type',
          barMaxWidth: 30,
          axes: [
            {
              orient: 'bottom',
              label: {
                visible: true,
                autoRotate: true,
                autoHide: true,
                style: {
                  angle: 45,
                  fontSize: 12,
                  fill: '#666'
                }
              }
            },
            {
              orient: 'left',
              label: {
                visible: true,
                style: {
                  fontSize: 12,
                  fill: '#666'
                }
              }
            }
          ],
          legends: {
            visible: true,
            position: 'middle',
            orient: 'bottom',
            item: {
              height: 24,
              spacing: 16
            }
          }
        };

      case 'pie':
        return {
          ...baseConfig,
          type: 'pie',
          valueField: 'value',
          categoryField: 'category',
          outerRadius: 0.8,
          innerRadius: 0.5,
          padAngle: 0.6,
          pie: {
            style: {
              cornerRadius: 10,
              fillOpacity: isValidData() ? 1 : 0.3 // 没有数据时降低透明度
            },
            state: {
              hover: {
                outerRadius: 0.85,
                stroke: '#000',
                lineWidth: 1
              },
              selected: {
                outerRadius: 0.85,
                stroke: '#000',
                lineWidth: 1
              }
            }
          },
          legends: {
            visible: false
          },
          label: {
            visible: isValidData(), // 没有数据时不显示标签
            formatMethod: (label: any, data: any) => {
              return {
                type: 'rich',
                text: [
                  {
                    text: `${data.percentage.toFixed(2)}%\n`,
                    fill: 'rgba(0, 0, 0, 0.92)',
                    fontSize: 16,
                    fontWeight: 500,
                    stroke: false
                  },
                  {
                    text: data.category,
                    fill: 'rgba(0, 0, 0, 0.55)',
                    fontSize: 12,
                    fontWeight: 400,
                    stroke: false
                  }
                ]
              };
            }
          }
        };

      default:
        return baseConfig;
    }
  };

  return (
    <Card className="shadow-sm">
      <Spin spinning={loading}>
        <div style={{ height: '400px', width: '100%', position: 'relative' }}>
          <VChart
            spec={getChartSpec()}
            options={{
              mode: 'desktop-browser',
              autoFit: true
            }}
            onError={e => {
              console.error('Chart error:', e);
              setLoading(false);
            }}
            onMounted={() => {
              setLoading(false);
            }}
          />
        </div>
      </Spin>
    </Card>
  );
};

export default DashboardChart;
