import { request } from '../request';

// 子账号列表
export const sonIndex = (data: any) => request({ url: "/user/son_index", method: "post", data })

// 子账号添加
export const sonAdd = (data: any) => request({ url: "/user/son_add", method: "post", data })

// 修改账号-获取原始信息
export const sonChange = (data: any) => request({ url: "/user/son_change", method: "get", data })

// 修改账号-修改
export const sonUpdate = (data: any) => request({ url: `/user/son_change?ID=${data.ID}`, method: "post", data })

// 修改账号-删除
export const sonDelete = (data: any) => request({ url: "/user/son_delete", method: "post", data })
