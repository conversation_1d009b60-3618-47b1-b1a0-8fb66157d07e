
import { getOrderShopinfo,haveCoupon,servicePackage } from "@/service/api";

export function Component() {

    const [loading, setLoading] = useState(true);
    const [searchParams] = useSearchParams();

    // 套餐内容
    const [packageList, setPackageList] = useState([]);


    // 获取套餐内容
    const getPackageList = async () => {
        const res = await servicePackage({});
        console.log(res);
        if(res && res.data){
            setPackageList(res.data);
        }
    }
    // 获取url参数
    useEffect(() => {
        const data = searchParams.get('data');
        console.log(data);

        // 获取套餐内容
        getPackageList();

    }, [])

    return (
        <div className="bg-[#1a1a2e] text-white p-8">
      <div className="flex justify-center space-x-4">
        {/* 1-month plan */}
        <div className="bg-[#2e2e4e] p-6 rounded-lg text-center w-80">
          <h2 className="text-xl font-bold">1个月计划</h2>
          <div className="mt-4 text-lg">0% 折扣</div>
          <div className="mt-2 text-3xl font-bold">€9.99/月</div>
          <button className="mt-4 bg-[#4e4e6e] text-white py-2 px-4 rounded-full">
            获取1个月计划
          </button>
          <p className="mt-2 text-sm">30天退款保证</p>
          <p className="mt-1 text-xs">每月€9.99计费</p>
        </div>

        {/* 24-month plan */}
        <div className="bg-[#2e2e4e] p-6 rounded-lg text-center w-80 border-4 border-[#6e6e8e]">
          <h2 className="text-xl font-bold">24个月计划</h2>
          <div className="mt-4 text-lg">70% 折扣</div>
          <div className="mt-2 text-3xl font-bold">€2.99/月</div>
          <button className="mt-4 bg-[#6e6e8e] text-white py-2 px-4 rounded-full">
            获取优惠
          </button>
          <p className="mt-2 text-sm">30天退款保证</p>
          <p className="mt-1 text-xs">前24个月计费€71.76</p>
        </div>

        {/* 12-month plan */}
        <div className="bg-[#2e2e4e] p-6 rounded-lg text-center w-80">
          <h2 className="text-xl font-bold">12个月计划</h2>
          <div className="mt-4 text-lg">50% 折扣</div>
          <div className="mt-2 text-3xl font-bold">€4.99/月</div>
          <button className="mt-4 bg-[#4e4e6e] text-white py-2 px-4 rounded-full">
            获取优惠
          </button>
          <p className="mt-2 text-sm">30天退款保证</p>
          <p className="mt-1 text-xs">前12个月计费€59.88</p>
        </div>
      </div>

      <div className="mt-8 bg-[#2e2e4e] p-6 rounded-lg" style={{ backgroundImage: 'linear-gradient(rgb(64 57 95 / 100%) 0%, rgb(27 19 64 / 100%) 100%)' }}>
        <h3 className="text-lg font-bold">ATLAS 订阅为您提供高级功能</h3>
        <ul className="mt-4 space-y-2">
          <li>AI自动探索重点词和长尾词</li>
          <li>AI自动探索同类ASIN</li>
          <li>AI量化调价智能逼近目标ACOS</li>
          <li>AI广告诊断</li>
          <li>AI智能扩大销量</li>
          <li>AI自动否定低产出关键词</li>
        </ul>
      </div>
    </div>
    );
}