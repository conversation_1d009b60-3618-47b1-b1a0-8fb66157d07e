import { Badge, Button, Form, Input, Modal, Spin } from 'antd';
import { forwardRef, useImperativeHandle } from 'react';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import { CreateListingReport, ShowCountListing } from '@/service/api';

interface AiDiagnosisModalProps {
  countryCode?: string;
}

export interface AiDiagnosisModalRef {
  open: () => void;
  close: () => void;
}

interface ReportInfo {
  ID?: number;
  ShopID?: number;
  CountryCode?: string;
  ReportState?: number;
  Addtime?: string;
  CompareAsin?: string;
  MainAsin?: string;
  ReportFile?: string | null;
  ShowFlag?: number;
  Updatetime?: string | null;
}

const AiDiagnosisModal = forwardRef<AiDiagnosisModalRef, AiDiagnosisModalProps>((props, ref) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [diagnosisCount, setDiagnosisCount] = useState(0);
  const [reportInfo, setReportInfo] = useState<ReportInfo>({});
  const [submitting, setSubmitting] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => {
      setVisible(true);
      fetchDiagnosisInfo();
    },
    close: () => {
      setVisible(false);
      form.resetFields();
    }
  }));

  const fetchDiagnosisInfo = async () => {
    if (!props.countryCode) return;

    setLoading(true);
    try {
      const res = await ShowCountListing({
        CountryCode: props.countryCode
      });
      if (res.data) {
        setDiagnosisCount(res.data.count || 0);
        setReportInfo(res.data.last_report_file || {});
      }
    } catch (error) {
      console.error('获取诊断信息失败:', error);
      //   window.$message?.error('获取诊断信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (!props.countryCode) {
        // window.$message?.warning('请先选择国家');
        return;
      }

      setSubmitting(true);
      const res = await CreateListingReport({
        CountryCode: props.countryCode,
        MainAsin: values.mainAsin,
        CompareAsin: values.compareAsin
      });
      //   判断res.data 不等于空数组 并且等于true
      if ((Array.isArray(res.data) && res.data.length > 0) || res.data === true) {
        // window.$message?.success('诊断报告生成请求已提交');
        fetchDiagnosisInfo(); // 刷新诊断次数和报告URL
      }
    } catch (error) {
      console.error('生成诊断报告失败:', error);
      //   window.$message?.error('生成诊断报告失败');
    } finally {
      setSubmitting(false);
    }
  };

  const onCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  // 判断是否显示报告状态区域
  const shouldShowReportStatus = () => {
    return reportInfo && reportInfo.ReportState;
  };

  const downloadReport = async () => {
    if (reportInfo && reportInfo.ID) {
      try {
        // 开始下载时显示加载状态
        setLoading(true);

        const downloadUrl = `${window.location.origin}/api/user/download_report/${reportInfo.ID}`;
        const response = await fetch(downloadUrl);

        if (!response.ok) {
          throw new Error('下载失败');
        }

        // 获取文件流
        const blob = await response.blob();

        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 自定义文件名：使用ASIN信息和日期
        const date = new Date().toLocaleDateString('zh-CN').replace(/\//g, '');
        const fileName = `AI Listing Report-${reportInfo.MainAsin}-${reportInfo.CompareAsin}-${date}.pdf`;
        link.download = fileName;

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // window.$message?.success('报告下载成功');
      } catch (error) {
        console.error('下载报告失败:', error);
        // window.$message?.error('下载报告失败，请重试');
      } finally {
        setLoading(false);
      }
    } else {
      window.$message?.warning('报告ID不存在，无法下载');
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center">
          <span>{t('page.ailisting.aiDiagnosisModal.title')}</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={550}
      className="ai-diagnosis-modal"
    >
      <Spin spinning={loading}>
        <div className="mb-4 flex items-center justify-between rounded-lg from-blue-50 to-cyan-50 bg-gradient-to-r p-3">
          <div className="flex items-center">
            <span className="text-gray-700">{t('page.ailisting.aiDiagnosisModal.remainingDiagnosis')}</span>
          </div>
          {/* diagnosisCount 10一下为黄色 5以下为红色 */}
          <Badge
            count={diagnosisCount}
            overflowCount={999}
            color={diagnosisCount < 5 ? '#f5222d' : diagnosisCount < 10 ? '#f5a623' : '#154EC1'}
          />
        </div>

        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            label={
              <div className="flex items-center">
                <span>{t('page.ailisting.aiDiagnosisModal.yourProductAsin')}</span>
              </div>
            }
            name="mainAsin"
            rules={[{ required: true, message: t('page.ailisting.aiDiagnosisModal.enterYourProductAsin') }]}
          >
            <Input
              placeholder={t('page.ailisting.aiDiagnosisModal.enterYourProductAsin')}
              prefix={
                <Icon
                  icon="mdi:amazon"
                  className="text-gray-400"
                />
              }
              onChange={e => {
                const value = e.target.value.replace(/\s/g, '');
                form.setFieldValue('mainAsin', value);
              }}
            />
          </Form.Item>

          <Form.Item
            label={
              <div className="flex items-center">
                <span>{t('page.ailisting.aiDiagnosisModal.competitorAsin')}</span>
              </div>
            }
            name="compareAsin"
            rules={[{ required: true, message: t('page.ailisting.aiDiagnosisModal.enterCompetitorAsin') }]}
          >
            <Input
              placeholder={t('page.ailisting.aiDiagnosisModal.enterCompetitorAsin')}
              prefix={
                <Icon
                  icon="mdi:amazon"
                  className="text-gray-400"
                />
              }
              onChange={e => {
                const value = e.target.value.replace(/\s/g, '');
                form.setFieldValue('compareAsin', value);
              }}
            />
          </Form.Item>

          <Button
            type="primary"
            block
            onClick={handleSubmit}
            loading={submitting}
            className="h-10 bg-#154EC1 text-base font-medium"
            // disabled={diagnosisCount <= 0}
          >
            {t('page.ailisting.aiDiagnosisModal.startDiagnosis')}
          </Button>

          {shouldShowReportStatus() && (
            <div className="mt-6 overflow-hidden border border-gray-100 rounded-lg shadow-sm">
              <div
                className="flex items-center justify-between p-3"
                style={{
                  background:
                    reportInfo.ReportState === 3
                      ? 'linear-gradient(120deg, #fff1f0, #ffccc7)'
                      : reportInfo.ReportState === 2
                        ? 'linear-gradient(120deg, #f6ffed, #d9f7be)'
                        : 'linear-gradient(120deg, #e6f7ff, #bae7ff)',
                  borderBottom: '1px solid rgba(0,0,0,0.06)'
                }}
              >
                <div className="flex items-center">
                  <Icon
                    icon="carbon:document-pdf"
                    className={`text-xl mr-2 ${
                      reportInfo.ReportState === 3
                        ? 'text-red-600'
                        : reportInfo.ReportState === 2
                          ? 'text-green-600'
                          : 'text-blue-600'
                    }`}
                  />
                  <span
                    className={`mr-2 font-medium ${
                      reportInfo.ReportState === 3
                        ? 'text-red-800'
                        : reportInfo.ReportState === 2
                          ? 'text-green-800'
                          : 'text-blue-800'
                    }`}
                  >
                    {t('page.ailisting.aiDiagnosisModal.reportDownload')}
                  </span>
                  {reportInfo.ReportState === 1 && (
                    <Badge
                      status="processing"
                      text={
                        <span className="text-xs text-blue-600">
                          {t('page.ailisting.aiDiagnosisModal.diagnosisInProgress')}
                        </span>
                      }
                    />
                  )}
                  {reportInfo.ReportState === 3 && (
                    <Badge
                      status="error"
                      text={
                        <span className="ml-2 text-sm text-red-500">
                          {t('page.ailisting.aiDiagnosisModal.diagnosisFailed')}
                        </span>
                      }
                    />
                  )}
                </div>
              </div>

              <div className="bg-white">
                <div
                  className={`flex items-center justify-between p-3 cursor-pointer ${
                    reportInfo.ReportState === 3
                      ? ' bg-red-50'
                      : reportInfo.ReportState === 2 && reportInfo.ReportFile
                        ? ' bg-green-50'
                        : ' bg-blue-50'
                  }`}
                  onClick={() => {
                    if (reportInfo.ReportState === 2 && reportInfo.ReportFile) {
                      downloadReport();
                    } else if (reportInfo.ReportState === 3) {
                      handleSubmit(); // 失败时点击重新生成
                    }
                  }}
                  style={{
                    opacity: reportInfo.ReportState === 1 ? 0.7 : 1,
                    transition: 'all 0.3s ease'
                  }}
                >
                  {/* 左侧固定显示PDF图标 */}
                  <div className="flex flex-1 items-center">
                    <Icon
                      icon="vscode-icons:file-type-pdf2"
                      className="mr-3 flex-shrink-0 text-3xl"
                    />

                    {/* 中间显示文件名 */}
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm text-gray-700 font-medium">
                        {`${reportInfo.MainAsin || ''}-${reportInfo.CompareAsin || ''}`}
                      </p>
                    </div>
                  </div>

                  {/* 右侧根据状态显示不同图标 */}
                  <div className="ml-3 flex-shrink-0">
                    {reportInfo.ReportState === 1 && (
                      <div className="h-8 w-8 flex items-center justify-center">
                        <Icon
                          icon="line-md:loading-twotone-loop"
                          className="text-2xl text-blue-500"
                        />
                      </div>
                    )}
                    {reportInfo.ReportState === 2 && (
                      <div className="flex items-center justify-center">
                        {reportInfo.ReportState === 2 && reportInfo.ReportFile && (
                          <p className="mr-1 text-xs text-green-500">
                            {t('page.ailisting.aiDiagnosisModal.clickToDownload')}
                          </p>
                        )}
                        <Icon
                          icon="line-md:cloud-alt-download-filled-loop"
                          className="text-2xl text-green-500"
                        />
                      </div>
                    )}
                    {reportInfo.ReportState === 3 && (
                      <div className="h-8 w-8 flex items-center justify-center">
                        <Icon
                          icon="line-md:close-circle"
                          className="text-2xl text-red-500"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Form>
        <div className="mt-4 text-center text-sm text-gray-500">
          <span>{t('page.ailisting.aiDiagnosisModal.betaVersionNote')}</span>
          <br />
          {reportInfo.ReportState === 3 && (
            <span className="text-red-500">{t('page.ailisting.aiDiagnosisModal.diagnosisFailedNoDeduction')}</span>
          )}
        </div>
      </Spin>
    </Modal>
  );
});

AiDiagnosisModal.displayName = 'AiDiagnosisModal';

export default AiDiagnosisModal;
