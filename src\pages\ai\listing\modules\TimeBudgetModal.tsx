import classNames from 'classnames';
import { Icon } from '@iconify/react';
import { selectUserInfo } from '@/store/slice/auth';
import { getContinentAndCountryName } from '@/utils/useChartData';
import { AuthAsinGetLowBudgets, AuthAsinSetTimeBudget } from '@/service/api';
import { getCurrency, getCurrencySymbol } from '@/components/weekly-vchart/chart';

interface TimeBudgetModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  countryCode: string;
}

interface LowBudgetData {
  StartHour: number;
  EndHour: number;
  MinBudget: string;
  NormalBudget?: string;
}

const TimeBudgetModal: React.FC<TimeBudgetModalProps> = ({
  visible,
  onClose,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onSuccess,
  countryCode
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedHours, setSelectedHours] = useState<number[]>([]);
  const [startHour, setStartHour] = useState<number | null>(null);
  const [budgetData, setBudgetData] = useState<LowBudgetData | null>(null);
  const userInfo = useAppSelector(selectUserInfo);

  // 获取货币代码和符号
  const currencyCode = countryCode ? getCurrency(countryCode) || '' : '';
  const symbol = currencyCode ? getCurrencySymbol(currencyCode) || '' : '';

  // 时间段定义
  const timeRanges = [
    { title: '00:00:00 - 05:59:59', hours: [0, 1, 2, 3, 4, 5] },
    { title: '06:00:00 - 11:59:59', hours: [6, 7, 8, 9, 10, 11] },
    { title: '12:00:00 - 17:59:59', hours: [12, 13, 14, 15, 16, 17] },
    { title: '18:00:00 - 23:59:59', hours: [18, 19, 20, 21, 22, 23] }
  ];

  // 获取分时预算数据
  const fetchBudgetData = useCallback(async () => {
    if (!countryCode) return;

    try {
      setLoading(true);
      const res = await AuthAsinGetLowBudgets({ CountryCode: countryCode });
      if (res.data) {
        // 如果API没有返回正常预算，这里模拟一个正常预算值
        // 实际应用中，应该从API获取真实的正常预算值
        const normalBudget = res.data.MinBudget ? (Number.parseFloat(res.data.MinBudget) * 2).toFixed(2) : '0.00';

        setBudgetData({
          ...res.data,
          NormalBudget: normalBudget
        });

        // 如果有设置过时间，则初始化选中状态
        if (res.data.StartHour !== -1 && res.data.EndHour !== -1) {
          let hoursToSelect: number[] = [];
          const start = res.data.StartHour;
          const end = res.data.EndHour;

          if (end >= start) {
            // 正常时间段
            hoursToSelect = Array.from({ length: end - start + 1 }, (_, i) => start + i);
          } else {
            // 跨天时间段
            hoursToSelect = [
              ...Array.from({ length: 24 - start }, (_, i) => start + i),
              ...Array.from({ length: end + 1 }, (_, i) => i)
            ];
          }

          setSelectedHours(hoursToSelect);
        }
      }
    } catch (error) {
      console.error('获取分时预算数据失败:', error);
      window.$message?.error('获取分时预算数据失败');
    } finally {
      setLoading(false);
    }
  }, [countryCode]);

  // 重置选择
  const handleReset = () => {
    setSelectedHours([]);
    setStartHour(null);
  };

  // 当Modal显示时获取数据
  useEffect(() => {
    if (visible) {
      fetchBudgetData();
    } else {
      handleReset();
    }
  }, [visible, fetchBudgetData]);

  // 处理时间点击
  const handleHourClick = (hour: number) => {
    // 如果点击的是已选中的唯一小时，则取消选择
    if (selectedHours.length === 1 && selectedHours[0] === hour) {
      setSelectedHours([]);
      setStartHour(null);
      return;
    }

    if (startHour === null) {
      // 第一次点击，设置起始时间
      setStartHour(hour);
      setSelectedHours([hour]);
    } else {
      // 第二次点击，设置结束时间并计算中间的所有小时
      let newSelection: number[] = [];

      // 如果点击的是同一个小时，则取消选择
      if (startHour === hour) {
        setStartHour(null);
        setSelectedHours([]);
        return;
      }

      if (hour > startHour) {
        // 正向选择
        newSelection = Array.from({ length: hour - startHour + 1 }, (_, i) => startHour + i);
      } else {
        // 跨天选择 - 不再自动选择所有小时
        // 而是从起始时间到23点，再从0点到结束时间
        newSelection = [
          ...Array.from({ length: 24 - startHour }, (_, i) => startHour + i),
          ...Array.from({ length: hour + 1 }, (_, i) => i)
        ];
      }

      // 检查是否选择了24小时
      if (newSelection.length === 24) {
        window.$message?.warning('不能选择24小时，请选择较短的时间段');
        return;
      }

      setSelectedHours(newSelection);
      setStartHour(null); // 重置起始时间
    }
  };

  // 格式化显示时间
  const formatHour = (hour: number) => {
    return `${hour.toString().padStart(2, '0')}`;
  };

  // 获取已选时间段的描述
  const getSelectedTimeRangesShort = () => {
    if (selectedHours.length === 0) return '';

    const sortedHours = [...selectedHours].sort((a, b) => a - b);

    // 检查是否是连续的时间段
    let isConsecutive = true;
    for (let i = 1; i < sortedHours.length; i += 1) {
      if (sortedHours[i] !== sortedHours[i - 1] + 1) {
        isConsecutive = false;
        break;
      }
    }

    // 如果不是连续的，说明是跨天的时间段
    if (!isConsecutive) {
      // 找出断点
      let breakIndex = 0;
      for (let i = 1; i < sortedHours.length; i += 1) {
        if (sortedHours[i] !== sortedHours[i - 1] + 1) {
          breakIndex = i;
          break;
        }
      }

      const firstPart = sortedHours.slice(0, breakIndex);
      const secondPart = sortedHours.slice(breakIndex);

      if (firstPart.length > 0 && secondPart.length > 0) {
        return `${formatHour(firstPart[0])}:00-${formatHour(firstPart[firstPart.length - 1] + 1)}:00, ${formatHour(secondPart[0])}:00-${formatHour(secondPart[secondPart.length - 1] + 1)}:00`;
      }
    }

    // 单个时间点
    if (sortedHours.length === 1) {
      return `${formatHour(sortedHours[0])}:00-${formatHour(sortedHours[0] + 1)}:00`;
    }

    // 正常连续时间段
    const start = sortedHours[0];
    const end = sortedHours[sortedHours.length - 1];

    // 显示为起始时间-结束时间+1
    return `${formatHour(start)}:00-${formatHour(end + 1)}:00`;
  };

  // 调用API保存时间段设置
  const handleSubmit = useCallback(async () => {
    // if (selectedHours.length === 0) {
    //   window.$message?.error('请选择投放时间段');
    //   return;
    // }

    // 显示确认对话框
    window.$modal?.info({
      title: (
        <div className="mb-2 flex items-center gap-2">
          <span className="text-[18px] font-medium">确认提交</span>
        </div>
      ),
      content: (
        <div className="pb-3 pt-2">
          {/* <div className="bg-[#FAFAFA] rounded-lg p-4 mb-2">
          <div className="flex flex-col gap-3">
            <div className="flex items-center">
              <span className="text-gray-500 w-24">当前时间段：</span>
              <span className="font-medium text-[15px] text-primary">
                {selectedHours.length > 0 ? getSelectedTimeRangesShort() : '未选择'}
              </span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-500 w-24">预算金额：</span>
              <span className="font-medium text-[15px] text-primary">
                {budgetData?.MinBudget ? `${symbol}${budgetData.MinBudget}` : '-'}
              </span>
            </div>
          </div>
        </div> */}
          <div className="y-3 text-base text-gray-700">
            {selectedHours.length > 0
              ? '确认提交当前分时策略？提交后，系统将执行最新的分时预算策略'
              : '当前站点未设置分时预算策略，提交后，系统将使用正常广告预算。'}
          </div>
        </div>
      ),
      width: 480,
      className: 'custom-modal-confirm',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const params = {
            CountryCode: countryCode,
            StartHour: selectedHours.length > 0 ? selectedHours[0] : -1,
            EndHour: selectedHours.length > 0 ? selectedHours[selectedHours.length - 1] : -1
          };

          const res = await AuthAsinSetTimeBudget(params);

          if (res && res.data) {
            window.$message?.success('设置成功');
            onSuccess?.();
            onClose();
          } else {
            window.$message?.error('设置失败');
          }
        } catch (error) {
          console.error('设置时间段失败:', error);
          window.$message?.error('设置失败');
        } finally {
          setLoading(false);
        }
      }
    });
  }, [selectedHours, countryCode, onSuccess, onClose]);

  return (
    <AModal
      title={
        <div className="flex items-center">
          <Icon
            icon="streamline-freehand:presentation-projector-screen-budget-analytics"
            className="mr-1 text-lg"
          />
          最低预算投放时间段设置
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <AButton
          key="cancel"
          onClick={onClose}
        >
          取消
        </AButton>,
        <AButton
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          提交
        </AButton>
      ]}
    >
      <div className="p-4">
        {/* 基本信息 */}
        <div className="mb-4">
          <div className="mb-2 text-base font-medium">基本信息</div>
          <div className="flex items-center gap-8 rounded bg-gray-50 p-3">
            <div className="flex items-center">
              <div className="mr-2 text-sm text-gray-500">店铺：</div>
              <div className="text-sm text-primary">{userInfo?.CompanyName || '-'}</div>
            </div>
            <div className="flex items-center">
              <div className="mr-2 text-sm text-gray-500">站点：</div>
              <div className="text-sm text-primary">{getContinentAndCountryName(countryCode).countryName || '-'}</div>
            </div>
          </div>
        </div>

        {/* 分时设置 */}
        <div className="mb-4">
          <div className="mb-2 text-base font-medium">分时设置</div>
          <div className="mb-2 flex items-center justify-between">
            <div className="mb-2 text-sm text-gray-500">
              请设置预算为：
              <span className="mx-1 text-lg text-red-500 font-medium">
                {loading ? '加载中...' : budgetData?.MinBudget ? `${symbol}${budgetData.MinBudget}` : '-'}
              </span>
              的投放时间段，支持跨天选择（如23:00-05:00），以下为北京时间
            </div>
            <div>
              <AButton
                type="text"
                onClick={handleReset}
              >
                <span className="text-primary">重置</span>
              </AButton>
            </div>
          </div>

          <div className="overflow-hidden border rounded">
            {/* 第一层：时间段标题 */}
            <div className="flex">
              {timeRanges.map((range, index) => (
                <div
                  key={`title-${index}`}
                  className="flex-1 border-r bg-gray-100 px-4 py-2 font-medium last:border-r-0"
                >
                  {range.title}
                </div>
              ))}
            </div>

            {/* 第二层：小时选择 */}
            <div className="flex">
              {timeRanges.map((range, index) => (
                <div
                  key={`hours-${index}`}
                  className="flex flex-1"
                >
                  {range.hours.map(hour => (
                    <div
                      key={hour}
                      className="flex flex-col flex-1"
                    >
                      <div
                        className={classNames(
                          'h-[40px] border-r last:border-r-0 cursor-pointer flex items-center justify-center text-sm transition-colors w-full',
                          {
                            'bg-primary text-white': selectedHours.includes(hour),
                            'hover:bg-gray-50': !selectedHours.includes(hour),
                            'border-primary': startHour === hour
                          }
                        )}
                        onClick={() => handleHourClick(hour)}
                      >
                        {formatHour(hour)}
                      </div>
                      {budgetData && (
                        <div
                          className={classNames('border-r py-1 text-center text-[10px] last:border-r-0', {
                            'bg-blue-50 text-red-500 font-bold': selectedHours.includes(hour),
                            'bg-gray-50 text-gray-500': !selectedHours.includes(hour)
                          })}
                        >
                          {selectedHours.includes(hour) ? `${symbol}${budgetData.MinBudget}` : '正常预算'}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="rounded bg-gray-50 p-4">
          <div className="mb-2 text-base font-medium">使用说明:</div>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>当站点不使用任何分时预算模板或者不在分时时间段内时，默认使用正常广告预算</li>
            <li>广告预算在分时预算时间段内时，默认使用较低预算</li>
            <li>建议不要频繁修改分时预算时间，否则可能造成执行结果不符合预期</li>
          </ul>
        </div>
      </div>
    </AModal>
  );
};

export default TimeBudgetModal;
