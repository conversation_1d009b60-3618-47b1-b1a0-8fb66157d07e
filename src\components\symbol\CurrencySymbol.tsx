import { getCurrencySymbol, getCurrency } from "@/components/weekly-vchart/chart";
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import React from 'react';
import BigNumber from 'bignumber.js';
import { Statistic } from 'antd';

// 定义组件的属性接口
interface CurrencySymbolProps {
  countryCode?: string; // 国家代码，可选
  value: string | number; // 当前值
  oldValue?: string | number; // 旧值，可选
  isShowContrast?: boolean; // 是否显示对比
  reverseColors?: boolean; // 是否反转颜色
  isInteger?: boolean; // 是否为整数
  showOld?: boolean; // 是否显示旧值
  className?: string; // 自定义样式类名
}

const CurrencySymbol: React.FC<CurrencySymbolProps> = ({ className, countryCode, value, oldValue = "no", isShowContrast = true, reverseColors = false, isInteger = false, showOld = false }) => {
  // 获取货币代码和符号
  const currencyCode = countryCode ? getCurrency(countryCode) || '' : '';
  const symbol = currencyCode ? getCurrencySymbol(currencyCode) || '' : '';

  // 判断值是否为百分比
  const isPercentage = (val: string | number) => typeof val === 'string' && val.includes('%');

  // 清理当前值和旧值，去掉百分号并转换为 BigNumber
  const cleanValue = new BigNumber(typeof value === 'string' ? value.replace('%', '') : value);
  const cleanOldValue = new BigNumber(typeof oldValue === 'string' ? oldValue.replace('%', '') : oldValue);

  // 判断当前值和旧值是否相等
  const isEqual = cleanValue.isEqualTo(cleanOldValue);

// 计算差值和百分比变化
const difference = cleanValue.minus(cleanOldValue);
// 修改环比计算逻辑
// const percentageChange = cleanOldValue.isZero() 
//   ? new BigNumber(0) :difference.dividedBy(cleanOldValue).multipliedBy(100)
const percentageChange = cleanOldValue.isZero() 
  ? (cleanValue.isZero() ? new BigNumber(0) : new BigNumber(100)) // 当旧值为0且新值不为0时，增长率为100%
  : difference.dividedBy(cleanOldValue).multipliedBy(100);

  // : isPercentage(value) 
  //   ? difference.dividedBy(cleanOldValue).multipliedBy(100) // 如果是百分比，计算环比
  //   : difference; 

  // 判断是增加还是减少
  const isIncrease = difference.isGreaterThan(0);
  const isDecrease = difference.isLessThan(0);

  // 格式化值，根据是否为整数决定小数位数
  const formatValue = (val: BigNumber) => {
    if (!val.isFinite() || val.isNaN()) return '--';
    return isInteger ? val.toFixed(0) : val.toFixed(2);
  };

  // 格式化百分比
  const formatPercentage = (val: BigNumber) => {
    if (!val.isFinite() || val.isNaN()) return '--';
    return val.toFixed(2);
  };

  // 定义颜色，根据是否反转颜色来设置
  const increaseColor = !reverseColors ? '#154EC1' : '#FF4C4C';
  const decreaseColor = !reverseColors ? '#FF4C4C' : '#154EC1';

  // 格式化后的当前值
  const formattedValue = formatValue(cleanValue);

  return (
    <span className={className}>
      {/* 当前值 */}
      {!cleanValue.isFinite() || cleanValue.isZero() || cleanValue.isNaN() ? '--' : (
      <Statistic
        className="current_value"
        title="" // 标题
        value={isPercentage(value) ? formatPercentage(cleanValue) : (cleanValue.isFinite() ? formattedValue : '--')} // 显示的值
        precision={isInteger ? 0 : 2} // 精度
        valueStyle={{ fontSize: showOld ? '20px' : '15px',whiteSpace: 'nowrap' }}
        prefix={!isPercentage(value) && cleanValue.isFinite() ? symbol : ''} // 前缀
        suffix={isPercentage(value) ? '%' : ''} // 后缀
      />
      )
      }
      {isShowContrast && oldValue !== "no" && value && (
        <div className='text-sm old_all' style={{ marginTop: showOld ? '6px' : '0px' }}>
          {showOld && oldValue && (
            <Statistic
              className="old_value"
              title="" // 旧值标题
              value={isPercentage(oldValue) ? formatPercentage(cleanOldValue) : formatValue(cleanOldValue)} // 旧值
              precision={isInteger ? 0 : 2} // 精度
              valueStyle={{ color: '#6e6f71', fontSize: '15px',marginRight: '5px',fontWeight: '400',whiteSpace: 'nowrap' }} // 旧值样式
              prefix={!isPercentage(oldValue) && cleanOldValue.isFinite() ? symbol : ''} // 前缀
              suffix={isPercentage(oldValue) ? '%' : ''} // 后缀
            />
          )}
           {!isEqual && !difference.isZero() && difference.isFinite() ? (
            <Statistic
              className="change_value"
              title="" // 变化标题
              // value={isPercentage(value) ? formatPercentage(difference.abs()) : formatPercentage(percentageChange.abs())} // 变化值
              value={formatPercentage(percentageChange.abs())}
              precision={2} // 精度
              valueStyle={{ fontSize: '13px', color: isIncrease ? increaseColor : decreaseColor,fontWeight: '400',whiteSpace: 'nowrap' }} // 变化值样式
              prefix={isIncrease ? <ArrowUpOutlined /> : <ArrowDownOutlined />} // 前缀图标
              suffix='%' // 后缀
            />
          ) : '--'}
        </div>
      )}
    </span>
  );
};

export default CurrencySymbol;