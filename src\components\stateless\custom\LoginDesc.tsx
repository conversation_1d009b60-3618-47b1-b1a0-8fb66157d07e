import React from 'react';
const LoginDesc: React.FC = () => {
  const { t } = useTranslation();

  // 定义产品范围数据结构
  const productScopes = [
    {
      title: t('page.login.description.productscope1'),
      description: t('page.login.description.productscope1op')
    },
    {
      title: t('page.login.description.productscope2'),
      description: t('page.login.description.productscope2op')
    },
    {
      title: t('page.login.description.productscope3'),
      description: t('page.login.description.productscope3op')
    }
  ];

  // 定义AI系统原理数据结构
  const systemPrinciples = [
    {
      title: t('page.login.description.systemprinciple1'),
      description: t('page.login.description.systemprinciple1op')
    },
    {
      title: t('page.login.description.systemprinciple2'),
      description: t('page.login.description.systemprinciple2op')
    },
    {
      title: t('page.login.description.systemprinciple3'),
      description: t('page.login.description.systemprinciple3op')
    },
    {
      title: t('page.login.description.systemprinciple4'),
      description: t('page.login.description.systemprinciple4op')
    }
  ];

  // 定义支持的国家数据结构
  const supportedCountries = [
    t('page.login.description.supportcountry1'),
    t('page.login.description.supportcountry2'),
    t('page.login.description.supportcountry3'),
    t('page.login.description.supportcountry4'),
    t('page.login.description.supportcountry5')
  ];

  return (
    <div className="h-screen flex items-center justify-center">
      <div className="max-h-[90vh] overflow-y-auto rounded-xl px-6 text-left tracking-wide backdrop-blur-sm 2xl:px-10">
        {/* 标题部分 */}
        <h1 className="mb-2 text-xl text-primary font-bold leading-tight 2xl:text-2xl">
          {t('page.login.description.title')}
          <div className="ml-2 inline-block text-base text-[#00376A] font-medium 2xl:text-lg">
            {t('page.login.description.description')}
          </div>
        </h1>

        <div className="space-y-2.5">
          {' '}
          {/* 稍微增加一点间距 */}
          {/* 服务价格部分 */}
          <section className="rounded-lg py-1.5">
            {' '}
            {/* 稍微增加内边距 */}
            <h4 className="text-sm text-[#333] font-bold 2xl:text-base">
              1. {t('page.login.description.serviceprice')}
            </h4>
            <p className="ml-1 text-xs text-gray-700 2xl:text-sm">
              <span className="text-primary font-bold">{t('page.login.description.price')}</span>
              <span className="ml-1 text-gray-600">{t('page.login.description.servicepriceunit')}</span>
            </p>
          </section>
          {/* 适用卖家类型部分 */}
          <section className="rounded-lg py-1.5">
            <h4 className="text-sm text-[#333] font-bold 2xl:text-base">
              2. {t('page.login.description.applyseller')}
            </h4>
            <p className="ml-1 text-xs text-gray-700 2xl:text-sm">{t('page.login.description.applysellerdesc')}</p>
          </section>
          {/* 产品适用范围部分 */}
          <section className="rounded-lg py-1.5">
            <h4 className="text-sm text-[#333] font-bold 2xl:text-base">
              3. {t('page.login.description.productscope')}
            </h4>
            <div className="ml-1 text-xs space-y-1.5">
              {productScopes.map((scope, index) => (
                <div
                  key={index}
                  className="mb-1"
                >
                  <p className="text-xs text-primary font-semibold 2xl:text-sm">• {scope.title}</p>
                  <p className="ml-3 text-xs text-gray-600 leading-tight">{scope.description}</p>
                </div>
              ))}
            </div>
          </section>
          {/* AI系统基本原理部分 */}
          <section className="rounded-lg py-1.5">
            <h4 className="text-sm text-[#333] font-bold 2xl:text-base">
              4. {t('page.login.description.systemprinciple')}
            </h4>
            <div className="grid grid-cols-1 ml-1 gap-1.5 text-xs">
              {systemPrinciples.map((principle, index) => (
                <div
                  key={index}
                  className="mb-1"
                >
                  <p className="text-xs text-primary font-semibold 2xl:text-sm">• {principle.title}</p>
                  <p className="ml-3 text-xs text-gray-600 leading-tight">{principle.description}</p>
                </div>
              ))}
            </div>
          </section>
          {/* 支持国家范围部分 */}
          <section className="rounded-lg py-1.5">
            <h4 className="text-sm text-[#333] font-bold 2xl:text-base">
              5. {t('page.login.description.supportcountry')}
            </h4>
            <div className="grid grid-cols-3 ml-1 gap-x-2 text-xs text-gray-700 2xl:text-sm">
              {supportedCountries.map((country, index) => (
                <div
                  key={index}
                  className="leading-tight"
                >
                  • {country}
                </div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default LoginDesc;
