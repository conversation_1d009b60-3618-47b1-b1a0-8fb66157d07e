import { Col, Form, Input, Row, Select, DatePicker } from 'antd';
import type { FormInstance } from 'antd';
import { memo, FC, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { selectUserInfo } from '@/store/slice/auth';

interface Props {
  localSearch: (params: any, isLocalSearch: boolean) => void;
  search: (params: any) => void;
  form: FormInstance;
  loading: boolean;
}

const UserSearch: FC<Props> = memo(({ search, localSearch, form, loading }) => {
  const defaultValues = { manageState: 1, date: dayjs().subtract(1, 'day') };
  const [defaultDate, setDefaultDate] = useState(defaultValues.date);
  const userInfo = useAppSelector(selectUserInfo);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params = {
      UID: userInfo.active_shop_id,
      campaignName: values.campaignName,
      campaignType: values.campaignType || 'DEEPBI',
      state: values.state,
      Date: values.date ? values.date.format('YYYY-MM-DD') : undefined,
    };
    localSearch(params, true);
  };

  useEffect(() => {
    form.setFieldsValue(defaultValues);
    search({
    });
  }, [form]);

  const handleDateChange = (value: any) => {

    // 检查选择的日期是否与当前日期相同
    if (value && dayjs(value).isSame(defaultDate, 'day')) {
      return; // 如果选择的日期与当前相同，则不更新
    }

    setDefaultDate(value);
    search({});
  };

  return (
    <Form form={form} disabled={loading} initialValues={defaultValues}>
      <Row gutter={[16, 16]} wrap>
        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="campaignName" label="广告活动名称">
            <Input placeholder="请输入广告活动名称" allowClear onChange={handleSearch} />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="campaignType" label="活动分类">
            <Select
              placeholder="请选择活动分类"
              defaultValue={'DEEPBI'}
              // allowClear
              options={[
                { label: '全部', value: 'ALL' },
                { label: 'AI广告计划', value: 'DEEPBI' },
                // 和非DEEPBI分类筛选
                { label: '非AI广告计划', value: 'NOT_DEEPBI' },
              ]}
              onChange={handleSearch}
            />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="state" label="活动状态">
            <Select
              placeholder="请选择活动状态"
              defaultValue={''}
              // allowClear
              options={[
                { label: '全部', value: '' },
                { label: '开启', value: 'enabled' },
                { label: '暂停', value: 'paused' },
              ]}
              onChange={handleSearch}
            />
          </Form.Item>
        </Col>

        <Col span={24} md={12} lg={4}>
          <Form.Item className="m-0" name="date" label="日期">
            <DatePicker
              allowClear={false}
              format="YYYY-MM-DD"
              disabledDate={(current: any) => {
                return current && current > dayjs().endOf('day');
              }}
              onChange={handleDateChange}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
});

export default UserSearch;