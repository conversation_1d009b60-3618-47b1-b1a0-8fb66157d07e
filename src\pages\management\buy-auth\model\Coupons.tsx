import { Skeleton, Empty, Collapse } from 'antd';
import { useState, useEffect } from 'react';
import { useLogin } from '@/hooks/common/login';
import { selectUserInfo } from '@/store/slice/auth';
import CouponCard from './CouponCard';
import { haveCoupon } from '@/service/api';
import './coupons.css';
import { AppContext } from './AppContext';
const { Panel } = Collapse;

export default function Coupons() {
  const userInfo = useAppSelector(selectUserInfo);
  const [refreshing, setRefreshing] = useState(false);
  const { toGroupLogin } = useLogin();
  const [coupons, setCoupons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCouponId, setSelectedCouponId] = useState<number | null>(null);
  const { selectedCoupon, setSelectedCoupon, parseFormattedData } = useContext(AppContext)!;


  const fetchCoupons = async () => {
    try {
      const shopData = parseFormattedData();
      const firstKey = Object.keys(shopData)[0];
      const firstValue = shopData[firstKey];
      const res = await haveCoupon({
        ShopID:firstKey,
        CountryCode:firstValue
      });
      if (res && res.data) {
        if (res.data.length > 0) {
          res.data.forEach((item: any, index: number) => {
            item.key = String(index + 1);
          });
          // 排除 CanUse为false的
          const filteredData = res.data.filter((item: any) => item.CanUse);
          setCoupons(filteredData);
          setSelectedCouponId(filteredData[0].ID); // Select the first coupon by default
          setSelectedCoupon(filteredData[0]);
        }else{
          setSelectedCoupon({});
        }
      }
    } catch (error) {
      console.error('Failed to fetch coupons:', error);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  const handleSelectCoupon = (id: number) => {
    const selected = id === selectedCouponId ? null : id;
    setSelectedCouponId(selected);
    
    const coupon = coupons.find((coupon: any) => coupon.ID === selected);
    setSelectedCoupon(coupon || null);
  
    // console.log(coupon, 'Selected Coupon');
  };

  const panelHeader = coupons.length > 0
    ? `已选择「${coupons?.length}」张优惠券`
    : `没有优惠券`;

  return (
    <div className="mb-6 coupons-container">
      <Collapse defaultActiveKey={['1']} bordered={false} style={{ padding: '0 !important' }}>
        <Panel header={
          <div className="flex items-center">
            <h3 className="text-base font-500 text-lg flex items-center">
              <SvgIcon localIcon="coupons" className="w-8 h-8 mr-2" />
              优惠券
            </h3>
            <p className="text-sm text-[#2f77f1] ml-4">{

              // 对象的长度 而不是键的长度
              <p className="text-sm text-[#2f77f1] ml-4">
              {selectedCoupon && Object.keys(selectedCoupon).length > 0 
                ? `已选择 「1」 张优惠券` 
                : `未选择优惠券`}
            </p>
            
            }</p>
          </div>
        } key="1">
          <div className="flex flex-wrap gap-4">
            {loading ? (
              <Skeleton active />
            ) : coupons.length > 0 ? (
              coupons.map((coupon) => (
                <CouponCard
                  key={coupon.ID}
                  data={{
                    id: coupon.ID,
                    couponDescription: coupon.CouponName,
                    validDate: coupon.ExpireDatetime,
                    CommonDenominator:parseFloat(coupon.CommonDenominator),
                    number: 1, // Assuming each coupon is a single item
                    amount: parseFloat(coupon.CouponAmount),
                    unit: '元',
                    couponInfo: coupon.CouponInfo,
                    couponLeftTimes: coupon.CouponLeftTimes
                  }}
                  valid={selectedCouponId === coupon.ID}
                  onSelect={() => handleSelectCoupon(coupon.ID)}
                />
              ))
            ) : (
              <Empty description="暂无优惠券" className='mx-auto' />
            )}
          </div>
          <div className="mt-2 text-gray-600 text-sm">
            <ul>
              <li>优惠券请在限期内使用，过期后将不可恢复。</li>
              <li>若订单使用了优惠券，但未支付成功，系统将会返还此券。</li>
              <li>优惠券使用最终解释权归DeepID所有。</li>
            </ul>
          </div>
        </Panel>
      </Collapse>
    </div>
  );
}