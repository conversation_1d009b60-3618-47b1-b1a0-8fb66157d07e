import { request } from '../request';

// 申请发票
//   POST /user/invoice_apply
export const invoiceApply = (data: any) => request({ url: "/user/invoice_apply", method: "post", data })

// 修改发票信息
//   POST /user/invoice_config
export const invoiceConfig = (data: any) => request({ url: "/user/invoice_config", method: "post", data })

// 获取已经有的发票信息
//   GET /user/invoice_config
export const getinvoiceInfo = () => request({ url: "/user/invoice_config", method: "get"})

// 历史开发票申请列表
//   POST /user/invoice_list
export const invoiceList = (data: any) => request({ url: "/user/invoice_list", method: "post", data })

// 下载发票
//   GET /user/invoice_download/<int:invoice_id>
export const invoiceDownload = (invoice_id: number) => request({ url: `/user/invoice_download/${invoice_id}`, method: "get"})
