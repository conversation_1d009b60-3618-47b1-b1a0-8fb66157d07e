import type { FC } from 'react';
import { FloatButton } from 'antd';
import { BookOutlined, CloseOutlined } from '@ant-design/icons';
import './style.css';
import { Icon } from '@iconify/react';
import { useLocation } from 'react-router-dom'; // 导入 useLocation

const GlobalFloat: FC = () => {
  const location = useLocation();
  const isLoginPage = location.pathname.includes('/login');
  const [showQRCode, setShowQRCode] = useState(false); // 初始值设为 false

  // 当路由变化时更新二维码显示状态
  // useEffect(() => {
  //   setShowQRCode(isLoginPage); // 登录页时自动显示，其他页面默认隐藏
  // }, [isLoginPage]);
  return (
    // 非登录页直接display:none
    <div className={`float-wrapper ${showQRCode ? 'show' : ''}`}>
      <FloatButton.Group
        shape="circle"
        style={{ insetInlineEnd: 24 }}
      >
        <div className="wechat-container">
          <FloatButton
            icon={
              <Icon
                icon="tdesign:service-filled"
                className="text-primary"
              />
            }
            onClick={() => setShowQRCode(true)} // 点击服务图标时显示二维码
          />
          {/* 二维码悬浮层 */}
          <div className={`qrcode-popup ${showQRCode ? 'visible' : 'hidden'}`}>
            <div className="qrcode-popup-content relative">
              <CloseOutlined
                className="absolute right-2 top-2 cursor-pointer text-white hover:text-gray-200"
                onClick={e => {
                  e.stopPropagation();
                  setShowQRCode(false);
                }}
              />
              <p className="qrcode-header">扫码添加产品顾问</p>
              <AImage
                src="https://www.deepbi.cn/kefu.png"
                alt="微信客服"
                className="qrcode-image"
              />
              <p className="scan-tip">👆 立即扫码</p>
              <p className="flex items-center justify-center pb-2 text-#f5222d font-500">
                <SvgIcon
                  localIcon="couponnew"
                  className="mr-1 h-6 w-6"
                />
                领取2490元优惠券
              </p>
            </div>
          </div>
        </div>
        <FloatButton
          tooltip="说明文档"
          onClick={() => {
            window.open('https://deepthought.feishu.cn/docx/LCa4dVdumo6XbVxwYzocponcnvb', '_blank');
          }}
          icon={<BookOutlined className="text-primary" />}
        />
      </FloatButton.Group>
    </div>
  );
};

export default GlobalFloat;
