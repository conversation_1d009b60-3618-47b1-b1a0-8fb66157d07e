import React, { useState } from "react";
import { Form, Modal, Button, Input, Radio } from 'antd';

interface AddShopProps {
    visible: boolean;
    onCancel: () => void;
    onOk: (values: any) => void;
    initialValues?: {  // 新增初始值参数，用于编辑时
        ShopName?: string;
        IsDistributor?: number;
        type?: 'add' | 'edit';
    };
}

export default function AddShop({ visible, onCancel, onOk, initialValues={} }: AddShopProps) {
    const { t } = useTranslation();
    const [clickCount, setClickCount] = useState(0);
    const [showSupplier, setShowSupplier] = useState(false);
    // const [defaultValues, setDefaultValues] = useState(initialValues);
    const [form] = Form.useForm();

    const handleTitleClick = () => {
        return
        setClickCount(prev => {
            const newCount = prev + 1;
            if (newCount === 5) {
                setShowSupplier(true);
                return 0;
            }
            return newCount;
        });
    };

    const handleCancel = () => {
        setClickCount(0);
        setShowSupplier(false);
        form.resetFields();
        onCancel();
    };

    const handleSubmit = (values: any) => {
        // console.log(values);
        // return;
        onOk({
            ...initialValues,
            ShopName: values.ShopName,
            IsDistributor: values.IsDistributor || 0,
        });
    };

    // 当 visible 或 initialValues 变化时重置表单
    useEffect(() => {
        if (visible) {
            console.log(initialValues);
            form.setFieldsValue(initialValues);
        }
    }, [visible, initialValues]);

    return (
        <Modal
            title={<div onClick={handleTitleClick} style={{ cursor: 'default' }}>
                {initialValues.type === 'add' ? t('page.setting.auth.addshop') : t('page.setting.auth.editshop')}
            </div>}
            open={visible}
            onCancel={handleCancel}
            footer={null}
            maskClosable={false}
            centered
            width={600}
            destroyOnClose
        >
              {/* {initialValues.type === 'add' && (
                    <div className="text-sm my-4 text-[#2c2f45] bg-[#d0d8ff] px-4 py-2 rounded-md">
                        {t('page.setting.auth.addshopdesc')}
                    </div>
                )} */}
            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={initialValues}
            >
                <Form.Item
                    label={t('page.table.columns.shopname')}
                    name="ShopName"
                    rules={[{ required: true, message: t('page.table.columns.shopname') }]}
                >
                    <Input 
                        style={{ width: '100%' }}
                        placeholder={t('page.table.columns.shopname')}
                    />
                </Form.Item>

                {initialValues.type === 'add' && (
                    <Form.Item
                        label={t('page.setting.auth.isdistributor')}
                        name="IsDistributor"
                    >
                        <Radio.Group>
                            <Radio value={0}>{t('page.setting.auth.no')}</Radio>
                            <Radio value={1}>{t('page.setting.auth.yes')}</Radio>
                        </Radio.Group> 
                    </Form.Item>
                )} 
              

                <Form.Item className="flex justify-end">
                    <Button type="primary" htmlType="submit" className="mt-4 p-4 bg-#154EC1">
                    {initialValues.type === 'add' ? t('common.add') : t('common.update')}
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
}