import Clipboard from 'clipboard';
import { DesktopOutlined, InfoCircleFilled, LinkOutlined } from '@ant-design/icons';

const MobileDetector = () => {
  const [isMobile, setIsMobile] = useState(false);
  const { t } = useTranslation();
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice =
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
        window.innerWidth <= 768;

      // window.$message?.info(`检测到设备: ${isMobileDevice ? '移动设备' : 'PC'}`);
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleCopy = () => {
    const clipboard = new Clipboard('.copy-btn', {
      text: () => window.location.href
    });

    clipboard.on('success', () => {
      window.$message?.success(t('page.mobileDetector.copySuccess'));
      clipboard.destroy(); // 使用完立即销毁
    });

    clipboard.on('error', () => {
      window.$message?.error(t('page.mobileDetector.copyFailed'));
      clipboard.destroy(); // 使用完立即销毁
    });
  };

  return (
    <AModal
      title={
        <div className="flex items-center gap-2 text-primary">
          <InfoCircleFilled />
          <span>{t('page.mobileDetector.title')}</span>
        </div>
      }
      open={isMobile}
      onCancel={() => {
        setIsMobile(false);
      }}
      // closable={false}
      // maskClosable={false}
      footer={[
        <AButton
          key="copy-btn"
          className="copy-btn"
          type="primary"
          onClick={handleCopy}
          icon={<LinkOutlined />}
        >
          {t('page.mobileDetector.copy')}
        </AButton>
      ]}
      centered
    >
      <div className="flex flex-col items-center gap-4 py-4">
        <DesktopOutlined className="text-5xl text-primary" />
        <p className="mb-0 text-center">
          {t('page.mobileDetector.description')}
          <br />
          <span className="text-sm text-gray-400">{t('page.mobileDetector.description1')}</span>
        </p>
      </div>
    </AModal>
  );
};

export default MobileDetector;
