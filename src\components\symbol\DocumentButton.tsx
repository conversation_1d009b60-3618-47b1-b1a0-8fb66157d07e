import React from 'react';
import { Button } from 'antd';
import { Icon } from '@iconify/react';

export interface DocumentButtonProps {
  /** 按钮文本 */
  text: string;
  /** 点击时打开的链接 */
  link: string;
  /** Iconify 图标名称 */
  icon?: string;
  /** 额外的类名 */
  className?: string;
}

/**
 * 文档按钮组件 - 用于文档或说明链接
 *
 * 提供美观的渐变样式和悬停动画效果
 */
const DocumentButton: React.FC<DocumentButtonProps> = ({
  text,
  link,
  icon = 'flat-color-icons:idea',
  className = ''
}) => {
  const handleClick = () => {
    window.open(link, '_blank');
  };

  return (
    <div
      className={`${className} cursor-pointer text-sm`}
      onClick={handleClick}
    >
      <div className="group flex transform items-center gap-1 border border-blue-100 rounded-md from-blue-50 to-purple-50 bg-gradient-to-r px-2 py-2 transition-all duration-300 hover:scale-105 hover:text-blue-500 hover:shadow-md">
        <Icon
          icon={icon}
          className="mt-[-4px] text-xl"
        />
        <span className="text-primary font-medium">{text}</span>
      </div>
    </div>
  );
};

export default DocumentButton;
