const { Panel } = ACollapse;
import { InfoCircleOutlined } from '@ant-design/icons';
const ServiceDescription: React.FC = () => {
    const { t } = useTranslation();
    return (
        <ACollapse className="mb-4" bordered={true}
        // defaultActiveKey={['1']}
        >
            <Panel header={
                <div className="flex items-center justify-between">
                    <h4 className="font-bold text-primary">{t('page.setting.auth.serviceDescription.title')}</h4>
                    {/* <p className="text-yellow-600">
                        <InfoCircleOutlined className='mr-1' />
                        广告服务激活后，站点将在3到5分钟内刷新并激活。
                    </p> */}
                </div>
                } key="1">
                <ul className="list-disc pl-6 space-y-4 text-gray-700">
                    <li>{t('page.setting.auth.serviceDescription.description1')}</li>
                    <li>{t('page.setting.auth.serviceDescription.description2')}
                        <p>{t('page.setting.auth.serviceDescription.description3')}</p>
                        <p>{t('page.setting.auth.serviceDescription.description4')}</p>
                    </li>
                    
                    <li>{t('page.setting.auth.serviceDescription.description5')}</li>
                    <li>{t('page.setting.auth.serviceDescription.description6')}</li>
                    <li>{t('page.setting.auth.serviceDescription.description7')}</li>
                </ul>
            </Panel>
        </ACollapse>
    );
};

export default ServiceDescription;