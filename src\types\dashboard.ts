export interface DashboardSummaryResponse {
  code: number;
  data: {
    data: {
      sum: SummaryData[];
      sum_old: SummaryData[];
      daily_data: DailyData[];
      com_keyword_create_num: KeywordData[];
      com_keyword_create_num_old: KeywordData[];
      keyword_create_num: KeywordData[];
      keyword_create_num_old: KeywordData[];
      ai_op_num: AIOpData[];
      ai_op_num_old: AIOpData[];
    };
  };
  msg: string;
}

export interface SummaryData {
  国家: string;
  总销售额: string;
  广告总销量: string;
  广告总花费: string;
  TACOS: string;
  广告总ACOS: string;
  销售数量: string;
  总订单量: string;
  自然销售额: string;
  自然销售额比例: string;
  SP总销售额: string;
  SP总花费: string;
  Deep_SP销售额: string;
  Deep_SP花费: string;
  非Deep_SP销售额: string;
  非Deep_SP花费: string;
  SP_ACOS: string;
  Deep_SP_ACOS: string;
  非Deep_SP_ACOS: string;
  Deep_SP销量: string;
  Deep_manual_SP销量: string;
  Deep_auto_SP销量: string;
  Deep_asin_SP销量: string;
  Deep_manual_SP销售额: string;
  Deep_auto_SP销售额: string;
  Deep_asin_SP销售额: string;
  [key: string]: string;
}

export interface DailyData {
  date: string;
  总订单量: string;
  总销售额: string;
  广告总销量: string;
  自然销售额: string;
  自然销售额比例: string;
  广告总花费: string;
  ACOS: string;
  TACOS: string;
  [key: string]: string;
}

export interface KeywordData {
  keyword_create_num?: string;
  com_keyword_create_num?: string;
  country_code: string;
}

export interface AIOpData {
  ai_op_num: string;
}

export interface MetricCard {
  id: string;
  title: string;
  value: string;
  prevValue?: string;
  change?: string;
  changeType?: 'increase' | 'decrease' | 'neutral';
  unit?: string;
  selected: boolean;
  chartType?: string;
  hideChange?: boolean;
  thirdLineTitle?: any[];
  thirdLineValue?: string;
  thirdLineUnit?: string;
}

export interface ChartData {
  id: string;
  title: string;
  data: any;
  type: string;
  selected: boolean;
}

export interface DashboardLogResponse {
  code: number;
  data: {
    data: {
      amazon_targeting_create_num: LogData[];
      amazon_targeting_create_num_old: LogData[];
      keyword_create_num: LogData[];
      keyword_create_num_old: LogData[];
      ai_op_num: LogData[];
      ai_op_num_old: LogData[];
    };
  };
  msg: string;
}

export interface LogData {
  ai_op_num: string;
  country_code: string;
}

export interface DashboardDailyLogResponse {
  code: number;
  data: {
    data: {
      amazon_campaign_create: Record<string, number>;
      amazon_campaign_update: Record<string, number>;
      amazon_adgroups_create: Record<string, number>;
      amazon_product_create: Record<string, number>;
      amazon_keyword_create: Record<string, number>;
      amazon_targeting_create: Record<string, number>;
      amazon_keyword_update: Record<string, number>;
      amazon_product_update: Record<string, number>;
      amazon_targeting_update: Record<string, number>;
      ai_op_num: Record<string, number>;
    };
  };
  msg: string;
}
