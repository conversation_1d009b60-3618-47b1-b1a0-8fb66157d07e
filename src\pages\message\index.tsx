import type { DropdownProps } from 'antd';
import UserSearch from './modules/UserSearch';
import { getMessage, setMessageRead } from '@/service/api';
import { decreaseUnreadCount,setUnreadCount } from '@/store/slice/message';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {
    const { t } = useTranslation();
    const unreadCount = useAppSelector(state => state.message.unreadCount);

    const { tableWrapperRef, scrollConfig } = useTableScroll();
    const dispatch = useAppDispatch();

    // 批量操作弹窗
    const [open, setOpen] = useState(false);

    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: getMessage,
            apiParams: {
                page: 1,
                pagesize: 20,
                where: {
                    // "BalanceType": "INCO",
                    is_read: 0
                }
            },
            immediate: false,
            columns: () => [
                {
                    key: 'ID',
                    dataIndex: 'ID',
                    title: 'ID',
                    align: 'center',
                    checked: true,
                    hidden: true,
                },
                {
                    key: 'Title',
                    dataIndex: 'Title',
                    title: '内容',
                    align: 'center',
                    checked: true,
                    render: (text: string, record: any) => {
                        return (
                            <div className='flex items-center justify-between relative pl-1'>
                                <div className='flex flex-col flex-start'>
                                    <p className='text-base font-500 text-left mb-2'>{text}</p>
                                    <p className='text-sm text-gray-700 text-left mb-2 max-w-[900px]'   dangerouslySetInnerHTML={{ __html: record.Content }}></p>
                                    {record.IsRead === 0 && (
                                        <AButton className='w-90px' color="primary" variant="outlined" size="small"  onClick={() => ChangeInfo([record.ID])}>
                                            标记为已读
                                        </AButton>
                                    )}
                                </div>
                                <span className='text-xs text-gray-500'>{record.Adddatetime}</span>
                                {record.IsRead === 0 && (
                                    <span className='absolute top-0 left-[-8px] w-2 h-2 bg-red-500 rounded-full'></span>
                                )}
                            </div>
                        );
                    }
                }

                // {
                //     key: 'CompanyName',
                //     dataIndex: 'CompanyName',
                //     title: '公司名称',
                //     align: 'center',
                //     //   width: 120,
                //     checked: true,
                //     // render: (text: string) => {
                //     //     const InvoiceInfo = JSON.parse(text);
                //     //     return InvoiceInfo?.CompanyName || '-';
                //     // }
                // },
                // {
                //     key: 'Content',
                //     dataIndex: 'Content',
                //     title: '内容',
                //     align: 'center',
                //     //   width: 120,
                //     checked: true,
                // },
                // {
                //     key: 'Adddatetime',
                //     dataIndex: 'Adddatetime',
                //     title: '添加时间',
                //     align: 'center',
                //     //   width: 120,
                //     checked: true,
                // },
                // {
                //     key: 'operate',
                //     dataIndex: 'operate',
                //     title: '操作',
                //     align: 'center',
                //     // width: 150,
                //     checked: true,
                //     render: (text: string, record: any) => {
                //         return (
                //            <div className='flex items-center justify-center gap-16px'>
                //            {record.IsRead === 0 && <AButton type='link'  onClick={() => ChangeInfo([record.ID])}>标为已读</AButton>}
                //            </div>
                //         )
                //     }
                // }
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData,
        clearCheckedRowKeys
    } = useTableOperate(data, run);


    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }
    const getTableData = async () => {
        // console.log(tableProps, "tableProps====")
        // console.log(params, "params====")
        // return
        // run(params);
        const formValues = form.getFieldsValue();
        console.log(formValues, "formValues====")
        const searchParams = {
            where: {
                ...(formValues.is_read ? { is_read: 0 } : {})
            },
        };
        handleRun({
            page: 1,
            pagesize: 20,
            ...searchParams,
            CountryCode: "CN"
        });
    }

    // 修改用户信息
    const ChangeInfo = async (IDS: any) => {
        const res = await setMessageRead({ ID: IDS })
        if (res && res.data) {
            window?.$message?.success('标为已读成功')
            reset()
            clearCheckedRowKeys()
            const total = unreadCount - IDS.length;
            dispatch(decreaseUnreadCount(total??0));
            // dispatch(decreaseUnreadCount(tableProps?.pagination.total || 0));
        }
    }


    // useEffect(() => {
    //     console.log(tableProps,"tableProps=====");
    //     console.log(form.getFieldsValue().is_read,"form.getFieldsValue().is_read=====");
    //     if(form.getFieldsValue().is_read){
           
    //     }
    // }, [tableProps.dataSource]);



    return (
        <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">

            <ACard>
                <div className='flex items-center'>
                    <AButton variant="outlined" color="primary" disabled={!form.getFieldsValue().is_read} className='mr-2' onClick={() => {
                        if (checkedRowKeys.length > 0) {
                            ChangeInfo(checkedRowKeys)
                        } else {
                            window?.$message?.warning('请先选择要标为已读的消息')
                        }
                    }}>
                        标为已读
                    </AButton>

                    <div className='flex-1'>
                        <UserSearch

                            search={getTableData}
                            reset={reset}
                            form={form}
                            loading={tableProps.loading}
                        />
                    </div>
                </div>
            </ACard>

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    rowSelection={rowSelection}
                    scroll={{
                        x:scrollConfig.x,
                        y:scrollConfig.y + 50
                    }}
                    size="small"
                    {...tableProps}
                // loading={loading}
                // tableLayout="auto"
                //   dataSource={formattedData}
                />
            </ACard>
        </div>
    );
}
