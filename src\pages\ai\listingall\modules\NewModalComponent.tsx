import React, { useState, useRef, useEffect } from 'react';
import { Modal } from 'antd';
import { RequireReport, GetReportState } from "@/service/api";
import { Icon } from '@iconify/react';
interface NewAntdModalComponentProps {
    visible: boolean;
    closeDrawer: () => void;
    form: any;
    onRefresh: () => void;
    tableloading: boolean
}

interface AsinData {
    Asin: string;
    PreAcos: number;
    AuthMaxMoney: number;
}

const NewAntdModalComponent: React.FC<NewAntdModalComponentProps> = ({ visible, closeDrawer, form, onRefresh, tableloading }) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState<boolean>(false);
    const [reportInfo, setReportInfo] = useState<any>({})
    const handleOk = () => {
        console.log(tableloading, "tableloading====")
        if (tableloading) return
        setLoading(true)
        if (reportInfo.left_times == 0) {
            window.$message?.warning("当前站点诊断次数不足，如需诊断请联系销售~")
            setLoading(false)
            return
        }
        // reportInfo,report_status 对象的length 并且 ReportState为 0或者1 提示
        if (Object.keys(reportInfo.report_status).length > 0) {
            if (reportInfo.report_status.ReportState === '0' || reportInfo.report_status.ReportState === '1') {
                window.$message?.warning("当前站点诊断报告正在生成中，您可前往AI诊断报告页面查看")
                setLoading(false)
                return
            }

            // console.log(reportInfo.report_status, "reportInfo.report_status====")

        }
        RequireReport({ CountryCode: form.getFieldValue('country'), AreaCode: form.getFieldValue('ContinentCode') }).then(res => {
            console.log(res)
            if (res && res.data) {
                window.$message?.success("创建诊断任务成功,请前往AI诊断报告页面查看")
                closeDrawer()
            }
            setLoading(false)
        }).catch(() => {
            setLoading(false)
        })
    }


    useEffect(() => {
        if (!visible) return
        GetReportState({ CountryCode: form.getFieldValue('country') }).then(res => {
            console.log(res)
            if (res && res.data) {
                setReportInfo(res.data)
            }
        })
    }, [visible])

    return (
        <Modal
            title={`AI广告诊断`}
            centered
            maskClosable={false}
            open={visible}
            onCancel={() => { if (!loading) { closeDrawer(); } }}
            width={800}
            onOk={handleOk}
        >
            <div className='flex flex-col gap-4 p-4'>
                <p className='text-lg font-bold flex items-center'>当前诊断站点：

                    <Icon
                        className="mr-2"
                        icon={`circle-flags:${form.getFieldValue('country')?.toLowerCase()}`}
                        width={30}
                        height={30}
                    />
                    {form.getFieldValue('country')}
                </p>
                <p className='text-lg font-bold flex items-center'>
                    本月剩余诊断次数：<span className='text-red-500'>{reportInfo.left_times}</span>
                </p>
            </div>


            <div className="p-4 bg-blue-100 rounded-md text-blue-900">
                此操作将对所选站点的所有Listing进行广告诊断，需要等待5-10分钟。
                <br />
                诊断过程可能耗费一定时间，请勿频繁探作。如诊断完成后需查看详细数据，请前往站点诊断记录页面
            </div>
        </Modal>
    );
};

export default NewAntdModalComponent;