import { mergeChartData } from "./useChartData";
import { DailyData,getAsinStoreSellDays,getAsinWeekStoreQuantity  } from "@/service/api";

export const fetchChartData = async (
  selectedCountry: string[],
  selectedDay: string,
  DataTypeList: any[],
  chartCache: any,
  handleChartData: any,
  dispatch: any,
  setChartCache: any,
  page: string,
) => {  

  try {
    console.log(chartCache,"chartCache=====") 
    const results = await Promise.all(
      DataTypeList.map(async (item) => {
        console.log(item, "item");
        const dataForItem = await Promise.all(
          selectedCountry.map(async (country) => {
            const cacheKey = `${country}-${item.name}-${selectedDay}`;
            console.log(cacheKey, "cacheKey");
            const cachedData = chartCache[cacheKey];
            console.log(cachedData,"cachedData")
            if (item.multiple) {
              console.log(item.name, "item.name");
              let currentList: any[] = [];
             

              if (cachedData) {
                console.log(cachedData.data,"cachedData.data")
                return currentList.map((orderItem) => ({
                  ...orderItem,
                  loading: false,
                  chart: handleChartData(orderItem, cachedData.data, country),
                }));
              }else{
                const req_data = {
                  where: {
                    ...(page === "inventory"
                      ? { ContinentCode: country }
                      : { CountryCode: country }),
                    StartDate: item.StartDate,
                    ...(item.EndDate ? { EndDate: item.EndDate } : {}),
                    DataType: item.name,
                  },
                  order: {
                    StartDate: "asc",
                  },
                  limit: 1000,
                };
                const res = await DailyData(req_data);
                // console.log(res, "res++++++++++++");
                dispatch(setChartCache({ key: cacheKey, data: res.data.data }));
                return currentList.map((orderItem) => ({
                  ...orderItem,
                  loading: false,
                  chart: handleChartData(orderItem, res.data.data, country),
                }));
              }
            } else {
              // console.log(item.name, "item.name");
              if (cachedData) {
                const newData = {
                  ...item,
                  loading: false,
                  chart: handleChartData(item, cachedData.data, country),
                };
                return newData;
              } else if(item.name == "W-FBA"){
                console.log(item.name, "item.name");
                const res =await getAsinStoreSellDays({
                  ContinentCode: country,
                })
                const chartData = res.data.data.length > 0 ? [
                  {
                    ShowData: JSON.stringify(res.data.data)
                  }
                ] : []
                const newData = {
                  ...item,
                  loading: false,
                  chart: handleChartData(item, chartData, country),
                };
                dispatch(setChartCache({ key: cacheKey, data: chartData }));
                return newData;
              }else if(item.name == "W-KL"){
                console.log(item.name, "item.name");
                const res =await getAsinWeekStoreQuantity({
                  ContinentCode: country,
                })
                const chartData = res.data.data.length > 0 ? [
                  {
                    ShowData: JSON.stringify(res.data.data.reverse())
                  }
                ] : []
                const newData = {
                  ...item,
                  loading: false,
                  chart: handleChartData(item, chartData, country),
                };
                dispatch(setChartCache({ key: cacheKey, data: chartData }));
                return newData;
              }else {
                const req_data = {
                  where: {
                    ...(page === "inventory"
                      ? { ContinentCode: country }
                      : { CountryCode: country }),
                    StartDate: item.StartDate,
                    ...(item.EndDate ? { EndDate: item.EndDate } : {}),
                    DataType: item.name,
                  },
                  order: {
                    StartDate: "asc",
                  },
                  limit: 1000,
                };
                const res = await DailyData(req_data);
                const newData = {
                  ...item,
                  loading: false,
                  chart: handleChartData(item, res.data.data, country),
                };
                dispatch(setChartCache({ key: cacheKey, data: res.data.data }));
                return newData;
              }
            }
          }),
        );
        // console.log(dataForItem, "dataForItem");
        return dataForItem.flat();
      }),
    );
    // console.log(results, "results");
    return mergeChartData(results, selectedCountry);
  } catch (error) {
    console.error("数据获取失败", error);
    throw error;
  }
};

export const fetchSmallTableData = async (
  selectedCountry: string[],
  selectedDays: string[],
  selectedContinentCode: string,
  DataTypeList: any[],
  chartCache: any,
  setChartCache: (key: string, data: any) => void,
) => {
  const fetchAllData = DataTypeList.map((item) => {
    return Promise.all(
      selectedCountry.map(async (country) => {
        const cacheKey = `${country}-${item.name}-${selectedDays}`;
        const cachedData = chartCache[cacheKey];
        // console.log(cacheKey, cachedData, "cachedData");
        if (cachedData) {
          return cachedData.data;
        }

        const req_data = {
          where: {
            // ContinentCode: selectedContinentCode,
            CountryCode: country,
            StartDate: item.StartDate,
            EndDate: item.EndDate,
            DataType: item.name,
          },
          order: {
            StartDate: "asc",
          },
          limit: 1000,
        };
        const res = await DailyData(req_data);
        if (res && res.data.data.length > 0) {
          const data = res.data.data.map((item) => ({
            ...item,
            showTableData: JSON.parse(item.ShowData),
          }));
          setChartCache(cacheKey, data);
          return data;
        } else {  
          console.log("没有数据");
          return [];
        }
      }),
    );
  });
  // console.log(fetchAllData, "fetchAllData");
  const results = await Promise.all(fetchAllData);
  // console.log(results, "results");
  // return mergeChartData(results[0].flat());
  return results[0].flat();
};
