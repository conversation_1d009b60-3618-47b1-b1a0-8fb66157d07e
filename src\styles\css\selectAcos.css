.select-acos{
    margin-right: 0 !important;
}
.select-acos .ant-select-selector {
    padding-right: 0 !important;
    /* border: none !important; */
    border-top: 0 !important;
    border-bottom: 0 !important;
    border-right: 0 !important;
    /* 去除右上右下圆角 */
    /* border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important; */
    /* 去除左上左下圆角 */
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}
.select-acos .ant-select-selector .ant-select-selection-item {
    padding-right: 0 !important;
}

.select-acos .ant-select-selector .ant-select-selection-placeholder {
    padding-right: 0 !important;
}
.select-acos  .ant-select-arrow {
    right: 0px !important;
}













.lastRowHigh {
    /* background-color: #ddecf8; */
    font-weight: bold;
    /* color: #2870a9; */
    position: sticky;
    bottom: 0;
    z-index: 8;
    overflow: auto;
    .ant-table-cell {
        background-color: #ddecf8;
    }
    >.ant-table-cell-row-hover {
        background-color: #ddecf8;
    }
}

.ListingDashboard .current_value {
    font-size: 20px;
}

.ListingDashboard .old_all {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.ListingDashboard .old_value {
    font-size: 12px;
}
