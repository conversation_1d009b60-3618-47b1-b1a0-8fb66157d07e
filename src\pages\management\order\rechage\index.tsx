import type { DropdownProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
import UserSearch from './modules/UserSearch';
import { rechageBlanceList, continuePay } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};
const balanceTypeMap: Record<string, string> = {
    INCO: '充值',
    CONS: '消费',
    RETU: '退款',
};

const payTypeMap: Record<string, string> = {
    ALIP: '支付宝',
    WECH: '微信支付',
    PUBA: '对公转账',
    FREE: '赠送',
};

export function Component() {
    const { t } = useTranslation();

    const userInfo = useAppSelector(selectUserInfo);

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const [loading, setLoading] = useState(true);

    const nav = useNavigate();
    // 批量操作弹窗
    const [open, setOpen] = useState(false);

    const [tableData, setTableData] = useState<any[]>([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [jumpUrl, setJumpUrl] = useState('');


    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: rechageBlanceList,
            apiParams: {
                page: 1,
                pagesize: 20,
                where: {
                    "BalanceType": "INCO",
                }
            },
            immediate: false,
            columns: () => [
                {
                    key: 'ID',
                    dataIndex: 'ID',
                    title: '序号',
                    align: 'center',
                    // checked: true,
                    hidden: true,
                },
                {
                    key: 'OrderNum',
                    dataIndex: 'OrderNum',
                    title: '充值编号',
                    align: 'center',
                    width: 150,
                    checked: true,
                },
                // {
                //     key: 'OutOrderNum',
                //     dataIndex: 'OutOrderNum',
                //     title: '关联订单编号',
                //     align: 'center',
                //     //   width: 200,
                //     ellipsis: {
                //         showTitle: false,
                //     },
                //     checked: true,
                //     render: (text: string) => <Tooltip placement="topLeft" title={text}>{text}</Tooltip>,
                // },
                // {
                //     key: 'BalanceType',
                //     dataIndex: 'BalanceType',
                //     title: '交易类型',
                //     align: 'center',
                //     checked: true,
                //     render: (type: string) => (
                //         <ATag color={type === 'INCO' ? 'green' : type === 'CONS' ? 'blue' : 'red'}>
                //             {balanceTypeMap[type] || type}
                //         </ATag>
                //     ),
                // },
               
                {
                    key: 'TransBalance',
                    dataIndex: 'TransBalance',
                    title: '充值金额',
                    align: 'center',
                    //   width: 120,
                    checked: true,
                    sorter: (a: any, b: any) => parseFloat(a.TransBalance) - parseFloat(b.TransBalance),
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                {
                    key: 'GiftBalance',
                    dataIndex: 'GiftBalance',
                    title: '赠送金额',
                    align: 'center',
                    //   width: 120,
                    sorter: (a: any, b: any) => parseFloat(a.GiftBalance) - parseFloat(b.GiftBalance),
                    checked: true,
                    render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                },
                // {
                //     key: 'LeftBalance',
                //     dataIndex: 'LeftBalance',
                //     title: '剩余金额',
                //     align: 'center',
                //     //   width: 120,
                //     checked: true,
                //     render: (text: string) => `¥${parseFloat(text).toFixed(2)}`,
                // },
                {
                    key: 'PayType',
                    dataIndex: 'PayType',
                    title: '支付方式',
                    align: 'center',
                    checked: true,
                    render: (type: string) => (
                        <ATag color={type === 'ALIP' ? 'blue' : type === 'WECH' ? 'green' : type === 'PUBA' ? 'orange' : 'purple'}>
                            {payTypeMap[type] || type}
                        </ATag>
                    ),
                },
                {
                    key: 'Addtime',
                    dataIndex: 'Addtime',
                    title: '创建时间',
                    align: 'center',
                    //   width: 150,
                    checked: true,
                    ellipsis: {
                        showTitle: false,
                    },
                    sorter: (a: any, b: any) => new Date(a.Addtime).getTime() - new Date(b.Addtime).getTime(),
                },
                {
                    key: 'PayState',
                    dataIndex: 'PayState',
                    title: '支付状态',
                    align: 'center',
                    //   width: 100,
                    checked: true,
                    sorter: (a: any, b: any) => a.PayState - b.PayState,
                    // `PayState` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态 -1 失败 0 等待支付 1 支付成功',
                    render: (state: number, record: any) => (
                        <ATag className='cursor-pointer' onClick={() => handlePay(state, record)} color={state === -1 ? 'red' : state === 0 ? 'blue' : 'green'}>
                            {state === -1 ? '支付失败' : state === 0 ? '去支付' : '支付成功'}
                        </ATag>
                    ),
                },
            ]
        },
        { showQuickJumper: true }
    );

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData
    } = useTableOperate(data, run);


    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async () => {
        console.log(tableProps, "tableProps====")
        // console.log(params, "params====")
        // return
        // run(params);
        const formValues = form.getFieldsValue();
        console.log(formValues, "formValues====")
        const searchParams = {
            where: {
                "BalanceType":"INCO",
                ...(formValues?.PayType ? { PayType: `${formValues.PayType}` } : {}),
            },
        };
        handleRun({
            page: 1,
            pagesize: 20,
            ...searchParams,
            CountryCode: "CN"
        });
    }

    // const filteredData = () => {
    //     return tableProps.dataSource?.map((item: any, index: number) => ({
    //         ...item,
    //        ID:index
    //     }));
    // }

    // 二次支付
    const handlePay = async (state: number, record: any) => {
        if (state === 0) {
            console.log(state, record, "state,record====")
            const res = await continuePay({
                RechargeID: record.ID
            })
            if (res && res.data) {

                console.log(res.data, "res.data====")
                if (res.data.JumpUrl) {
                    setJumpUrl(res.data.JumpUrl);
                    setModalVisible(true);
                    window.open(res.data.JumpUrl, '_blank');
                }
            }
            // console.log(res, "res====")
        }
    }

    // useEffect(() => {
    //     setLoading(true);
    //     if (tableProps.dataSource.length >= 0) {
    //     const fetchData = async () => {
    //         const newData = await filteredData();
    //         // console.log(newData, "newData====");
    //         setTableData(newData);
    //         // setListingHistoryLoading(false);
    //     };
    //     fetchData();
    //     }

    // }, [tableProps.dataSource]);
    return (
        <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">

            <PaymentModal
                visible={modalVisible}
                onClose={() => {
                    setModalVisible(false)
                    reset()
                }}
                jumpUrl={jumpUrl}
                redirectToUrl={() => {
                    setModalVisible(false);
                    reset()
                }}
            />
            <ACard>

                <UserSearch
                    search={getTableData}
                    reset={reset}
                    form={form}
                    loading={tableProps.loading}
                />
            </ACard>

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={reset}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <div>
                        充值记录
                    </div>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={scrollConfig}
                    // rowSelection={rowSelection}
                    size="small"
                    {...tableProps}
                    dataSource={tableProps.dataSource}
                // loading={loading}
                // tableLayout="auto"
                //   dataSource={formattedData}
                />
            </ACard>
        </div>
    );
}
