import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Alert, Modal } from 'antd';
import { InfoCircleOutlined, RobotOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { asinOptions, getStrategyLabel, limitDecimalsP } from './until';

// 定义暴露给父组件的方法类型
export interface AcosSettingModalRef {
  open: (asins: string[]) => void;
  close: () => void;
}

interface AcosSettingModalProps {
  onSuccess: (selectedAcos: string | number) => void;
  initialAcos?: number;
  checkedRowKeys?: string[];
}

const AcosSettingModal = forwardRef<AcosSettingModalRef, AcosSettingModalProps>(
  ({ onSuccess, initialAcos = 0 }, ref) => {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [selectedAcos, setSelectedAcos] = useState<string | number>(initialAcos);
    const [customAcos, setCustomAcos] = useState<string>('');
    const selectedAcosRef = useRef<string | number>(selectedAcos);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      open: () => {
        setSelectedAcos(initialAcos);
        const isCustom = asinOptions.find((option: any) => option.value === initialAcos);
        if (typeof initialAcos === 'number' && !isCustom) {
          setSelectedAcos('custom'); // 设置为自定义选项
          setCustomAcos(String(initialAcos)); // 设置自定义ACOS值
        }
        setIsOpen(true);
      },
      close: () => {
        setIsOpen(false);
      }
    }));

    const handleOk = async () => {
      const finalAcos = selectedAcos === 'custom' ? Number(customAcos) : selectedAcos;
      onSuccess(finalAcos);
    };

    const handleCancel = () => {
      setIsOpen(false);
    };

    return (
      <Modal
        open={isOpen}
        title={
          <div className="flex items-center gap-2">
            <RobotOutlined className="text-lg text-primary" />
            <span>{t('page.ailisting.acosSettingModal.title')}</span>
          </div>
        }
        onCancel={handleCancel}
        onOk={handleOk}
        width={680}
        className="acos-setting-modal"
      >
        <div className="space-y-4">
          <ACard className="border border-blue-100 bg-gray-50">
            <div className="mb-6 flex items-center gap-2">
              <span className="whitespace-nowrap">{t('page.ailisting.acosSettingModal.targetAcosValue')}</span>
              <ASelect
                className="min-w-[200px]"
                value={selectedAcos}
                onChange={value => {
                  setSelectedAcos(value);
                  selectedAcosRef.current = value;
                }}
                options={[
                  ...asinOptions,
                  {
                    value: 'custom',
                    label: t('page.ailisting.acosSettingModal.customAcos', {
                      value:
                        getStrategyLabel(Number(selectedAcosRef.current), asinOptions, selectedAcosRef.current) || '?'
                    })
                  }
                ]}
              />

              {selectedAcos === 'custom' && (
                <div className="ml-2 flex items-center gap-2">
                  <AInputNumber
                    formatter={limitDecimalsP}
                    parser={limitDecimalsP}
                    autoComplete="off"
                    step={1}
                    min={16}
                    max={50}
                    style={{ width: 180 }}
                    addonBefore="ACOS ≤"
                    addonAfter="%"
                    value={customAcos}
                    onChange={value => {
                      setCustomAcos(String(value));
                    }}
                  />
                  <span className="text-primary font-medium">
                    {getStrategyLabel(Number(customAcos), asinOptions, selectedAcos)}
                  </span>
                </div>
              )}
            </div>

            <Alert
              message={
                <div className="text-gray-600">
                  <div className="mb-2 font-medium">{t('page.ailisting.acosSettingModal.optimizationStrategy')}</div>
                  <ul className="list-disc pl-4 space-y-1">
                    <li>{t('page.ailisting.acosSettingModal.strategyPoints.siteBased')}</li>
                    <li>{t('page.ailisting.acosSettingModal.strategyPoints.referenceIndicator')}</li>
                  </ul>
                </div>
              }
              type="info"
            />
          </ACard>

          {selectedAcos === 'custom' && (
            <div className="flex items-center gap-2 rounded-lg bg-blue-50 px-4 py-3">
              <InfoCircleOutlined className="text-blue-500" />
              <span className="text-primary">{t('page.ailisting.acosSettingModal.customRangeNotice')}</span>
            </div>
          )}
        </div>
      </Modal>
    );
  }
);

AcosSettingModal.displayName = 'AcosSettingModal';

export default AcosSettingModal;
