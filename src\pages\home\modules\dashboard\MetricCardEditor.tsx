import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, Checkbox, Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import type { MetricCard } from '@/types/dashboard';

interface MetricCardEditorProps {
  visible: boolean;
  metrics: MetricCard[];
  onClose: () => void;
  onSave: (selectedMetrics: string[]) => void;
}

const MetricCardEditor: React.FC<MetricCardEditorProps> = ({ visible, metrics, onClose, onSave }) => {
  const { t } = useTranslation();
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 当metrics变化时更新选中状态
  useEffect(() => {
    console.log(metrics, 'metrics');
    if (metrics.length > 0) {
      setSelectedMetrics(metrics.filter(m => m.selected).map(m => m.id));
    }
  }, [metrics]);

  const handleChange = (checkedValues: string[]) => {
    if (checkedValues.length > 6) {
      setError(t('page.home.dashboard.maxSelectMetrics'));
      return;
    }

    setError(null);
    setSelectedMetrics(checkedValues);
  };

  const handleSave = () => {
    if (selectedMetrics.length === 0) {
      setError(t('page.home.dashboard.minSelectMetrics'));
      return;
    }

    onSave(selectedMetrics);
    onClose();
  };

  return (
    <Modal
      title={t('page.home.dashboard.editCoreMetrics')}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button
          key="cancel"
          onClick={onClose}
        >
          {t('common.cancel')}
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
        >
          {t('common.save')}
        </Button>
      ]}
    >
      <div className="mb-2 text-gray-500">{t('page.home.dashboard.maxSelectMetrics')}</div>
      {error && (
        <Alert
          message={error}
          type="error"
          className="mb-3"
        />
      )}

      <Checkbox.Group
        value={selectedMetrics}
        onChange={values => handleChange(values as string[])}
      >
        <div className="grid grid-cols-2 gap-2">
          {metrics.map(metric => (
            <div
              key={metric.id}
              className="flex items-center"
            >
              <Checkbox
                value={metric.id}
                disabled={selectedMetrics.length >= 6 && !selectedMetrics.includes(metric.id)}
              >
                {t(`page.home.dashboard.metrics.${metric.id}.title`)}
              </Checkbox>
            </div>
          ))}
        </div>
      </Checkbox.Group>
    </Modal>
  );
};

export default MetricCardEditor;
