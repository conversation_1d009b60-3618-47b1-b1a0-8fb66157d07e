import { PricingPackages } from "./model/PricingPackages";
import AccountOverview from "./model/AccountOverview";
import { useSearchParams } from "react-router-dom";
import Coupons from "./model/Coupons";
import OrderInfo from "./model/OrderInfo";
import { AppProvider } from "./model/AppContext";
export function Component() {
  const [searchParams] = useSearchParams();

  const handlePackageSelect = (packageId: number) => {
    console.log('Selected package ID:', packageId);
    // 处理选中的套餐ID
  };

  useEffect(() => {
    const data = searchParams.get("data");
    console.log(data);
  }, []);

  return (
    <AppProvider>
    <div className="p-4 bg-white">
      <div className="flex items-center justify-between">
      <h3 className="text-base mb-4 font-500 text-lg flex items-center">
        <SvgIcon localIcon="tocan" className="w-6 h-6 mr-2" />
        服务套餐
      </h3>
      <DocumentButton
          text="广告服务激活说明"
          link="https://deepthought.feishu.cn/wiki/INYWwDGSGiMLhLkdYWMcU9Rkn8b"
      />
      </div>
      <PricingPackages onPackageSelect={handlePackageSelect} />
      {/* 账户概况 */}
      <AccountOverview />
      {/* 优惠券 */}
      <Coupons />
      {/* 订单信息 */}
      <OrderInfo />
      </div>
    </AppProvider>
  );
}