import React from 'react';
import { Card, Popover, Tooltip } from 'antd';
import { ArrowDownOutlined, ArrowUpOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { MetricCard as MetricCardType } from '@/types/dashboard';

interface MetricCardProps {
  data: MetricCardType;
}

// 指标说明内容
const getMetricDescription = (id: string): { title: string; formula: string | string[]; description: string } => {
  const descriptions: Record<string, { title: string; formula: string | string[]; description: string }> = {
    ad_spend: {
      title: 'AI 广告销售额',
      formula: 'AI 广告销售占比=(AI广告销售额 / 广告销售额)× 100%',
      description: '由AI优化的广告所产生的销售额。'
    },
    total_sales: {
      title: '总销售额',
      formula: '广告销售额+自然销售额',
      description: '包括广告和自然流量产生的所有销售额总和。'
    },
    ad_sales_ratio: {
      title: 'AI 广告费用占比',
      formula: '(AI广告花费 / 广告总花费) × 100%',
      description: 'AI广告花费占总广告花费的百分比，反映AI广告在整体广告投入中的比重。'
    },
    natural_sales: {
      title: 'AI 带动自然流量销售额',
      formula: '(AI销售额 / SP总销售额) × 自然销售额',
      description: '通过AI广告带动的自然流量销售额，根据AI广告销售额占比计算。'
    },
    sales_count: {
      title: 'AI 销售数量',
      formula: '(AI订单数 / 广告总订单数) × 广告总订单数',
      description: 'AI广告带来的销售数量，根据AI销量占比计算。'
    },
    order_count: {
      title: 'AI 订单数',
      formula: 'AI订单数',
      description: 'AI广告带来的订单数量。'
    },
    ad_savings: {
      title: 'AI 节省广告费',
      formula: [
        'AI 节省广告费 = (非AI花费 / 非AI销售额 - AI花费/AI销售额) × 100%',
        'ACOS下降 = (上期AI广告花费 - 本期AI广告花费) / 上期广告销售额',
        'AI贡献率 = (上期AI广告花费 - 本期AI广告花费) / (上期广告销售额 - 本期广告销售额)'
      ],
      description: 'AI广告与非AI广告的ACOS差异百分比，反映AI优化带来的广告费用节省。正值表示AI广告更高效。'
    },
    ai_op_count: {
      title: 'AI 运营调优次数',
      formula: '节省运营工作量 = AI运营调优次数 / 200',
      description: 'AI系统执行的运营优化操作次数，包括关键词优化、出价调整等。'
    },
    keyword_create: {
      title: 'AI 选词优化',
      formula: 'AI选词操作的累计次数',
      description: 'AI系统执行的关键词选择和优化操作次数。'
    },
    targeting_create: {
      title: 'AI 自动加ASIN',
      formula: 'AI添加ASIN的累计次数',
      description: 'AI系统自动添加ASIN定向的操作次数。'
    }
  };

  return (
    descriptions[id] || {
      title: '未知指标',
      formula: '暂无公式',
      description: '暂无描述'
    }
  );
};

const MetricCard: React.FC<MetricCardProps> = ({ data }) => {
  const { t } = useTranslation();
  const {
    id,
    title,
    value,
    prevValue,
    change,
    changeType,
    unit,
    hideChange,
    thirdLineTitle,
    thirdLineValue,
    thirdLineUnit
  } = data;

  // 获取指标的国际化说明
  const getMetricInfo = (metricId: string) => {
    return {
      title: t(`page.home.dashboard.metrics.${metricId}.title`),
      description: t(`page.home.dashboard.metrics.${metricId}.description`),
      formula: t(`page.home.dashboard.metrics.${metricId}.formula`, { returnObjects: true })
    };
  };

  const metricInfo = getMetricInfo(id);

  const renderChangeIndicator = () => {
    if (hideChange || !change) return null;

    const isPositive = changeType === 'increase';
    const isNegative = changeType === 'decrease';

    return (
      <span
        className={`ml-2 pt-1 flex items-center ${isPositive ? 'text-primary' : isNegative ? 'text-error' : 'text-gray-500'}`}
      >
        {isPositive && <ArrowUpOutlined />}
        {isNegative && <ArrowDownOutlined />}
        {change}
      </span>
    );
  };

  // 指标说明弹出框内容
  const popoverContent = (
    <div style={{ maxWidth: '400px' }}>
      {/* <h4 style={{ marginTop: 0 }}>{metricInfo.title}</h4> */}
      <div style={{ marginBottom: '8px' }}>
        {Array.isArray(metricInfo.formula) ? (
          // 如果是数组，则循环渲染多个公式
          <div>
            {metricInfo.formula.map((formula, index) => (
              <div
                key={index}
                style={{
                  background: '#f5f5f5',
                  padding: '6px 8px',
                  borderRadius: '4px',
                  marginTop: '4px',
                  fontFamily: 'monospace',
                  marginBottom: index < metricInfo.formula.length - 1 ? '8px' : '0'
                }}
              >
                {formula}
              </div>
            ))}
          </div>
        ) : (
          // 如果是单个字符串，则直接渲染
          <div
            style={{
              background: '#f5f5f5',
              padding: '6px 8px',
              borderRadius: '4px',
              marginTop: '4px',
              fontFamily: 'monospace'
            }}
          >
            {metricInfo.formula}
          </div>
        )}
      </div>
      <div>
        <strong>{t('page.home.dashboard.description')}：</strong>
        <p style={{ marginTop: '4px', marginBottom: 0 }}>{metricInfo.description}</p>
      </div>
    </div>
  );

  return (
    <Card className="h-full shadow-sm transition-shadow hover:shadow-md">
      <div className="h-full flex flex-col">
        <div className="mb-1 flex items-center text-sm text-gray-500">
          {/* 指标标题 */}
          {t(`page.home.dashboard.metrics.${id}.title`)}
          <Popover
            content={popoverContent}
            title={null}
            placement="top"
            trigger="hover"
          >
            <InfoCircleOutlined
              className="ml-1 cursor-help text-gray-400"
              style={{ fontSize: '14px' }}
            />
          </Popover>
        </div>
        <div className="flex items-center">
          <div>
            <AStatistic
              className="old_value"
              title="" // 旧值标题
              value={value} // 旧值
              // precision={thirdLineUnit?.length > 1 ? 0 : 2} // 精度
              valueStyle={{ color: '#333', fontSize: '20px', fontWeight: '600', whiteSpace: 'nowrap' }} // 旧值样式
              prefix={unit && unit !== '%' ? unit : ''} // 前缀
              suffix={unit === '%' ? '%' : ''} // 后缀
            />
          </div>
          {renderChangeIndicator()}
        </div>
        {thirdLineTitle ? (
          <div className="mt-1 flex items-center text-sm text-gray-500">
            {/*  */}
            {id === 'ad_savings'
              ? thirdLineTitle.map((item: any, index: number) => (
                  <div key={index}>
                    {t(`page.home.dashboard.metrics.${id}.thirdLine${index + 1}`)}
                    {item.value}
                  </div>
                ))
              : t(`page.home.dashboard.metrics.${id}.thirdLine`)}

            {thirdLineValue && (
              <AStatistic
                className="old_value"
                title="" // 旧值标题
                value={thirdLineValue} // 旧值
                // precision={thirdLineUnit?.length > 1 ? 0 : 2} // 精度
                valueStyle={{
                  color: '#6e6f71',
                  fontSize: '15px',
                  marginRight: '5px',
                  fontWeight: '400',
                  whiteSpace: 'nowrap'
                }} // 旧值样式
                prefix={thirdLineUnit !== '%' && thirdLineUnit?.length <= 1 ? thirdLineUnit : ''} // 前缀
                suffix={thirdLineUnit == '%' || thirdLineUnit?.length > 1 ? thirdLineUnit || '' : ''} // 后缀
              />
            )}
            {/* {thirdLineValue} */}
          </div>
        ) : (
          <div className="mt-1 flex items-center text-sm text-gray-500">
            {t('page.home.dashboard.previousPeriod')}:
            {prevValue && (
              <AStatistic
                className="old_value"
                title="" // 旧值标题
                value={prevValue} // 旧值
                // precision={thirdLineUnit?.length > 1 ? 0 : 2} // 精度
                valueStyle={{
                  color: '#6e6f71',
                  fontSize: '15px',
                  marginRight: '5px',
                  fontWeight: '400',
                  whiteSpace: 'nowrap'
                }} // 旧值样式
                prefix={unit && unit !== '%' ? unit : ''} // 前缀
                suffix={unit === '%' ? '%' : ''} // 后缀
              />
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default MetricCard;
