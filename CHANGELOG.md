# Changelog


## [v1.0.0](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.2...v1.0.0) (2024-10-06)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**:
  - @sa/hooks add useRequest &nbsp;-&nbsp; by **wang** [<samp>(e5cdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e5cdcc9)
  - hooks: add use-array & example &nbsp;-&nbsp; by **wang** [<samp>(02483)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/02483b5)
- **projects**:
  - add menu functions &nbsp;-&nbsp; by **wang** [<samp>(0b14d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0b14deb)
  - support add parent when add route &nbsp;-&nbsp; by **wang** [<samp>(084bf)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/084bf89)
  - support dynamic add route & optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6f3ad)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6f3adca)
  - add before guard &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(13b0c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/13b0cab)
  - @sa/axios: add response to flatRequest when success &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(92e3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/92e3cec)
  - does the configuration support automatic updates. &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fb758)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fb7583a)
  - add details page to show loader for data router &nbsp;-&nbsp; by **wang** [<samp>(7928b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7928bd6)
  - the topic configuration replication function was added &nbsp;-&nbsp; by **wang** [<samp>(e3d7a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e3d7a99)
  - add animation &nbsp;-&nbsp; by **wang** [<samp>(ea5d7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ea5d7c6)
  - add useMeta &nbsp;-&nbsp; by **wang** [<samp>(d0c6a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d0c6a37)
  - add keep-alive &nbsp;-&nbsp; by **wang** [<samp>(ed7e7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ed7e793)
  - login supports accessible operation &nbsp;-&nbsp; by **wang** [<samp>(d2dae)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d2dae2d)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - eix tab  can't click on  mobile side &nbsp;-&nbsp; by **wang** [<samp>(e0141)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e01410a)
  - support pass state and fix judgments before jumpe &nbsp;-&nbsp; by **wang** [<samp>(34935)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3493583)
  - fix useRouter type &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(32628)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/32628df)
  - failure to return in some fast new cases results in no initialization . close #8 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/8 [<samp>(cfe46)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cfe46ea)
- **projects**:
  - fix top menu abnormal &nbsp;-&nbsp; by **wang** [<samp>(5e1f7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5e1f789)
  - fixed abnormal display of dynamic switching size menu &nbsp;-&nbsp; by **wang** [<samp>(79c1a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/79c1ae1)
  - fix eslint errors &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fec80)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fec80a1)
  - click tab left menu openkeys does not change &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f3f57)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f3f570b)
  - fix route type & remove startTransition &nbsp;-&nbsp; by **wang** [<samp>(fac36)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fac368b)
  - Fixed redirection when switching roles & init tab no cache &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(58d1f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/58d1feb)
  - fix refresh token when meet multi requests &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fbe7d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fbe7ddb)
  - in big screen has scroll bar &nbsp;-&nbsp; by **wang** [<samp>(cb942)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cb94245)
  - fix after  menu expansion submenu not open &nbsp;-&nbsp; by **wang** [<samp>(c96c9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c96c964)
  - fix shrink mess in mixed mode &nbsp;-&nbsp; by **wang** [<samp>(0c6fb)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0c6fba6)
  - fix global-tab click conflict with contextmenu &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a32f5)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a32f507)
  - Fixed submenu opening when shrinking &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(77f2b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/77f2b6a)
  - fixed the sidebar automatically expanding when switching between mobile and pc &nbsp;-&nbsp; by **wang** [<samp>(0abdd)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0abdd0c)
  - failed to switch sidebar language &nbsp;-&nbsp; by **wang** [<samp>(75307)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/753079f)
  - the reproduction environment can cache the theme configuration &nbsp;-&nbsp; by **wang** [<samp>(50932)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/50932b7)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- **packages**:
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(5f78e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5f78e52)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(26f05)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/26f0579)
  - simple-router: optimize code &nbsp;-&nbsp; by **wang** [<samp>(27d5f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/27d5f7e)
- **projects**:
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(85b64)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/85b6483)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(21d28)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/21d28b0)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(b29bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b29bceb)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(f6fd4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f6fd4f8)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(43f8b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/43f8b45)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(a5cc9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a5cc93a)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **packages**:
  - update Route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(8795b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8795b2f)
  - @sa/hooks: use-request fir axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(3dbe7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3dbe701)
  - @sa/simple-router: stable useRoute &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6cf09)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6cf09f9)
  - @sa/materials: remove tab close shortcut by mouse &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(edb3e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/edb3e69)
- **projects**:
  - add logout route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(df689)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/df689df)
  - refactor simple-router &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d7861)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d78613c)
  - combine theme tokens and theme settings &nbsp;-&nbsp; by **wang** [<samp>(8d703)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8d703d9)
  - remove dark sidebar configuration &nbsp;-&nbsp; by **wang** [<samp>(f9582)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f958280)
  - Modify card's global style search table to use Collapse &nbsp;-&nbsp; by **wang** [<samp>(17c2e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/17c2ed9)
  - change antd's colorBgContainer &nbsp;-&nbsp; by **wang** [<samp>(0b65c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0b65cea)
  - change sidebar animation &nbsp;-&nbsp; by **wang** [<samp>(f9297)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f92972e)

### &nbsp;&nbsp;&nbsp;📖 Documentation

- **projects**: update CHANGELOG &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a13a7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a13a70d)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(1dad4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1dad4f0)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6ff15)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6ff150b)
  - remove transition-group-plus &nbsp;-&nbsp; by **wang** [<samp>(36996)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3699680)
  - add framer-motion & remove react-transition-group &nbsp;-&nbsp; by **wang** [<samp>(abada)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/abada7f)
- **projects**:
  - update CHANGELOG &nbsp;-&nbsp; by **wang** [<samp>(264f9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/264f9c6)
  - update scss config &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(480c8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/480c869)
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(0d855)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0d855e7)
- **types**:
  - remove type declaration for document.startViewTransition (TypeScript 5.6 inclnudes it) &nbsp;-&nbsp; by **wang** [<samp>(cef47)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cef474d)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v1.0.0-beta.1](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.2...v1.0.0-beta.1) (2024-09-14)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**:
  - @sa/hooks add useRequest &nbsp;-&nbsp; by **wang** [<samp>(e5cdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e5cdcc9)
- **projects**:
  - add menu functions &nbsp;-&nbsp; by **wang** [<samp>(0b14d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0b14deb)
  - support add parent when add route &nbsp;-&nbsp; by **wang** [<samp>(084bf)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/084bf89)
  - support dynamic add route & optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6f3ad)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6f3adca)
  - add before guard &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(13b0c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/13b0cab)
  - @sa/axios: add response to flatRequest when success &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(92e3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/92e3cec)
  - does the configuration support automatic updates. &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fb758)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fb7583a)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - eix tab  can't click on  mobile side &nbsp;-&nbsp; by **wang** [<samp>(e0141)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e01410a)
  - support pass state and fix judgments before jumpe &nbsp;-&nbsp; by **wang** [<samp>(34935)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3493583)
  - fix useRouter type &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(32628)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/32628df)
  - failure to return in some fast new cases results in no initialization . close #8 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/8 [<samp>(cfe46)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cfe46ea)
- **projects**:
  - fix top menu abnormal &nbsp;-&nbsp; by **wang** [<samp>(5e1f7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5e1f789)
  - fixed abnormal display of dynamic switching size menu &nbsp;-&nbsp; by **wang** [<samp>(79c1a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/79c1ae1)
  - fix eslint errors &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fec80)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fec80a1)
  - click tab left menu openkeys does not change &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f3f57)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f3f570b)
  - fix route type & remove startTransition &nbsp;-&nbsp; by **wang** [<samp>(fac36)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fac368b)
  - Fixed redirection when switching roles & init tab no cache &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(58d1f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/58d1feb)
  - fix refresh token when meet multi requests &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(fbe7d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/fbe7ddb)
  - in big screen has scroll bar &nbsp;-&nbsp; by **wang** [<samp>(cb942)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cb94245)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- **packages**:
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(5f78e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5f78e52)
- **projects**:
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(85b64)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/85b6483)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(21d28)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/21d28b0)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(b29bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b29bceb)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **packages**:
  - update Route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(8795b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8795b2f)
  - @sa/hooks: use-request fir axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(3dbe7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3dbe701)
  - @sa/simple-router: stable useRoute &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6cf09)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6cf09f9)
- **projects**:
  - add logout route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(df689)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/df689df)
  - refactor simple-router &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d7861)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d78613c)
  - combine theme tokens and theme settings &nbsp;-&nbsp; by **wang** [<samp>(8d703)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8d703d9)
  - remove dark sidebar configuration &nbsp;-&nbsp; by **wang** [<samp>(f9582)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f958280)

### &nbsp;&nbsp;&nbsp;📖 Documentation

- **projects**: update CHANGELOG &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a13a7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a13a70d)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(1dad4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1dad4f0)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6ff15)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6ff150b)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[DESKTOP-31IBRMI\Administrator](mailto:<EMAIL>)

## [v0.3.2](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.0...v0.3.2) (2024-09-07)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **deps**:
  - add typewriter &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0651b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0651b64)
- **packages**:
  - add remove route & If the route to be jumped is the current route, no jump occurs &nbsp;-&nbsp; by **wang** [<samp>(a75c2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a75c25e)
  - Synchronize the useRouterPush of soybean &nbsp;-&nbsp; by **wang** [<samp>(04577)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/04577cd)
- **proejects**:
  - routing and layout error boundaries combine &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7de3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7de3c59)
- **projects**:
  - change document address &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7ced8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7ced849)
  - synchronize updates to soybean's axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6401f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6401f0b)
  - add common internationalization configuration &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a702d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a702d5c)
  - the routing information file can be created on the command line &nbsp;-&nbsp; by **wang** [<samp>(6e77e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6e77edc)
  - update elegant-route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d4a65)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d4a655f)
  - iframe add skeleton &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e21af)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e21afb8)
  - add components auto-import &nbsp;-&nbsp; by **wang** [<samp>(b60f2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b60f2ea)
  - add antd auto-import & close route log info &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(5879f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5879fd1)
  - add full screen watermarke &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(103b6)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/103b643)
  - optimize code style &  exception page add button click  event &nbsp;-&nbsp; by **wang** [<samp>(f1dcd)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f1dcd68)
  - Synchronize the useRouterPush of soybean &nbsp;-&nbsp; by **wang** [<samp>(308df)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/308dfdd)
  - add version update notifications &nbsp;-&nbsp; by **wang** [<samp>(3f25b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/3f25b19)
  - use json5 resolve env VITE_OTHER_SERVICE_BASE_URL & fix proxy enable & updated international language &nbsp;-&nbsp; by **wang** [<samp>(15769)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1576922)
  - add color fading mode &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(10b2a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/10b2a65)
- **prxojects**:
  - change useRouter declaration type &nbsp;-&nbsp; by **wang** [<samp>(f27d4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f27d4c3)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - can jump to params & not-found &nbsp;-&nbsp; by **wang** [<samp>(6241a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6241a81)
- **projects**:
  - fixed tables not showing scrollbars when screen width changes . close #5 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/5 [<samp>(bc673)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/bc67301)
  - repair tabcannot be modified &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0d3ca)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0d3cae0)
  - update redirect &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(dc18c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dc18c38)
  - fixed  left menu mixed mode shrinks  menu &nbsp;-&nbsp; by **wang** [<samp>(77dac)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/77dacbd)
- **types**:
  - fix the type of TableApiFn &nbsp;-&nbsp; by **wang** [<samp>(b8daf)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b8daf7c)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(00bdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/00bdccb)
- **packages**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(4f6a2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/4f6a2e1)
- **projects**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a9854)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a98549a)
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(aa3bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/aa3bc09)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(b407e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b407ec4)
  - optimize code &nbsp;-&nbsp; by **wang** [<samp>(6ce4f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6ce4f26)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(8f9a8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8f9a86c)
  - optimize code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0402b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0402b46)

### &nbsp;&nbsp;&nbsp;💅 Refactors

- **packages**:
  - Refactoring useRoute &nbsp;-&nbsp; by **wang** [<samp>(b9b55)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b9b55d3)
- **projects**:
  - change css vars mount to root &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(c59ed)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c59edf6)
  - refactor part of the menu code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d19aa)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d19aa0b)
  - the vercal-mix reconstruction is complete &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(dab53)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dab5333)
  - refactor the menu section code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1f1ef)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1f1efbb)
  - Refactoring useMenu &nbsp;-&nbsp; by **wang** [<samp>(185ff)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/185ff72)
  - refactor global menu & support reversed-horizontal-mix-menu &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(132fa)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/132fa6f)

### &nbsp;&nbsp;&nbsp;📦 Build

- **deps**: add deps vite-plugin-checker &nbsp;-&nbsp; by **wang** [<samp>(24454)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2445424)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **README**:
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e9ebe)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e9ebeb2)
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a084d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a084d21)
- **deps**:
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f63e7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f63e71e)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(ce564)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ce564ce)
  - remove lodash-es &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d487c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d487c9b)
  - add simple-git-hooks lint-staged &nbsp;-&nbsp; by **wang** [<samp>(9c977)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/9c977fd)
- **other**:
  - print project information in console &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d9cf8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d9cf87a)
- **projects**:
  - add vscode configuration & add vite preload &nbsp;-&nbsp; by **wang** [<samp>(48cd0)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/48cd07e)

### &nbsp;&nbsp;&nbsp;🎨 Styles

- **proejcts**:
  - optimized code format &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e2c03)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e2c0391)
- **projects**:
  - optimized code style &nbsp;-&nbsp; by **wang** [<samp>(f5fed)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f5fed34)
  - change component classification &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e41a9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e41a925)
  - optimize code style &nbsp;-&nbsp; by **wang** [<samp>(0eded)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0eded8d)
  - optimize code style &nbsp;-&nbsp; by **wang** [<samp>(95243)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/95243a0)
  - optimized code &nbsp;-&nbsp; by **wang** [<samp>(28d68)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/28d68fc)
  - reduce the padding of header buttoon &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1a343)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1a34371)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v0.3.1](https://github.com/mufeng889/react-soybean-admin/compare/v0.3.0...v0.3.1) (2024-08-31)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **proejects**:
  - routing and layout error boundaries combine &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7de3c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7de3c59)
- **projects**:
  - change document address &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(7ced8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7ced849)
  - synchronize updates to soybean's axios &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6401f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6401f0b)
  - add common internationalization configuration &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a702d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a702d5c)
  - the routing information file can be created on the command line &nbsp;-&nbsp; by **wang** [<samp>(6e77e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6e77edc)
  - update elegant-route &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d4a65)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d4a655f)
  - iframe add skeleton &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e21af)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e21afb8)
  - add components auto-import &nbsp;-&nbsp; by **wang** [<samp>(b60f2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/b60f2ea)
  - add antd auto-import & close route log info &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(5879f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5879fd1)
  - add full screen watermarke &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(103b6)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/103b643)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - can jump to params & not-found &nbsp;-&nbsp; by **wang** [<samp>(6241a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6241a81)
- **projects**:
  - fixed tables not showing scrollbars when screen width changes . close #5 &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** in https://github.com/mufeng889/react-soybean-admin/issues/5 [<samp>(bc673)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/bc67301)
  - repair tabcannot be modified &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(0d3ca)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/0d3cae0)
  - update redirect &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(dc18c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/dc18c38)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(00bdc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/00bdccb)
- **packages**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(4f6a2)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/4f6a2e1)
- **projects**:
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a9854)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a98549a)
  - optimized code &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(aa3bc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/aa3bc09)

### &nbsp;&nbsp;&nbsp;📦 Build

- **deps**: add deps vite-plugin-checker &nbsp;-&nbsp; by **wang** [<samp>(24454)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2445424)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **README**:
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e9ebe)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e9ebeb2)
  - update README &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(a084d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a084d21)
- **deps**:
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(f63e7)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f63e71e)
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(ce564)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/ce564ce)
  - remove lodash-es &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d487c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d487c9b)
- **projects**:
  - add vscode configuration & add vite preload &nbsp;-&nbsp; by **wang** [<samp>(48cd0)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/48cd07e)

### &nbsp;&nbsp;&nbsp;🎨 Styles

- **proejcts**:
  - optimized code format &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e2c03)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e2c0391)
- **projects**:
  - optimized code style &nbsp;-&nbsp; by **wang** [<samp>(f5fed)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f5fed34)
  - change component classification &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(e41a9)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/e41a925)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[DESKTOP-31IBRMI\Administrator](mailto:<EMAIL>)

## [v0.3.0](https://github.com/mufeng889/react-soybean-admin/compare/v0.2.1...v0.3.0) (2024-08-20)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **packages**: perfect routing method &nbsp;-&nbsp; by **wang** [<samp>(7a15b)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/7a15b1b)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **packages**:
  - fix repeat routing &nbsp;-&nbsp; by **wang** [<samp>(98a71)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/98a7117)
- **projects**:
  - prevent local storage conflicts &nbsp;-&nbsp; by @Azir-11 [<samp>(cc2d4)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/cc2d470)
  - fix Fix type error type error  . close #3 &nbsp;-&nbsp; by **wang** in https://github.com/mufeng889/react-soybean-admin/issues/3 [<samp>(d9237)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d923752)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code &nbsp;-&nbsp; by **wang** [<samp>(f94b5)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/f94b58b)
- **code**:
  - optimize the experience &nbsp;-&nbsp; by **wang** [<samp>(6a135)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6a1354a)
- **packages**:
  - optimize simple-router code &nbsp;-&nbsp; by **wang** [<samp>(c6ea8)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c6ea8b0)
  - optimized  simple-router code &nbsp;-&nbsp; by **wang** [<samp>(edfb1)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/edfb1f6)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **package**: improve package content . close #4 &nbsp;-&nbsp; by **wang** in https://github.com/mufeng889/react-soybean-admin/issues/4 [<samp>(a2dde)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/a2dde59)

### &nbsp;&nbsp;&nbsp;❤️ Contributors

[![Azir-11](https://github.com/Azir-11.png?size=48)](https://github.com/Azir-11)&nbsp;&nbsp;
[wang](mailto:<EMAIL>),&nbsp;

## [v0.2.1](https://github.com/mufeng889/react-soybean-admin/compare/v0.2.0...v0.2.1) (2024-08-14)

### &nbsp;&nbsp;&nbsp;🐞 Bug Fixes

- **proejects**: use other methods to replace toSorted for better compatibility with more browsers &nbsp;-&nbsp; by **wang** [<samp>(5bc13)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5bc13d6)
- **projects**: fixed tab not switching colors when switching color themes &nbsp;-&nbsp; by **wang** [<samp>(5afba)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/5afba5e)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**: update deps &nbsp;-&nbsp; by **wang** [<samp>(95d38)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/95d38fe)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

## [v0.2.0](https://github.com/mufeng889/react-soybean-admin/compare/undefined...v0.2.0) (2024-08-08)

### &nbsp;&nbsp;&nbsp;🚀 Features

- **.github**:
  - add  workflows &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(d3e8c)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/d3e8c65)
- **.npmr**:
  - cancel Taobao mirror address &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1f90f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1f90f11)
- **pachages**:
  - add parse query &  stringify query &nbsp;-&nbsp; by **wang** [<samp>(c0134)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/c0134c7)
- **packages**:
  - add route Transition animation &nbsp;-&nbsp; by **wang** [<samp>(da94e)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/da94e7c)
- **projects**:
  - init project &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(651d3)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/651d3bb)
  - add configuration &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(6c976)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6c976ba)
  - remove restrictions on locked files &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(9138d)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/9138d77)
  - perfect user interface &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(1cc8a)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1cc8a60)
  - add il18 for error boundary component &nbsp;-&nbsp; by **wang** [<samp>(8d6cc)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8d6cc79)

### &nbsp;&nbsp;&nbsp;🛠 Optimizations

- optimized code & remove useless code &nbsp;-&nbsp; by **wang** [<samp>(2ac61)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/2ac611f)
- **projects**: place the error boundary under the root route & add the internationalized language of the login page &nbsp;-&nbsp; by **wang** [<samp>(1e4a1)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/1e4a109)

### &nbsp;&nbsp;&nbsp;🏡 Chore

- **deps**:
  - update deps &nbsp;-&nbsp; by **DESKTOP-31IBRMI\Administrator** [<samp>(beea6)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/beea6ad)
  - add deps vite-plugin-remove-console & optimize code style &nbsp;-&nbsp; by **wang** [<samp>(6387f)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/6387f23)
  - update deps &nbsp;-&nbsp; by **wang** [<samp>(965df)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/965df84)

### &nbsp;&nbsp;&nbsp;🤖 CI

- **vercel**: add vercel profile &nbsp;-&nbsp; by **wang** [<samp>(8b275)</samp>](https://github.com/mufeng889/react-soybean-admin/commit/8b27596)

### &nbsp;&nbsp;&nbsp;❤️ Contributors


[wang](mailto:<EMAIL>)

