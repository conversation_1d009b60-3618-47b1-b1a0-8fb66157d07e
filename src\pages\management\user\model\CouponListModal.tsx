import React, { forwardRef, useImperativeHandle, useState, useEffect, useRef, useCallback } from 'react';
import { CouponList } from '@/service/api';
import { LoadingOutlined } from '@ant-design/icons';
import CouponCard from '@/pages/management/buy-auth/model/CouponCard'

const CouponListModal = forwardRef<CouponListModalRef, CouponListModalProps>((props, ref) => {
    const [visible, setVisible] = useState(false);
    const [couponList, setCouponList] = useState([]);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const loader = useRef<HTMLDivElement | null>(null);
    const couponListRef = useRef<HTMLDivElement | null>(null);
    const nav = useNavigate();
    const [observer, setObserver] = useState<IntersectionObserver | null>(null);
    useImperativeHandle(ref, () => ({
        open: () => setVisible(true),
        close: () => close(),
    }));

    const close = () => {
        setVisible(false)
        // couponListRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
        setPage(1);
        setHasMore(true);
        setCouponList([]);
        if (loader.current && observer) {
            observer.disconnect(); // 取消观察
        }
    }

    const loadCoupons = useCallback(() => {
        if (loading || !hasMore) return;
        setLoading(true);
        CouponList({
            page,
            pagesize: 10
        }).then((res) => {
            if (res && Array.isArray(res.data.data)) {
                setCouponList(prevList => [...prevList, ...res.data.data]);
                setPage(prevPage => prevPage + 1);
                if (res.data.data.length < 10) {
                    setHasMore(false);
                }
            }
        }).finally(() => {
            setLoading(false);
        });
    }, [page, loading, hasMore]);

    useEffect(() => {
        if (visible && page === 1) {
            loadCoupons(); // 初始加载第一页
        }
    }, [visible, loadCoupons, page]);

    useEffect(() => {
        let currentObserver: IntersectionObserver | null = null;
        
        if (visible) {
            currentObserver = new IntersectionObserver((entries) => {
                if (entries[0].isIntersecting) {
                    loadCoupons();
                }
            }, { threshold: 0.5 });

            if (loader.current) {
                currentObserver.observe(loader.current);
            }
            
            setObserver(currentObserver);
        }

        return () => {
            if (currentObserver) {
                currentObserver.disconnect();
            }
        };
    }, [loadCoupons, visible]);

    return (
        <AModal
            title="优惠券"
            open={visible}
            onCancel={() => close()}
            footer={null}
            centered
            maskClosable={false}
        >
            <div className="flex flex-col" ref={couponListRef} style={{ maxHeight: '580px', overflowY: 'auto' }}>
                {couponList.map((coupon: any, index: number) => (
                    <div key={coupon.ID}>
                    <CouponCard
                        key={coupon.ID}
                        data={{
                            id: coupon.ID,
                            couponDescription: coupon.CouponName,
                            validDate: coupon.ExpireDatetime,
                            CommonDenominator: parseFloat(coupon.CommonDenominator),
                            number: 1,
                            amount: parseFloat(coupon.CouponAmount),
                            unit: '元',
                            Used: coupon.Used,
                            couponLeftTimes: coupon.CouponLeftTimes,
                            couponInfo: coupon.CouponInfo
                        }}
                        valid={false}
                        onSelect={() => {
                            if(coupon.Used == 0){
                                // 跳转到优惠券兑换页面
                                close();
                                nav(`/management/auth`);
                            }
                           
                        }}
                    />
                    </div>
                ))}
                <div ref={loader} style={{ height: '50px', textAlign: 'center' }}>
                    {loading && <div> <LoadingOutlined className='mr-2' />加载中...</div>}
                    {!hasMore && <div>没有更多优惠券了</div>}
                </div>
            </div>
        </AModal>
    );
});

export default CouponListModal;