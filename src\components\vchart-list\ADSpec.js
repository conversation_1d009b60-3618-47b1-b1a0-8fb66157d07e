const spec = [

    {
        title: "Impression",
        value: "",
        type: "",
        num: "",
        data: {
            type: 'area',
            data: {
                values: [
                ]
            },
            xField: 'time',
            yField: 'value',
            seriesField: 'country',
            stack: false,
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        // min: 0,
                        // max: 16
                    }
                },
                {
                    orient: 'bottom',
                    visible: false,
                    trimPadding: true
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // text: 'Impression',
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title: "Clicks",
        value: "",
        // rise
        // decline
        type: "",
        num: "",
        data: {
            type: 'area',
            stack: false,
            data: {
                values: [
                ]
            },
            xField: 'time',
            yField: 'value',
            seriesField: 'country',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    // range: {
                    //     min: 0,
                    //     max: 20
                    // }
                },
                {
                    orient: 'bottom',
                    visible: false,
                    trimPadding: true
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        "title": "CTR",
        "value": "",  // 平均CTR
        "type": "",
        "num": "",  // 从2024-06-17到2024-06-18的变化
        "data": {
            "type": "area",
            "stack": false,
            "data": {
                "values": [
                ]
            },
            xField: 'time',
            yField: 'value',
            seriesField: 'country',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        min: 0,
                        // max: 1
                    }
                },
                {
                    orient: 'bottom',
                    visible: false,
                    trimPadding: true
                }
            ],
            point: {
                visible: false,
                style: {
                    fill: 'red'
                }
            },
            tooltip: {
                dimension: {
                    content: [
                        {
                            key: datum => datum['country'],
                            value: datum => datum['value'] + '%'
                        }
                    ]
                }
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title: "Order",
        value: "",  // Total number of orders
        type: "",
        num: "",  // Percentage increase from first to last day
        data: {
            type: 'area',
            stack: false,
            data: {
                values: [
                ]
            },
            xField: 'time',
            yField: 'value',
            seriesField: 'country',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    // range: {
                    //     min: 0,
                    //     max: 20
                    // }
                },
                {
                    orient: 'bottom',
                    visible: false,
                    trimPadding: true
                }
            ],
            point: {
                visible: false
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title: "Conversion Rate",
        value: "",  // 最后一天的转化率
        type: "",
        num: "",  // 从2024-06-12到2024-06-18的变化
        data: {
            type: 'area',
            stack: false,
            data: {
                values: [
                ]
            },
            xField: 'time',
            yField: 'value',
            seriesField: 'country',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                },
                {
                    orient: 'bottom',
                    visible: false,
                    trimPadding: true
                }
            ],
            point: {
                visible: false
            },
            tooltip: {
                dimension: {
                    content: [
                        {
                            key: datum => datum['country'],
                            value: datum => datum['value'] + '%'
                        }
                    ]
                }
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        "title": "ACOS",
        "value": "",  // 平均ACOS
        "type": "",
        "num": "",  // 从2024-06-17到2024-06-18的变化
        "data": {
            "type": "area",
            "stack": false,
            "data": {
                "values": [
                    // { "time": "2024-06-12", "value": 31.35 },
                    // { "time": "2024-06-13", "value": 36.20 },
                    // { "time": "2024-06-14", "value": 18.90 },
                    // { "time": "2024-06-15", "value": 16.90 },
                    // { "time": "2024-06-16", "value": 21.10 },
                    // { "time": "2024-06-17", "value": 23.80 },
                    // { "time": "2024-06-18", "value": 28.90 }
                ]
            },
            xField: 'time',
            yField: 'value',
            seriesField: 'country',
            axes: [
                {
                    orient: 'left',
                    visible: false,
                    range: {
                        // min: 0,
                        // max: 30
                    }
                },
                {
                    orient: 'bottom',
                    visible: false,
                    trimPadding: true
                }
            ],
            point: {
                visible: false
            },
            tooltip: {
                visible: true,
                dimension: {
                    content: [
                        { key: datum => datum['country'], value: datum => datum.value + '%' }
                    ]
                }
            },
            area: {
                style: {
                    fill: {
                        gradient: 'linear',
                        x0: 0.5,
                        y0: 0,
                        x1: 0.5,
                        y1: 1,
                        stops: [
                            { offset: 0, opacity: 1 },
                            { offset: 1, opacity: 0.3 }
                        ]
                    }
                }
            },
            title: {
                // Additional properties can be uncommented or added here
            }
        }
    },
    {
        title: "Ads type",
        value: "",
        type: "",
        num: "",
        data: {
            type: 'pie',
            data: [
                {
                    id: 'id0',
                    values: [
                        //   { type: 'SP', value: '83.93' },
                        //   { type: 'SD', value: '6.68' },
                        //   { type: 'SB', value: '9.39' }
                    ]
                }
            ],
            indicator: {
                visible: true,
                trigger: 'hover',
                limitRatio: 0.4,
                title: {
                    visible: true,
                    style: {
                        fontWeight: 'bolder',
                        fontFamily: 'Times New Roman',
                        text: data => {
                            return 'Sales';
                        }
                    }
                },
                // content: [
                //     {
                //         visible: true,
                //         style: {
                //             fontSize: 20,
                //             fill: 'orange',
                //             fontWeight: 'bolder',
                //             fontFamily: 'Times New Roman',
                //             text: data => {
                //                 if (data) {
                //                     return data['type'];
                //                 }
                //             }
                //         }
                //     },
                //     {
                //         visible: true,
                //         style: {
                //             fontSize: 18,
                //             fill: 'orange',
                //             fontFamily: 'Times New Roman',
                //             text: data => {
                //                 // 求
                //                 if (data) {
                //                     return data['sales'];
                //                 }
                //                 return "";
                //             }
                //         }
                //     }
                // ]
            },
            outerRadius: 0.8,
            innerRadius: 0.5,
            padAngle: 0.6,
            valueField: 'value',
            categoryField: 'type',
            pie: {
                style: {
                    // cornerRadius: 10
                },
                state: {
                    hover: {
                        outerRadius: 0.85,
                        lineWidth: 1
                    },
                    selected: {
                        outerRadius: 0.85,
                        stroke: '#000',
                        lineWidth: 1
                    }
                }
            },
            legends: {
                visible: true,
                orient: 'right'
            },
            label: {
                visible: false
            },
            color: ['#154EC1', '#85A5FF', '#D6E4FF'],
            tooltip: {
                mark: {
                    title: {
                        visible: false,
                        text: 'Sales'
                    },
                    content: [
                        {
                            key: datum => datum['type'],
                            value: datum => datum['value'] + '%'
                        },
                        {
                            key: "Sales",
                            value: datum => datum['sales']
                        }
                    ]
                }
            }
        }
    },

]

export default spec;

