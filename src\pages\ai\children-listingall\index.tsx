import { Suspense, lazy } from 'react';
import { Tooltip } from 'antd';
import type { DropdownProps, MenuProps } from 'antd';
import TableHeaderOperation from '@/components/advanced/TableHeaderOperation';
const UserSearch = lazy(() => import('./modules/UserSearch'))
import { getListingParentData } from '@/service/api';
import { selectUserInfo } from '@/store/slice/auth';
const ParentAsinHeader = lazy(() => import('./modules/ParentAsinHeader'));
import BigNumber from "bignumber.js";
const tagMap: Record<Api.Common.EnableStatus, string> = {
    1: 'success',
    2: 'warning'
};

const tagUserGenderMap: Record<Api.SystemManage.UserGender, string> = {
    1: 'processing',
    2: 'error'
};

export function Component() {
    const { t } = useTranslation();

    const { tableWrapperRef, scrollConfig } = useTableScroll();

    const nav = useNavigate();

    const userInfo = useAppSelector(selectUserInfo);

    const [searchParams] = useSearchParams();


    const market = searchParams.get('market');

    const asin = searchParams.get('asin');

    const { columnChecks, data, reset, form, setColumnChecks, tableProps, run, handleRun } = useTable(
        {
            apiFn: getListingParentData,
            apiParams: {

            },
            immediate: false,
            columns: () => [
                {
                    key: 'asin',
                    dataIndex: 'asin',
                    title: '子ASIN',
                    align: 'center',
                    width: 350,
                    fixed: 'left',
                    checked: true,
                    render: (_, record) => {
                        let son_storage = [];
                        let filteredData = tableProps.dataSource[tableProps.dataSource.length - 1][asin] || {};
                        if (Object.keys(filteredData).length > 0) {
                            const storageValue = filteredData.son_storage;
                            son_storage = storageValue === "None" ? [] : JSON.parse(storageValue);
                        }
                        // console.log(son_storage, "son_storage")
                        let son_asin_info =   son_storage.find((item: any) => item.ASIN == _)
                        return (
                            <div className='flex items-stretch h-full gap-2'>
                                   <LazyImage
                                        src={record.first_image}
                                        alt={record.item_name || "--"}
                                        className='max-h-[100px] object-contain min-w-[80px]'
                                        width={100}
                                    />
                                <div className='flex flex-col justify-between w-full'>
                                    <Tooltip title={record.item_name || "--"}>
                                        <div className='line-clamp-2 text-left' >
                                            {record.item_name || "--"}
                                        </div>
                                    </Tooltip>
                                    <div className='text-start flex flex-nowrap'>
                                    <span className='text-gray-400 mr-1 whitespace-nowrap'>价格:</span>
                                    <div className='flex flex-nowrap'>
                                            {record.min_price === record.max_price ?

                                                <CurrencySymbol countryCode={market} value={record.min_price} />
                                                :
                                                <>
                                                    <CurrencySymbol countryCode={market} value={record.min_price} />~<CurrencySymbol countryCode={market} value={record.max_price} />
                                                </>
                                            }
                                        </div>
                                        <span className='mx-1'> | </span>
                                        <span className='whitespace-nowrap'>
                                            <span className='text-gray-400 mr-1'>{asin !==_ &&son_asin_info?.FBM === 1 ? <span className='text-red-500'>自配送</span> : ''}库存:</span>
                                            { son_asin_info?.HaveNum || 0} 
                                        </span>
                                    </div>
                                    <div className='text-start'>
                                        ASIN:  
                                        <a 
                                          className='text-primary' 
                                          target="_blank" 
                                          rel="noopener noreferrer"
                                          onClick={(event) => {
                                            event.stopPropagation();
                                            if(_ && _ !== '-'){
                                                window.open(`https://www.${getSalesChannel(market)}/dp/${_}`, '_blank');
                                            }
                                          }} 
                                          >
                                            {_ || "-"}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        );
                    }
                },
                {
                    key: '总销售额',
                    title: "总销售额",
                    align: 'center',
                    dataIndex: '总销售额',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.总销售额 - b.总销售额;
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} />;
                    }
                },
                {
                    key: '广告总销售额',
                    dataIndex: '广告总销售额',
                    title: "广告销售额",
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.广告总销售额 - b.广告总销售额;
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} />;
                    }
                },
                {
                    key: 'ACOS',
                    dataIndex: 'ACOS',
                    title: "ACOS",
                    align: 'center',
                    width: 80,
                    sorter: (a: any, b: any) => {
                        return a.ACOS.replace("%", "") - b.ACOS.replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.广告总花费 || 0);
                        const sales = new BigNumber(record.广告总销售额 || 0);
                        const acos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={acos} />;
                    }
                },
                {
                    key: '自然销售比例',
                    dataIndex: '自然销售比例',
                    title: "自然销售占比",
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.自然销售比例.replace("%", "") - b.自然销售比例.replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} />;
                    }
                },
                {
                    key: 'TACOS',
                    dataIndex: 'TACOS',
                    title: "TACOS",
                    align: 'center',
                    width: 90,
                    sorter: (a: any, b: any) => {
                        return a.TACOS.replace("%", "") - b.TACOS.replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        const spend = new BigNumber(record.广告总花费 || 0);
                        const sales = new BigNumber(record.总销售额 || 0);
                        const tacos = spend.dividedBy(sales).multipliedBy(100).toFixed(2) + "%";
                        return <CurrencySymbol countryCode={market} value={tacos} />;
                    }
                },
                {
                    key: "SP广告销售额占比",
                    dataIndex: "SP广告销售额占比",
                    title: "SP广告销售额占比",
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a["SP广告销售额占比"].replace("%", "") - b["SP广告销售额占比"].replace("%", "");
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol countryCode={market} value={text} />;
                    }
                },
                {
                    key: '总订单量',
                    dataIndex: '总订单量',
                    title: "总订单量",
                    align: 'center',
                    width: 100,
                    sorter: (a: any, b: any) => {
                        return a.总订单量 - b.总订单量;
                    },
                    checked: true,
                    render: (text: string, record: any) => {
                        return <CurrencySymbol isInteger={true} value={parseInt(record.总订单量 || 0).toFixed(0)} />;
                    }
                }
            ]
        },
        { showQuickJumper: true }
    );
    const [tableData, setTableData] = useState<any[]>(tableProps.dataSource);

    const {
        checkedRowKeys,
        rowSelection,
        onBatchDeleted,
        onDeleted,
        handleEdit,
        handleAdd,
        drawerVisible,
        closeDrawer,
        operateType,
        editingData,
        clearCheckedRowKeys
    } = useTableOperate(data, run);

    async function handleBatchDelete() {
        // request
        console.log(checkedRowKeys);
        onBatchDeleted();
    }

    const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
        if (info.source === 'trigger' || nextOpen) {
            setOpen(nextOpen);
        }
    };

    function handleDelete(id: number) {
        // request
        console.log(id);

        onDeleted();
    }

    function edit(id: number) {
        handleEdit(id);
    }
    const getTableData = async (params: any = {}, isLocalSearch: boolean = false) => {
        clearCheckedRowKeys()
        if (isLocalSearch) {
            console.log(params, "本地搜索")
            // handledataSource(params);
            const newData = handledataSource(params);
            // 使用 setTableData 更新表格数据
            setTableData(newData);
        } else {
            if (searchParams.get('market') && searchParams.get('asin')) {
                const query = {
                    UID: userInfo.active_shop_id,
                    CountryCode: searchParams.get('market'),
                    StartDate: form.getFieldValue('dateRange') ? form.getFieldValue('dateRange')[0].format('YYYY-MM-DD') : undefined,
                    EndDate: form.getFieldValue('dateRange') ? form.getFieldValue('dateRange')[1].format('YYYY-MM-DD') : undefined,
                    ParentAsin: searchParams.get('asin'),
                };
                console.log(query, "query")
                // return
                run(query);
            }
        }
    }

    // 处理数据 加入汇总行
    // 广告活动名称 叫汇总
    // 预算为空
    // 状态为空
    // 花费加和 点击加和 销售额加和 订单加和
    // acos 需要根据加合后的花费和销售额计算
    const handledataSource = (params: any = {}) => {
        // console.log(tableProps.dataSource, "tableProps.dataSource")
        // 去除最后一行
        let filteredData = [...tableProps.dataSource].slice(0, -1);

        if (!filteredData.length) return [];

        // 本地搜索过滤
        if (params.ASIN) {
            console.log(params.ASIN, "params.ASIN");
            filteredData = filteredData.filter((item: any) => {
                return item.asin.includes(params.ASIN);
            });
        }

        return filteredData;
    }

    useEffect(() => {

        let newData = handledataSource({});
        // 筛除  newData的asin  等于 asin
        newData = newData.filter((item: any) => item.asin !== asin);
        // 使用 setTableData 更新表格数据
        setTableData(newData);
    }, [tableProps.dataSource])

    return (
        <div
            className="min-h-500px  max-h-2000px"
        >
            {/* <ACollapse
        bordered={false}
        className="card-wrapper"
        defaultActiveKey={['1']}
        items={[
          {
            key: '1',
            label: t('common.search'),
            children: (
              
            )
          }
        ]}
      /> */}

            <ParentAsinHeader dataSource={tableProps.dataSource} searchParams={searchParams} />
            <ACard className='my-2'>

                <UserSearch
                    search={getTableData}
                    localSearch={getTableData}
                    form={form}
                    loading={tableProps.loading}
                />
            </ACard>

            <ACard
                ref={tableWrapperRef}
                bordered={false}
                extra={
                    <TableHeaderOperation
                        onDelete={handleBatchDelete}
                        refresh={() => {
                            getTableData({})
                        }}
                        add={handleAdd}
                        loading={tableProps.loading}
                        setColumnChecks={setColumnChecks}
                        disabledDelete={checkedRowKeys.length === 0}
                        columns={columnChecks}
                    />
                }
                title={
                    <>
                        {
                            // tableProps.dataSource.length > 0 &&
                            <span>子ASIN数量：{tableData.length}</span>
                        }
                    </>
                }
                className="flex-col-stretch sm:flex-1-hidden card-wrapper"
            >
                <ATable
                    scroll={{
                        y: 700,
                        x: 702
                    }}
                    size="small"
                    {...tableProps}
                    dataSource={tableData}
                    showSizeChanger={false}
                    rowClassName={(record, index) => {
                        // Highlight the last row if it's the total row
                        // if (tableData.length > 0) {
                        //     const isLastRow = index === tableData.length -1;
                        //     return isLastRow ? 'lastRowHigh' : '';
                        // }
                        // return '';
                    }}
                    locale={{ emptyText: <AEmpty
                        image={AEmpty.PRESENTED_IMAGE_SIMPLE}
                        description={
                            !tableProps.loading ? (
                                <div className='flex-col items-center'>
                                    <AButton type='primary' className='my-2' onClick={reset}>
                                        刷新页面
                                    </AButton>
                                </div>
                            ) : null
                    }></AEmpty> }}
                />
            </ACard>
        </div>
    );
}
