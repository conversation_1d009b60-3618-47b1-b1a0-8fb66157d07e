import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Modal, Form, Input, Button } from 'antd';
import CaptchaQRCode from '@/pages/login/captch-qr-code';
interface ChangeNameModalProps {
  onSubmit: (values: any,captchaRef:any) => void;
}

const CouponRedemptionModel = forwardRef(({ onSubmit }: ChangeNameModalProps, ref) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const captchaRef = useRef<any>(null);
  const [captchaKey, setCaptchaKey] = useState('');
  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false),
  }));
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
    //   console.log(values);
    //   return
      onSubmit({...values,captchaKey},captchaRef);
      // setVisible(false);
    } catch (error) {
      console.error('Validation Failed:', error);
    }
  };

  return (
    <Modal
      centered
      maskClosable={false}
      title="兑换优惠券"
      open={visible}
      onCancel={() => setVisible(false)}
      footer={[
        <Button key="back" onClick={() => setVisible(false)}>
        取消
      </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          兑换
        </Button>,
     
      ]}
    >
      <Form form={form} layout="vertical" className='w-full py-2'>
        <Form.Item
          label="兑换码"
          name="Code"
          rules={[{ required: true, message: '请输入兑换码' }]}
        >
          <Input placeholder="请输入兑换码" />
        </Form.Item>
        <CaptchaQRCode
          ref={captchaRef}
          label={true}
          onCaptchaChange={(value, key) => {
            setCaptchaKey(key);
          }}
        />
      </Form>
    </Modal>
  );
});

export default CouponRedemptionModel;